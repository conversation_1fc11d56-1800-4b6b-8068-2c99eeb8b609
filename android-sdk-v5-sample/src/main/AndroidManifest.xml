<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    package="dji.sampleV5.aircraft">
    <!-- Sample permission requirement -->
    <uses-permission
        android:name="android.permission.READ_EXTERNAL_STORAGE"
        android:maxSdkVersion="32" />
    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />
    <uses-permission android:name="android.permission.READ_MEDIA_AUDIO" />
    <uses-permission android:name="android.permission.RECORD_AUDIO" />
    <uses-permission android:name="android.permission.READ_MEDIA_VISUAL_USER_SELECTED" />
    <uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
    <uses-permission android:name="android.permission.KILL_BACKGROUND_PROCESSES" />
    <uses-permission android:name="android.permission.BLUETOOTH" />
    <uses-permission android:name="android.permission.BLUETOOTH_ADMIN" />
    <uses-permission android:name="android.permission.VIBRATE" />
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <uses-permission android:name="android.permission.CHANGE_WIFI_STATE" />
    <uses-permission android:name="android.permission.RECORD_AUDIO" />
    <uses-permission android:name="android.permission.REQUEST_INSTALL_PACKAGES" />
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
    <uses-permission android:name="android.permission.QUERY_ALL_PACKAGES"
        tools:ignore="QueryAllPackagesPermission" /><!-- Google Maps -->
    <uses-feature
        android:name="android.hardware.usb.host"
        android:required="false" />
    <uses-feature
        android:name="android.hardware.usb.accessory"
        android:required="true" />

    <application
        android:name=".DJIAircraftApplication"
        android:allowBackup="true"
        android:allowNativeHeapPointerTagging="false"
        android:icon="@mipmap/skysys_yg_app_icon"
        android:label="@string/my_app_name"
        android:networkSecurityConfig="@xml/network_security_config"
        android:requestLegacyExternalStorage="true"
        android:supportsRtl="true"
        android:theme="@android:style/Theme.NoTitleBar.Fullscreen"
        tools:replace="android:label">

        <!-- MSDK -->
        <meta-data
            android:name="com.dji.sdk.API_KEY"
            android:value="${API_KEY}" /> <!-- AMaps -->
        <meta-data
            android:name="com.amap.api.v2.apikey"
            android:value="${AMAP_API_KEY}" />

        <service
            android:name="com.amap.api.location.APSService"
            android:exported="false" />
        <!--
<activity
            android:name="dji.sampleV5.aircraft.DJIDemo.DJIAircraftMainActivity"
            android:theme="@style/full_screen_theme"
            android:screenOrientation="sensorLandscape"
            android:configChanges="orientation|screenSize|keyboardHidden"
            android:exported="true">

            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
        -->
        <activity
            android:name=".HomeActivity"
            android:configChanges="orientation|screenSize|keyboardHidden"
            android:exported="true"
            android:screenOrientation="sensorLandscape"
            android:theme="@style/full_screen_theme">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <action android:name="android.hardware.usb.action.USB_ACCESSORY_ATTACHED" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>

            <meta-data
                android:name="android.hardware.usb.action.USB_ACCESSORY_ATTACHED"
                android:resource="@xml/accessory_filter" />
        </activity>
        <activity
            android:name=".page.RecentMissionDetailActivity"
            android:exported="false"
            android:screenOrientation="sensorLandscape"
            android:theme="@style/AppTheme" />
        <activity
            android:name=".page.SiteListActivity"
            android:exported="false"
            android:screenOrientation="sensorLandscape"
            android:theme="@style/full_screen_theme" />
        <activity
            android:name=".page.operate.OperateCenterActivity"
            android:exported="false"
            android:screenOrientation="sensorLandscape"
            android:theme="@style/full_screen_theme" />
        <activity
            android:name=".page.picture.PictureActivity"
            android:exported="false"
            android:screenOrientation="sensorLandscape"
            android:theme="@style/AppTheme" />
        <activity
            android:name=".DefaultLayoutActivity"
            android:exported="false"
            android:screenOrientation="sensorLandscape"
            android:theme="@style/full_screen_theme" />
        <activity
            android:name=".TestActivity"
            android:exported="false"
            android:screenOrientation="sensorLandscape"
            android:theme="@style/full_screen_theme" />
        <activity
            android:name=".page.web.WebViewActivity"
            android:exported="false"
            android:screenOrientation="sensorLandscape" />
        <activity
            android:name=".page.web.CameraActivity"
            android:exported="false"
            android:screenOrientation="sensorLandscape"
            android:theme="@style/AppTheme" />
        <activity
            android:name="com.amap.api.maps.offlinemap.OfflineMapActivity"
            android:exported="false"
            android:screenOrientation="sensorLandscape" />
        <activity
            android:name=".page.login.LoginActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:exported="false"
            android:screenOrientation="sensorLandscape"
            android:theme="@style/AppTheme"
            android:windowSoftInputMode="adjustPan|stateHidden" />
        <activity
            android:name=".page.login.LoginMixActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:exported="false"
            android:screenOrientation="sensorLandscape"
            android:theme="@style/AppTheme"
            android:windowSoftInputMode="adjustPan|stateHidden" />
        <activity
            android:name=".page.fly.Task.TaskHistoryListActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:exported="false"
            android:screenOrientation="sensorLandscape"
            android:theme="@style/full_screen_theme"
            android:windowSoftInputMode="adjustPan|stateHidden" />
        <activity
            android:name=".page.ai.AiReportActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:exported="false"
            android:screenOrientation="sensorLandscape"
            android:theme="@style/AppTheme"
            android:windowSoftInputMode="adjustPan|stateHidden" />
        <activity
            android:name=".page.ai.PdfViewerActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:exported="false"
            android:screenOrientation="fullSensor"
            android:theme="@style/AppTheme"
            android:windowSoftInputMode="adjustPan|stateHidden" />
        <activity
            android:name=".page.fly.Task.TaskHistoryInfoActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:exported="false"
            android:screenOrientation="sensorLandscape"
            android:theme="@style/AppTheme"
            android:windowSoftInputMode="adjustPan|stateHidden" />
        <activity
            android:name=".page.FindAircraftActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:exported="false"
            android:screenOrientation="sensorLandscape"
            android:theme="@style/full_screen_theme"
            android:windowSoftInputMode="adjustPan|stateHidden" />
        <!--
  <activity
            android:name=".DJIDemo.UsbAttachActivity"
            android:exported="true"
            android:theme="@style/translucent_theme">
            <intent-filter>
                <action android:name="android.hardware.usb.action.USB_ACCESSORY_ATTACHED" />
            </intent-filter>

            <meta-data
                android:name="android.hardware.usb.action.USB_ACCESSORY_ATTACHED"
                android:resource="@xml/accessory_filter" />
        </activity>
        -->
        <activity
            android:name="dji.v5.ux.sample.showcase.defaultlayout.DefaultLayoutActivity"
            android:configChanges="orientation|screenSize|keyboardHidden"
            android:exported="false"
            android:screenOrientation="sensorLandscape"
            android:theme="@style/full_screen_theme" />
        <activity
            android:name="dji.v5.ux.sample.showcase.widgetlist.WidgetsActivity"
            android:configChanges="orientation|screenSize|keyboardHidden"
            android:exported="false"
            android:screenOrientation="sensorLandscape"
            android:theme="@style/full_screen_theme" />
        <!--
<activity
            android:name=".DJIDemo.AircraftTestingToolsActivity"
            android:configChanges="orientation|screenSize|keyboardHidden"
            android:exported="false"
            android:screenOrientation="sensorLandscape"
            android:theme="@style/full_screen_theme" />
        -->
        <activity
            android:name=".mvvm.ui.activity.realtime.RealTimeActivity"
            android:configChanges="orientation|screenSize|keyboardHidden"
            android:exported="false"
            android:screenOrientation="sensorLandscape"
            android:theme="@style/full_screen_theme" />

        <receiver
            android:name="com.yc.video.old.other.NetChangedReceiver"
            android:exported="false" />

        <service android:name=".mvvm.update.service.DownloadService" />

        <provider
            android:name=".mvvm.update.config.AppUpdateFileProvider"
            android:authorities="${applicationId}.fileProvider"
            android:exported="false"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/app_update_file" />
        </provider>

        <activity
            android:name=".mvvm.update.view.UpdateDialogActivity"
            android:configChanges="orientation|screenSize|keyboardHidden"
            android:theme="@style/AppUpdate.UpdateDialog" />
        <activity
            android:name=".mvvm.ui.activity.defect.DefectActivity"
            android:configChanges="orientation|screenSize|keyboardHidden"
            android:exported="false"
            android:screenOrientation="sensorLandscape"
            android:theme="@style/full_screen_theme" />
        <activity
            android:name=".mvvm.ui.activity.report.NewReportActivity"
            android:configChanges="orientation|screenSize|keyboardHidden"
            android:exported="false"
            android:screenOrientation="sensorLandscape"
            android:theme="@style/full_screen_theme" />
        <activity
            android:name=".mvvm.ui.activity.multisite.MultisiteActivity"
            android:exported="false"
            android:screenOrientation="sensorLandscape"
            android:theme="@style/full_screen_theme" />
        <activity
            android:name=".mvvm.ui.activity.history.HistoryResultActivity"
            android:exported="false"
            android:screenOrientation="sensorLandscape"
            android:theme="@style/full_screen_theme" />
        <activity
            android:name=".mvvm.ui.activity.history.HistoryVideoPlayerActivity"
            android:exported="false"
            android:screenOrientation="sensorLandscape"
            android:theme="@style/full_screen_theme" />
        <activity
            android:name=".mvvm.ui.activity.history.HistoryListActivity"
            android:exported="false"
            android:screenOrientation="sensorLandscape"
            android:theme="@style/full_screen_theme" />
        <activity
            android:name=".mvvm.ui.activity.site.RegisterStationActivity"
            android:exported="false"
            android:screenOrientation="sensorLandscape"
            android:theme="@style/full_screen_theme"
            android:windowSoftInputMode="adjustResize|stateHidden" />
        <activity
            android:name=".mvvm.ui.activity.navi.AMapNaviActivity"
            android:configChanges="orientation|keyboardHidden|screenSize|navigation"
            android:launchMode="singleInstance"
            android:screenOrientation="sensorLandscape" />
        <activity
            android:name=".mvvm.ui.activity.log.LogManagerActivity"
            android:exported="false"
            android:screenOrientation="sensorLandscape"
            android:theme="@style/full_screen_theme" />
        <receiver
            android:name="com.yc.video.old.other.NetChangedReceiver"
            android:exported="false" />

        <meta-data
            android:name="design_width_in_dp"
            android:value="1024" />
        <meta-data
            android:name="design_height_in_dp"
            android:value="768" /> <!-- 适配华为（huawei）刘海屏 -->
        <meta-data
            android:name="android.notch_support"
            android:value="true" /> <!-- 适配小米（xiaomi）刘海屏 -->
        <meta-data
            android:name="notch.config"
            android:value="portrait|landscape" /> <!-- 告知 XXPermissions 当前项目已经适配了分区存储特性 -->
        <meta-data
            android:name="ScopedStorage"
            android:value="true" />
    </application>

</manifest>