package dji.sampleV5.aircraft.util;

import android.util.Log;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

import dji.sampleV5.aircraft.mvvm.util.XLogUtil;

public class FileUtil {

    /**
     * 压缩文件和文件夹
     *
     * @param srcFileString 要压缩的文件或文件夹
     * @param zipFileString 压缩完成的Zip路径
     * @throws Exception
     */

    public static void ZipFolder(String srcFileString, String zipFileString) throws Exception {
        //创建ZIP
        ZipOutputStream outZip = new ZipOutputStream(new FileOutputStream(zipFileString));
        //创建文件
        File file = new File(srcFileString);
        //压缩
        ZipFiles(file.getParent() + File.separator, file.getName(), outZip);
        //完成和关闭
        outZip.finish();
        outZip.close();
    }

    /**
     * 压缩文件
     *
     * @param folderString
     * @param fileString
     * @param zipOutputSteam
     * @throws Exception
     */

    private static void ZipFiles(String folderString, String fileString, ZipOutputStream zipOutputSteam) throws Exception {
        if (zipOutputSteam == null)
            return;

        File file = new File(folderString + fileString);
        if (file.isFile()) {
            ZipEntry zipEntry = new ZipEntry(fileString);
            FileInputStream inputStream = new FileInputStream(file);
            zipOutputSteam.putNextEntry(zipEntry);
            int len;
            byte[] buffer = new byte[4096];
            while ((len = inputStream.read(buffer)) != -1) {
                zipOutputSteam.write(buffer, 0, len);
            }
            zipOutputSteam.closeEntry();

        } else {
            //文件夹
            String fileList[] = file.list();
            //没有子文件和压缩
            if (fileList.length <= 0) {
                ZipEntry zipEntry = new ZipEntry(fileString + File.separator);
                zipOutputSteam.putNextEntry(zipEntry);
                zipOutputSteam.closeEntry();
            }

            //子文件和递归
            for (int i = 0; i < fileList.length; i++) {
                Log.e("ZipFiles", "ZipFiles: "+folderString + fileString + "/");
                ZipFiles(folderString + fileString + "/", fileList[i], zipOutputSteam);
            }
        }

    }

    public static void toZip(String srcFileString, String zipFileString){
        FileOutputStream fos1=null;
        ZipOutputStream zip=null;
        try{
            fos1 = new FileOutputStream(new File(zipFileString));
            zip= new ZipOutputStream(fos1);
            File f = new File(srcFileString);
            compress(f, zip, f.getName());
            zip.flush();
            zip.close();
            fos1.close();
        } catch (Exception e) {
            try {
                if(zip!=null)zip.close();
                if(fos1!=null)fos1.close();
            } catch (IOException ee) {}

        }
    }
    public static void compress(File sourceFile, ZipOutputStream zos, String name) throws Exception {
        int  BUFFER_SIZE = 2 * 1024;
        byte[] buf = new byte[BUFFER_SIZE];
        if (sourceFile.isFile()) {
            // 向zip输出流中添加一个zip实体，构造器中name为zip实体的文件的名字
            zos.putNextEntry(new ZipEntry(name));
            // copy文件到zip输出流中
            int len;
            FileInputStream in = new FileInputStream(sourceFile);
            while ((len = in.read(buf)) != -1) {
                zos.write(buf, 0, len);
            }
            // Complete the entry
            zos.closeEntry();
            in.close();
        } else {
            File[] listFiles = sourceFile.listFiles();
            if (listFiles == null || listFiles.length == 0) {
                // 空文件夹的处理
                zos.putNextEntry(new ZipEntry(name + "/"));
                // 没有文件，不需要文件的copy
                zos.closeEntry();

            } else {
                for (File file : listFiles) {
                    compress(file, zos, name + "/" + file.getName());
                }
            }
        }
    }

    public static boolean deleteFile(File file) {
        try {
            if (file.exists()) {
                boolean isDeleted = file.delete();
                if (isDeleted) {
                    XLogUtil.INSTANCE.d("FileUtil", "文件" + file.getName() + " 已删除");
                } else {
                    XLogUtil.INSTANCE.d("FileUtil", "文件" + file.getName() + "删除失败");
                }
                return isDeleted;
            } else {
                return false;
            }
        } catch (Exception e) {
            XLogUtil.INSTANCE.e("FileUtil", "文件" + file.getName() + "删除失败:" + e.getMessage());
            return false;
        }
    }

}
