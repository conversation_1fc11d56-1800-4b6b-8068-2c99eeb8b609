package dji.sampleV5.aircraft.util;

import android.os.Handler;
import android.os.Looper;

import androidx.annotation.NonNull;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

import dji.sampleV5.aircraft.mvvm.net.repository.UserRepository;
import kotlin.coroutines.Continuation;
import kotlin.coroutines.CoroutineContext;
import kotlin.coroutines.EmptyCoroutineContext;

public class CoroutineUtils {

    private static final ExecutorService executor = Executors.newCachedThreadPool();
    private static final Handler mainHandler = new Handler(Looper.getMainLooper());

    /**
     * Java中调用Kotlin挂起函数的回调接口
     * @param <T> 返回值类型
     */
    public interface ResultCallback<T> {
        void onSuccess(T result);
        void onError(Exception e);
    }

    /**
     * 在Java中调用isBreakpointResume挂起函数的包装方法
     * @param missionId 任务ID
     * @param isBreakpoint 是否断点续飞
     * @param callback 回调接口
     */
    public static void callIsBreakpointResume(String missionId, boolean isBreakpoint, ResultCallback<String> callback) {
        executor.execute(() -> {
            try {
                // 创建Continuation对象
                Continuation<String> continuation = new Continuation<String>() {
                    @NonNull
                    @Override
                    public CoroutineContext getContext() {
                        return EmptyCoroutineContext.INSTANCE;
                    }

                    @Override
                    public void resumeWith(@NonNull Object result) {
                        // 在这里处理结果
                        if (result instanceof Throwable) {
                            // 错误情况
                            mainHandler.post(() -> callback.onError(new Exception((Throwable) result)));
                        } else {
                            // 成功情况
                            mainHandler.post(() -> callback.onSuccess((String) result));
                        }
                    }
                };

                // 调用挂起函数
                UserRepository.INSTANCE.isBreakpointResume(missionId, isBreakpoint)
                        .await(continuation);
            } catch (Exception e) {
                mainHandler.post(() -> callback.onError(e));
            }
        });
    }
}