package dji.sampleV5.aircraft.enums


/**
 * Copyright (C), 2015-2025
 * FileName: FinishAction
 * Author: 80945
 * Date: 2025/1/23 9:51
 * Description:
 */

enum class FinishAction(val code: Int, val value: String) {
    NO_ACTION(0, "noAction"),
    GO_HOME(1, "goHome"),
    AUTO_LAND(2, "autoLand"),
    GO_FIRST_WAYPOINT(3, "gotoFirstWaypoint"),
    UNKNOWN(-1, "goHome");
    companion object {
        fun getByCode(code: Int): FinishAction {
            return FinishAction.values().find { it.code == code } ?: UNKNOWN
        }

        fun getValue(value: String): FinishAction {
            return FinishAction.values().find { it.value == value } ?: UNKNOWN
        }
    }
}