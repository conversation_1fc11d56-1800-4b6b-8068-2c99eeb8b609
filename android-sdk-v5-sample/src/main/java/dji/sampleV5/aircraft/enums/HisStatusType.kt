package dji.sampleV5.aircraft.enums

/**
 * Copyright (C), 2015-2024
 * FileName: HisStatusType
 * Author: 80945
 * Date: 2024/12/30 9:57
 * Description: 飞行历史状态
 */
enum class HisStatusType(val code: Int, val value: String) {
    DAIMING(1, "待命"),
    QIFEIZHUNBEI(2, "起飞准备"),
    ZHIFEIZHONG(3, "执飞中"),
    JIANGLUOWANCHENGING(4, "降落完成中"),
    JIANGLUOWANCHENG(5, "降落完成"),
    UNKONW(-1, "异常");

    companion object {
        fun getByCode(code: Int): HisStatusType {
            return HisStatusType.values().find { it.code == code } ?: UNKONW
        }
    }
}