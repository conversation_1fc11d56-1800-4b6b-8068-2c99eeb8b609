package dji.sampleV5.aircraft.enums;

public enum LAUNCH_FLOW_STATE {

    NONE(0, "NONE", 0),
    MISSION_CHECK(5, "任务检查", 5),
    MISSION_LOAD(10, "任务装载",10),
    GARAGE_OPEN(15, "机库开启",15),
    CHECK_GS_SWITCH(20, "检查挡位切换",17),
    CHECK_ESTOP(25, "检查是否急停",19),
    CHECK_GS_ONLINE(30, "检查遥控器",20),
    CHECK_UAV_ONLINE(35, "检查无人机",22),
    CHECK_FLIGHTCONTROLLER(40, "检查飞控",24),
    CHECK_SDCARD(45, "检查SD卡设备",26),
    CHECK_SUBASSEMBLY(46, "检查组件连接",27),
    CHECK_TEMPERATURE(50, "检查温度",28),
    CHECK_FLIGHT_SETTING(55, "检查飞行设置",33),
    CHECK_LAUNCH_POT(60, "检查起飞点",40),
    CHECK_COMPASS(65, "检查指南针",42),
    CHECK_COLLISIONAVOIDANCE(70, "设置飞行避障",46),
    CHECK_ACTIVEOBSTACLE_AVOIDANCEENABLED(71, "获得主动避障状态", 47),
    CHECK_RTKSETTING(73, "设置飞行避障",48),
    CHECK_SMARTRETURN(75, "设置智能返航", 49),
    CHECK_VISIONASSISTEDPROSITIONING(80, "设置视觉辅助定位", 50),
    CHECK_ROLLPITCHCONTROLMODE(85,"设置俯仰控制模式", 55),
    CHECK_VERTICALCONTROLMODE(90, "设置垂直控制模式", 57),
    CHECK_YAWCONTROLMODE(95,"设置偏航控制模式", 59),
    CHECK_FLIGHTCOORDINATESYSTEM(100,"设置水平轴参照", 61),


    CHECK_GIMBAL_SIMULTANEOUS_FOLLOW(101, "设置云台同步", 62),
    CHECK_RTKENADBLE(102,"开启rtk", 63),
    CHECK_RTKENADBLING(103,"rtk启动中", 64),
    CHECK_DISRTKENADBL(104,"关闭rtk", 65),
    CHECK_GPS_LEVEL(105, "检查GPS信号", 70),
    CHECK_SATELLITE_NUM(110, "检查卫星数量", 75),
    CHECK_UPDOWN_SIGNAL(115, "检查上下行信号", 80),
    CHECK_OSDK(120, "检查OSDK状态", 85),
    CHECK_CANNOT_TAKEOFF(125, "起飞状态检查", 87),
    CHECK_GIMBAL(130, "云台状态检测", 89),
    QUERY_CAMERA_INFO(135, "获取云台参数",93),
    GARAGE_CLAW(140, "松开卡爪",97),

    ;

    private LAUNCH_FLOW_STATE(int code, String name, int progress){
        this.code = code;
        this.name = name;
        this.progress = progress;
    }

    private int code;
    private String name;
    private int progress;

    public int getCode() {return code;}
    public void setCode(int code) {this.code = code;}
    public String getName() {return name;}
    public void setName(String name) {this.name = name;}
    public int getProgress() {return progress;}
    public void setProgress(int progress) {this.progress = progress;}

    public LaunchState toClass(){
        return new LaunchState(code, name, progress);
    }

    public static class LaunchState{
        private int code;
        private String name;
        private int progress;
        LaunchState(int code, String name, int progress){
            this.code = code;
            this.name = name;
            this.progress = progress;
        }

        public int getCode() {
            return code;
        }

        public void setCode(int code) {
            this.code = code;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public int getProgress() {
            return progress;
        }

        public void setProgress(int progress) {
            this.progress = progress;
        }
    }

}
