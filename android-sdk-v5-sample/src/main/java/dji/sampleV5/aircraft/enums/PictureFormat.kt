package dji.sampleV5.aircraft.enums

import dji.sampleV5.aircraft.enums.TaskLostAction.UNKNOWN

/**
 * Copyright (C), 2015-2025
 * FileName: PictureFormat
 * Author: 80945
 * Date: 2025/1/23 9:38
 * Description: 存储照片格式枚举
 */
enum class PictureFormat(val code: Int, val format: String) {
    VISABLE(1, "visable"),
    WIDE(2, "wide"),
    ZOOM(3, "zoom"),
    IR(4, "ir"),
    UNKNOWN(-1, "unknown");

    companion object {
        fun getByCode(code: Int): PictureFormat {
            return PictureFormat.values().find { it.code == code } ?: ZOOM
        }
        fun getValue(value: String): PictureFormat {
            return PictureFormat.values().find { it.format == value } ?: ZOOM
        }
    }
}