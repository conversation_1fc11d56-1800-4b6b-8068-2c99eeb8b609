package dji.sampleV5.aircraft.enums;

public enum MISSION_CHECK_CODE {

    OK(200, "正常", false),
    UNDEFINED_TYPE(201, "未知任务类型", false),
    NO_CONTROLLER(203, "遥控器不在线或开启失败", false),
    NOT_FLYABLE_STATE(205, "状态不可飞行", false),
    NOLANDPOT(207, "未获得起飞点坐标", false),
    NOMISSION(209, "任务内容为空", false),
    NO_SDCARD(210, "SD卡未插入", false),
    NOHOTPOINT(211, "HotPoint 任务内容为空", false),
    MISSION_IS_RUNNING(217, "任务正在执行", false),
    FAILED_OPENGARAGE(219, "机库打开失败", false),
    FAILED_GIMBAL_OVER_RAND(220, "云台俯仰角超过正常范围", false),
    FAILED_YAW_OVER_RAND(221, "云台偏航角超过正常范围", false),
    FAILED_NO_HOMEPOT(225, "未获得返航点坐标", false),

    FAILED_MISSION_TYPE_ABNORMAL(226, "任务类型异常", false),
    FAILED_MISSION_DISTANCE_TOO_LONG(227, "任务距离太远", false),
    FAILED_MISSION_DISTANCE_TOO_CLOSE(228, "任务距离太近", false),
    FAILED_MISSION_COSTTIME_TOO_LONG(229, "任务时间太大", false),
    FAILED_MISSION_COSTTIME_TOO_CLOSE(231, "任务时间太小", false),
    FAILED_MISSION_TEMPERATURE(233, "电池温度太高", false),
    FAILED_NO_ENOUGH_BATTERY(235, "电池电量不足", false),
    FAILED_WAYPOINT_DISTANCE_TOO_CLOSE(237, "WayPoint 陆点距离太近", false),
    FAILED_HOTPOINT_DISTANCE_TOO_CLOSE(239, "HotPoint 陆点距离太近", false),
    FAILED_GOTO_DISTANCE_TOO_CLOSE(241, "HotPoint 陆点距离太近", false),
    FAILED_HAVE_NO_NECESSARY_BATTERY(243, "电池电量不足以完成任务", false),
    FAILED_NO_RTLS_SPOT_OR_HIVEID(245, "RTLS 返航点坐标未设置或格式错误", false),
    FAILED_NOT_ENOUGH_WAYPOINT_NUM(247, "WayPoint任务陆点数量太少", false),
    FAILED_NO_GOTO_LANDPOT(249, "GOTO 未设置陆点坐标", false),
    FAILED_NO_MAPPATH(250, "扫图任务 未设置陆点坐标", false),
    FAILED_MAPPATH_NUM_OVER(251, "扫图任务 陆点坐标数量异常", false),
    FAILED_NO_POINTPATH(252, "全景拍照 未设置陆点坐标", false),
    FAILED_POINTPATH_NUM_OVER(253, "全景拍照 陆点坐标数量异常", false),
    FAILED_FLIGHTMODE_ERROR(254,"挡位不是P档", false),
    FAILED_EMERGENTSTOP(255, "任务被急停操作中断", false),
    FAILED_FLIGHTMODE_SWITCH_CHANGED(256, "挡位被切换过(任务被认为干预)", true),
    FAILED_MISSION_CANCELED(257, "任务被取消", true),
    FAILED_MISSION_NO_RELATIVE(260, "任务与当前站点无关联", true),
    FAILED_MISSION_SOURCE_CODE_NOMATCH(261, "任务起始库点未知", true),
    FAILED_MISSION_STARTPOT_WITHGARAGE_NOMATCH(262, "作为起点，任务起飞是否带机库配置不匹配", true),
    FAILED_MISSION_TERMINALPOT_WITHGARAGE_NOMATCH(263, "作为终点，任务降落是否带机库配置不匹配", true),
    FAILED_MISSION_TERMINALPOT_LOCATION_NOCONFIG(264, "终点站位置信息未配置", true),
    FAILED_MISSION_NOT_CONTROLLING_STATION(265, "当前站点非控制终端节点", true),
    FAILED_MISSION_NOT_START_STATION(266, "当前站点非任务起点", true),
    FAILED_MISSION_NOT_AB_DOUBLE_ALIVE(267, "任务关联双站点，但AB非同时存活", true),
    FAILED_MISSION_ERROR(267, "任务报错", true),


    FAILED_TOLAUNCH_OPEN_GARAGE(300, "打开机库失败(开机库+激活无人机)", true),
    FAILED_TOLAUNCH_NO_LOCATION(303, "未能获得当前坐标信息", false),
    FAILED_TOLAUNCH_LOCATION_NOT_ACCURATE(304, "坐标信息误差较大", false),
    FAILED_TOLAUNCH_COMPASS_ERROR(306, "指南针异常", false),
    FAILED_TOLAUNCH_COLLISIONAVOIDANCE_CANNOT_CLOSE(307, "避障关闭失败", false),
    FAILED_CHECK_ACTIVEOBSTACLE_AVOIDANCEENABLED(308, "获得主动避障失败", false),
    FAILED_TOLAUNCH_RTK_SETTING_FAILED(310, "RTK设置失败", false),
    FAILED_TOLAUNCH_PRECISIONLANDIND_CANNOT_ENABLE(312, "precisionLanding cannot enable", false),
    FAILED_TOLAUNCH_LANDINGPROTECTION_CANNOT_CLOSE(315, "landingprotection cannot disable", false),
    FAILED_TOLAUNCH_SMARTRETURNTOHOME_CANNOT_ENABLE(318, "智能返航开启失败", false),
    FAILED_TOLAUNCH_VISIONASSISTEDPOSITIONING_CANNOT_ENABLE(321, "视觉辅助定位开启失败", false),
    FAILED_TOLAUNCH_UPDOWNLINK_SINGNAL_LOW(324, "数传图传信号弱", false),
    FAILED_TOLAUNCH_GIMBAL_SIMULTANEOUS_FOLLOW(325, "云台跟随不同步", false),
    FAILED_TOLAUNCH_GPS_SINGNAL_TOO_WEAK(327, "GPS信号弱", false),
    FAILED_TOLAUNCH_SATELLITE_NUM_LESS(330, "GPS卫星数量不足", false),
    FAILED_TOLAUNCH_AIRCRAFT_SHUTDOWN(333, "无人机未开启", false),
    FAILED_TOLAUNCH_NO_FLIGHTCONTROLLER(336, "飞控组件获得失败", false),
    FAILED_TOLAUNCH_GIMBLE_CANNOT_MOVE(339, "云台检测失败", false),
    FAILED_TOLAUNCH_NO_AIRCRAFT(342, "无人机未开启或连接失败", false),
    //FAILED_TOLAUNCH_NO_YAWFOLLOW(345, "yaw is not follow mode", false),
    //FAILED_TOLAUNCH_VIRTUALSTICKMODEDISABLE(348, "virtualStickModeEnabled is disable", false),
    //FAILED_TOLAUNCH_PITCHRANGEEXTENSIONENABLED(351, "pitchrangeextension is disable", false),
    FAILED_TOLAUNCH_ROLLPITCHCOORDINATESYSTEM(354, "坐标系统水平轴设置失败", false),
    FAILED_TOLAUNCH_RTKENADBLE(355, "rtk开启失败", false),

    FAILED_TOLAUNCH_ROLLPITCHCONTROLMODE(357, "俯仰滚动控制模式设置失败", false),
    FAILED_TOLAUNCH_VERTICALCONTROLMODE(360, "垂直方向控制模式设置失败", false),
    FAILED_TOLAUNCH_YAWCONTROLMODE(363, "偏航控制模式设置失败", false),
    //FAILED_TOLAUNCH_VIRTUALSTICKMODEENABLED(366, "virtualstickmode is disable", false),
    FAILED_TOLAUNCH_CANNOTTAKEOFF(369, "CAN NOT TAKEOFF", false),
    FAILED_TOLAUNCH_OSDK_NO_RECEIVER(372,"OSDK未正常开启", false),
    FAILED_TOLAUNCH_OPEN_CLAW(375, "松开卡爪失败(起飞前)", true),


    FAILED_LANDPOT_WAITTIME(378,"当前为陆点悬停状态", false),

    ;

    /**
     * 任务类型
     * @param code  编号
     * @param name  名称
     */
    private MISSION_CHECK_CODE(int code, String name, boolean isNeedRepair){
        this.code = code;
        this.name = name;
        this.isNeedRepair = isNeedRepair;
    }

    private int code;
    private String name;
    private boolean isNeedRepair;   //需要人工介入

    public int getCode() {return code;}
    public void setCode(int code) {this.code = code;}
    public String getName() {return name;}
    public void setName(String name) {this.name = name;}
    public boolean isNeedRepair() { return isNeedRepair; }
    public void setNeedRepair(boolean needRepair) { isNeedRepair = needRepair;}

    public LanchError toClass(){
        return new LanchError(code, name, isNeedRepair);
    }

    public static class LanchError{
        private int code;
        private String name;
        private boolean isNeedRepair;   //需要人工介入
        LanchError(int code, String name, boolean isNeedRepair){
            this.code = code;
            this.name = name;
            this.isNeedRepair = isNeedRepair;
        }

        public int getCode() {
            return code;
        }

        public void setCode(int code) {
            this.code = code;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public boolean isNeedRepair() {
            return isNeedRepair;
        }

        public void setNeedRepair(boolean needRepair) {
            isNeedRepair = needRepair;
        }
    }


}
