package dji.sampleV5.aircraft.common;

import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.ScheduledThreadPoolExecutor;
import java.util.concurrent.TimeUnit;


public class TimerScheduler {
    private static final long MIN_INTERVAL = 200;
    private static TimerScheduler instance = new TimerScheduler();
    private ScheduledThreadPoolExecutor executor = new ScheduledThreadPoolExecutor(2);

    private TimerScheduler(){}

    public static TimerScheduler getInstance() {
        return instance;
    }

    /**
     * 两个任务之间间隔为interval
     * 使用完需及时结束task方法：task.cancel()
     */
    public ScheduledFuture scheduleWithFixedDelay(Runnable task, long initDelay, long interval){
        return executor.scheduleWithFixedDelay(task, initDelay, getInterval(interval), TimeUnit.MILLISECONDS);
    }

    /**
     * 类似Timer
     * 使用完需及时结束task方法：task.cancel()
     */
    public ScheduledFuture scheduleAtFixedRate(Runnable task, long initDelay, long interval){
        return executor.scheduleAtFixedRate(task, initDelay, getInterval(interval), TimeUnit.MILLISECONDS);
    }

    private long getInterval(long rawInterval){
        return rawInterval < MIN_INTERVAL ? MIN_INTERVAL : rawInterval;
    }
}
