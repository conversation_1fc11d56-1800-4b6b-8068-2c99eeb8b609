package dji.sampleV5.aircraft.common.voice;



public class SoundPlayerBean {
    private Boolean isFlying;
    private Boolean isGoingHome;
    private Boolean isCompassError;
    private Boolean isPitchAtStop;
    private Boolean isRollAtStop;
    private Boolean isYawAtStop;
    private Boolean isLowCellVoltageDetected;
    private Boolean isSdcardFull;
    private Boolean isSdcardInserted =true;
    private Boolean isSdcardHasError;
    private Integer landImmediatelyBattery;
    private Integer goHomeBattery;
    private Integer chargeRemainingInPercent;
    private Integer batteryPercentNeededToGoHome;
    private Integer currentLandImmediatelyBattery;

    public Boolean getIsFlying() {
        return isFlying;
    }

    public void setIsFlying(Boolean isFlying) {
        this.isFlying = isFlying;
    }

    public Boolean getIsGoingHome() {
        return isGoingHome;
    }

    public void setIsGoingHome(Boolean isGoingHome) {
        this.isGoingHome = isGoingHome;
    }

    public Boolean getIsCompassError() {
        return isCompassError;
    }

    public void setIsCompassError(Boolean isCompassError) {
        this.isCompassError = isCompassError;
    }

    public Boolean getIsPitchAtStop() {
        return isPitchAtStop;
    }

    public void setIsPitchAtStop(Boolean isPitchAtStop) {
        this.isPitchAtStop = isPitchAtStop;
    }

    public Boolean getIsRollAtStop() {
        return isRollAtStop;
    }

    public void setIsRollAtStop(Boolean isRollAtStop) {
        this.isRollAtStop = isRollAtStop;
    }

    public Boolean getIsYawAtStop() {
        return isYawAtStop;
    }

    public void setIsYawAtStop(Boolean isYawAtStop) {
        this.isYawAtStop = isYawAtStop;
    }

    public Boolean getIsLowCellVoltageDetected() {
        return isLowCellVoltageDetected;
    }

    public void setIsLowCellVoltageDetected(Boolean isLowCellVoltageDetected) {
        this.isLowCellVoltageDetected = isLowCellVoltageDetected;
    }

    public Boolean getIsSdcardFull() {
        return isSdcardFull;
    }

    public void setIsSdcardFull(Boolean isSdcardFull) {
        this.isSdcardFull = isSdcardFull;
    }

    public Boolean getIsSdcardInserted() {
        return isSdcardInserted;
    }

    public void setIsSdcardInserted(Boolean isSdcardInserted) {
        this.isSdcardInserted = isSdcardInserted;
    }

    public Boolean getIsSdcardHasError() {
        return isSdcardHasError;
    }

    public void setIsSdcardHasError(Boolean isSdcardHasError) {
        this.isSdcardHasError = isSdcardHasError;
    }

    public Integer getLandImmediatelyBattery() {
        return landImmediatelyBattery;
    }

    public void setLandImmediatelyBattery(Integer landImmediatelyBattery) {
        this.landImmediatelyBattery = landImmediatelyBattery;
    }

    public Integer getGoHomeBattery() {
        return goHomeBattery;
    }

    public void setGoHomeBattery(Integer goHomeBattery) {
        this.goHomeBattery = goHomeBattery;
    }

    public Integer getChargeRemainingInPercent() {
        return chargeRemainingInPercent;
    }

    public void setChargeRemainingInPercent(Integer chargeRemainingInPercent) {
        this.chargeRemainingInPercent = chargeRemainingInPercent;
    }

    public Integer getBatteryPercentNeededToGoHome() {
        return batteryPercentNeededToGoHome;
    }

    public void setBatteryPercentNeededToGoHome(Integer batteryPercentNeededToGoHome) {
        this.batteryPercentNeededToGoHome = batteryPercentNeededToGoHome;
    }

    public Integer getCurrentLandImmediatelyBattery() {
        return currentLandImmediatelyBattery;
    }

    public void setCurrentLandImmediatelyBattery(Integer currentLandImmediatelyBattery) {
        this.currentLandImmediatelyBattery = currentLandImmediatelyBattery;
    }
}
