package dji.sampleV5.aircraft.common.drone.mission;


import java.util.List;

import dji.sampleV5.aircraft.net.bean.Flightpath;
import dji.sampleV5.aircraft.net.bean.MissionJson;
import dji.sampleV5.aircraft.page.plan.WaypointMission;

public abstract class BaseMissionHelper {


    public static final int UNKNOWN = 10;
    public static final int DISCONNECTED =11;
    public static final int NOT_SUPPORTED = 12;
    public static final int RECOVERING = 13;
    public static final int READY_TO_UPLOAD = 14;
    public static final int UPLOADING = 15;
    public static final int READY_TO_EXECUTE = 16;
    public static final int EXECUTING = 17;
    public static final int EXECUTION_PAUSED = 18;
    public static final int INITIAL_PHASE = 19;

    IOperationResultListener resultListener;


    public void setResultListener(IOperationResultListener listener){
        this.resultListener = listener;
    }

    public abstract void startMission();
    public abstract void startBPMission();
    public abstract void pauseMission();
    public abstract void resumeMission();
    public abstract void stopMission();
    public abstract void stopLastAndUploadMission();
    public abstract void removeListener();

    public abstract int getCurrentState();

    public void setOriginMission(WaypointMission mission){}
    //public void setWayPointV2Mission(WaypointV2Mission mission){}

    public void setFlyPath(List<Flightpath> flightpath, MissionJson missionJson){}


    public void setWayPointMission(WaypointMission mission){}


    protected IMissionProcessCallback callback;

    public void setMissionProcessCallback(IMissionProcessCallback callback) {
        this.callback = callback;
    }

    public static final int MISSION_UPLOAD = 1;
    public static final int MISSION_START = 2;
    public static final int MISSION_PAUSE = 3;
    public static final int MISSION_RESUME = 4;
    public static final int MISSION_STOP = 5;
    public static final int MISSION_FINISH = 6;

    public static final int VALUE_SUCCEED = 10;
    public static final int VALUE_FAILED = 11;
    public static final int VALUE_STARTED = 12;
    public static final int VALUE_FINISHED = 13;
    /**
     * value
     * 0 ~ 1 value - upload progress
     * 2-failed
     * 3-succeed
     */
    public interface IOperationResultListener{
        void onResult(int state, float value, String err);
    }

    public interface IMissionProcessCallback {
        void onProgress(int progress);
    }


}
