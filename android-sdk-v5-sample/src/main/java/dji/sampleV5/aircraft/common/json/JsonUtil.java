package dji.sampleV5.aircraft.common.json;

import android.util.Log;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;

import java.lang.reflect.Type;
import java.util.Map;

import dji.sampleV5.aircraft.common.json.callback.IterationCallback;

public class JsonUtil {
    private static final String TAG = "JsonUtil";

    public static String toJson(Object object) {
        return JSON.toJSONString(object);
    }

    /**
     * @param cls     普通类(泛型类请使用{@link #fromJson(String, Type)})
     */
    public static <T> T fromJson(String json, Class<T> cls) {
        T t = null;
        try {
            t = JSON.parseObject(json, cls);
        } catch (Exception e) {
            Log.e(TAG, "fromJson: " + e.getLocalizedMessage());
        }
        return t;
    }

    /**
     * type构造方法请参考:
     * https://github.com/ikidou/TypeBuilder
     * @param type    泛型类(普通类请使用{@link #fromJson(String, Class)})
     */
    public static <T> T fromJson(String json, Type type) {
        T t = null;
        try {
            t = JSON.parseObject(json, type);
        } catch (Exception e) {
            Log.e(TAG, "fromJson: " + e.getLocalizedMessage());
        }
        return t;
    }

    public static void iterate(String json, IterationCallback callback) {
        JSONObject jsonObject = JSON.parseObject(json);
        for (Map.Entry<String, Object> next : jsonObject.entrySet()) {
            callback.onIterate(next.getKey(), next.getValue().toString());
        }
    }
}
