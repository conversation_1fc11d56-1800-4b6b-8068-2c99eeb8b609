package dji.sampleV5.aircraft.common.voice;

import android.content.Context;
import android.media.AudioManager;
import android.media.SoundPool;

import dji.sampleV5.aircraft.R;

import dji.sampleV5.aircraft.ContextUtil;


public class SoundPlayer {
//    public static final int SHUTTER_1 = 1;
//    public static final int SHUTTER_3 = 3;
//    public static final int SHUTTER_5 = 5;
//    public static final int SHUTTER_7 = 7;
//    public static final int RECORD_START = -1;
//    public static final int END = -2;
    public static final int ATTI_MODE = 0;
    public static final int BATTERY_ERROR = 41;
    public static final int BATTERY_OVERHEATING = 2;
    public static final int COMPASS_ERROR = 43;
//    public static final int DISCONNECT_GOHOME = 4;
    public static final int DISTANCE_LIMIT = 45;
//    public static final int FALISAFE = 6;
//    public static final int GIMBAL_LIMIT = 47;
    public static final int GO_HOME = 8;
//    public static final int GOHOME_FAILED = 9;
    public static final int HEIGHT_LIMIT = 10;
    public static final int HOME_POINT_IS_UPDATE = 11;
    public static final int IN_LIMIT_AREA = 12;
//    public static final int LANDING = 13;
    public static final int LOW_POWER = 14;
    public static final int NEAR_LIMIT_AREA = 15;
//    public static final int NO_SIGNAL = 16;
//    public static final int PGPS_MODE = 17;
//    public static final int SDCARD_ERROR = 18;
//    public static final int SDCARD_FULL = 19;
    public static final int SERIOUS_LOW_POWER = 20;
    public static final int SERIOUS_LOW_VOLTAGE = 21;
    public static final int SMART_LOW_BATTERY = 22;
    public static final int SMART_SERIOUS_LOW_BATTERY = 23;
//    public static final int STOP_GOHOME = 24;
//    public static final int STOP_LANDING = 25;
    public static final int TAKEOFF = 26;
    public static final int TRIPOD_FOLD = 27;
    public static final int TRIPOD_UNFOLD = 28;
//    public static final int VISION_WORK = 29;
//    public static final int VPS_MODE = 30;
//    public static final int CAMERA_FOCUS_BEEP = 32;
    public static final int CAMERA_EV_CENTER = 33;
//    public static final int BEEP_ONCE = 34;
//    public static final int BEEP_QUARTER = 35;
//    public static final int BEEP_TEN = 36;

    private static SoundPool pool;
    private static float volume;

    public void init() {
        AudioManager manager = (AudioManager) ContextUtil.getApplicationContext().getSystemService(Context.AUDIO_SERVICE);
        assert manager != null;
        int maxVolume = manager.getStreamMaxVolume(AudioManager.STREAM_MUSIC);
        int currentVolume = manager.getStreamVolume(AudioManager.STREAM_MUSIC);
        volume = currentVolume / (float) maxVolume;
        pool = new SoundPool.Builder()
                .setMaxStreams(1)
                .build();

        pool.setOnLoadCompleteListener((soundPool, sampleId, status) -> soundPool.play(sampleId, volume, volume, 1, 0, 1));
    }

    public void release() {
        if (pool != null) {
            pool.release();
            pool = null;
        }
    }

    public void playSoundNow(int sound) {
        if (pool != null) {
            playSound(sound, 1);
        }
    }

    public void playSound(final int sound) {
        if (pool != null) {
            ContextUtil.getHandler().postDelayed(() -> playSound(sound, 1), 2000);
        }
    }

    public void playSound(final int sound, int priority) {
        if (pool != null) {
            Context context = ContextUtil.getApplicationContext();
            switch (sound) {
//                // 相机聚焦音效
//                case CAMERA_FOCUS_BEEP:
//                    pool.load(context, R.raw.camera_focus_beep, priority);
//                    break;
//                // 壁障音效：beep一次
//                case BEEP_ONCE:
//                    pool.load(context, R.raw.beep_once, priority);
//                    break;
//                // 壁障音效：beep四次
//                case BEEP_QUARTER:
//                    pool.load(context, R.raw.beep_quarter, priority);
//                    break;
//                // 壁障音效：beep十次
//                case BEEP_TEN:
//                    pool.load(context, R.raw.beep_ten, priority);
//                    break;
//                // 单拍
//                case SHUTTER_1:
//                    pool.load(context, R.raw.shutter_1, priority);
//                    break;
//                // 连拍3张
//                case SHUTTER_3:
//                    pool.load(context, R.raw.shutter_3, priority);
//                    break;
//                // 连拍5张
//                case SHUTTER_5:
//                    pool.load(context, R.raw.shutter_5, priority);
//                    break;
//                // 连拍7张
//                case SHUTTER_7:
//                    pool.load(context, R.raw.shutter_7, priority);
//                    break;
//                // 录制开始
//                case RECORD_START:
//                    pool.load(context, R.raw.video_voice, priority);
//                    break;
//                // 录制结束
//                case END:
//                    pool.load(context, R.raw.end_video_record, priority);
//                    break;
                // 姿态模式
                case ATTI_MODE:
                    pool.load(context, R.raw.tips_voice_atti_mode, priority);
                    break;
                // 电池错误
                case BATTERY_ERROR:
                    pool.load(context, R.raw.tips_voice_battery_error, priority);
                    break;
                // 电池过热
                case BATTERY_OVERHEATING:
                    pool.load(context, R.raw.tips_voice_battery_overheating, priority);
                    break;
                // 指南针错误
                case COMPASS_ERROR:
                    pool.load(context, R.raw.tips_voice_compass_error, priority);
                    break;
//                // 无图像显示，5s后飞行器将自动返航
//                case DISCONNECT_GOHOME:
//                    pool.load(context, R.raw.tips_voice_disconnect_gohome, priority);
//                    break;
                // 限远
                case DISTANCE_LIMIT:
                    pool.load(context, R.raw.tips_voice_distance_limit, priority);
                    break;
                // 遥控器信号丢失
//                case FALISAFE:
//                    pool.load(context, R.raw.tips_voice_falisafe, priority);
//                    break;
                // 返航
                case GO_HOME:
                    pool.load(context, R.raw.tips_voice_gohome, priority);
                    break;
                // 返航失败
//                case GOHOME_FAILED:
//                    pool.load(context, R.raw.tips_voice_gohome_failed, priority);
//                    break;
                // 限高
                case HEIGHT_LIMIT:
                    pool.load(context, R.raw.tips_voice_height_limit, priority);
                    break;
                // 返航点已刷新
                case HOME_POINT_IS_UPDATE:
                    pool.load(context, R.raw.tips_voice_home_point_is_update, priority);
                    break;
                // 在限飞区
                case IN_LIMIT_AREA:
                    pool.load(context, R.raw.tips_voice_in_limit_area, priority);
                    break;
                // 降落
//                case LANDING:
//                    pool.load(context, R.raw.tips_voice_landing, priority);
//                    break;
                // 低电量警报
                case LOW_POWER:
                    pool.load(context, R.raw.tips_voice_low_power, priority);
                    break;
                // 靠近限飞区
                case NEAR_LIMIT_AREA:
                    pool.load(context, R.raw.tips_voice_near_limit_area, priority);
                    break;
                // 无图传信号
//                case NO_SIGNAL:
//                    pool.load(context, R.raw.tips_voice_no_signal, priority);
//                    break;
                // PGPS模式
//                case PGPS_MODE:
//                    pool.load(context, R.raw.tips_voice_pgps_mode, priority);
//                    break;
                case SERIOUS_LOW_POWER:
                    pool.load(context, R.raw.tips_voice_seriou_low_power, priority);
                    break;
                case SERIOUS_LOW_VOLTAGE:
                    pool.load(context, R.raw.tips_voice_seriou_low_voltage, priority);
                    break;
                case SMART_LOW_BATTERY:
                    pool.load(context, R.raw.tips_voice_smart_low_battery, priority);
                    break;
                case SMART_SERIOUS_LOW_BATTERY:
                    pool.load(context, R.raw.tips_voice_smart_seriou_low_battery, priority);
                    break;
//                case STOP_GOHOME:
//                    pool.load(context, R.raw.tips_voice_stop_gohome, priority);
//                    break;
//                case STOP_LANDING:
//                    pool.load(context, R.raw.tips_voice_stop_landing, priority);
//                    break;
                case TAKEOFF:
                    pool.load(context, R.raw.tips_voice_takeoff, priority);
                    break;
                case TRIPOD_FOLD:
                    pool.load(context, R.raw.tips_voice_tripod_fold, priority);
                    break;
                case TRIPOD_UNFOLD:
                    pool.load(context, R.raw.tips_voice_tripod_unfold, priority);
                    break;
//                case VISION_WORK:
//                    pool.load(context, R.raw.tips_voice_vision_work, priority);
//                    break;
//                case VPS_MODE:
//                    pool.load(context, R.raw.tips_voice_vps_mode, priority);
//                    break;
                case CAMERA_EV_CENTER:
                    pool.load(context, R.raw.camera_ev_center, priority);
                    break;
//                // 云台到达最大限位
//                case GIMBAL_LIMIT:
//                    pool.load(context, R.raw.tips_voice_gimbal_limit, priority);
//                    break;
//                // SD卡错误
//                case SDCARD_ERROR:
//                    pool.load(context, R.raw.tips_voice_sdcard_error, priority);
//                    break;
//                // SD卡已满
//                case SDCARD_FULL:
//                    pool.load(context, R.raw.tips_voice_sdcard_full, priority);
//                    break;
            }
        }
    }
}
