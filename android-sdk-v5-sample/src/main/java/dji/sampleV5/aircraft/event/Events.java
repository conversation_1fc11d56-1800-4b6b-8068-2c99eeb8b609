package dji.sampleV5.aircraft.event;


public class Events {

    public static class PlanParamEvent {
        public int length;
        public int time;
        public int point;

        public PlanParamEvent(int length, int time, int point, int photo, float resolution) {
            this.length = length;
            this.time = time;
            this.point = point;
        }
    }

    public static class RemoteMissionEvent {
        public String type;
        public String detail;

        public RemoteMissionEvent(String type, String detail) {
            this.type = type;
            this.detail = detail;
        }
    }


    public static class IndexEvent {

        /**
         * 播放器在上
         */
        public static final int INDEX_LIVE = 0;
        /**
         * 地图在上
         */
        public static final int INDEX_MAP = 1;

        private int index;

        public IndexEvent(int index) {
            this.index = index;
        }

        public int getIndex() {
            return index;
        }

        public void setIndex(int index) {
            this.index = index;
        }
    }

    /**
     * 设备注册状态
     */

   /* public static class DeviceBindStatusEvent {
        public static final int STATUS_UNKNOWN = -1;
        public static final int STATUS_UNREGISTERED = 0;
        public static final int STATUS_REGISTERED = 1;
        private int registerStatus = STATUS_UNKNOWN;
        private Device.DevicesBean devicesBean;

        public DeviceBindStatusEvent() {}

        public DeviceBindStatusEvent(int registerStatus, Device.DevicesBean devicesBean) {
            this.registerStatus = registerStatus;
            this.devicesBean = devicesBean;
        }

        public int getRegisterStatus() {
            return registerStatus;
        }

        public void setRegisterStatus(int registerStatus) {
            this.registerStatus = registerStatus;
        }

        public Device.DevicesBean getDevicesBean() {
            return devicesBean;
        }

        public void setDevicesBean(Device.DevicesBean devicesBean) {
            this.devicesBean = devicesBean;
        }
    }*/

    public static class DeviceConnectionEvent {
        private boolean connected;

        public DeviceConnectionEvent() {
        }

        public DeviceConnectionEvent(boolean connected) {
            this.connected = connected;
        }

        public boolean isConnected() {
            return connected;
        }

        public void setConnected(boolean connected) {
            this.connected = connected;
        }
    }

    public static class GetSNEvent {
        private String sn;

        public GetSNEvent() {
        }

        public GetSNEvent(String sn) {
            this.sn = sn;
        }

        public String getSn() {
            return sn;
        }

        public void setSn(String sn) {
            this.sn = sn;
        }
    }

    public static class UserEvent {
        public UserEvent() {
        }
    }


}
