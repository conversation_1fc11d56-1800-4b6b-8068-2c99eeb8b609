package dji.sampleV5.aircraft.event;

import org.greenrobot.eventbus.EventBus;

public class Event {
    public static void postSticky(Object event) {
        EventBus.getDefault().postSticky(event);
    }

    public static void removeStickyEvent(Object event) {
        EventBus.getDefault().removeStickyEvent(event);
    }

    public static void removeAllStickyEvents() {
        EventBus.getDefault().removeAllStickyEvents();
    }

    public static void post(Object event){
        EventBus.getDefault().post(event);
    }

    public static void register(Object subscriber){
        if (!EventBus.getDefault().isRegistered(subscriber)) {
            EventBus.getDefault().register(subscriber);
        }
    }

    public static void unregister(Object subscriber){
        EventBus.getDefault().unregister(subscriber);
    }
}
