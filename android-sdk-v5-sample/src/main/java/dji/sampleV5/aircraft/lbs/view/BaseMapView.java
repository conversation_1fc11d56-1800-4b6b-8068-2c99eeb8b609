package dji.sampleV5.aircraft.lbs.view;

import android.content.Context;
import android.graphics.Color;
import android.graphics.Paint;
import android.graphics.Point;
import android.view.MotionEvent;
import android.view.View;
import android.widget.FrameLayout;

import androidx.annotation.NonNull;


import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import dji.sampleV5.aircraft.lbs.MapController;
import dji.sampleV5.aircraft.lbs.MapEventListener;
import dji.sampleV5.aircraft.lbs.bean.AppLatLng;
import dji.sampleV5.aircraft.lbs.bean.MarkerInfo;
import dji.sampleV5.aircraft.util.BitmapUtils;
import dji.sampleV5.aircraft.util.phone.DensityUtil;


public class BaseMapView extends FrameLayout implements View.OnTouchListener, View.OnClickListener, MapEventListener {
    protected static final int MARKER_VIEW_WIDTH = 24;
    protected int markerViewWidth;

    protected LinkedHashMap<View, MarkerInfo.ProxyMarker> viewMarkerMap = new LinkedHashMap<>();
    protected Map<MarkerInfo.ProxyMarker, View> markerViewMap = new HashMap<>();
    protected List<MarkerInfo.ProxyMarker> markerList = new ArrayList<>();

    protected Paint paint;
    protected int movingIndex = -1;

    protected float downRawX;
    protected float downRawY;
    protected MapBoardViewListener listener;
    protected int lstIndex = -1;

    protected MapController mMapController;

    public BaseMapView(@NonNull Context context) {
        super(context);
        markerViewWidth = DensityUtil.dp2px(MARKER_VIEW_WIDTH);
        setBackgroundColor(Color.TRANSPARENT);
        paint = new Paint();
        paint.setColor(Color.RED);
        paint.setStyle(Paint.Style.STROKE);
        paint.setStrokeWidth(5);
    }

    public void setMapBoardViewListener(MapBoardViewListener listener) {
        this.listener = listener;
    }

    public void addInitMarker() {
    }

    public int deleteMarker() {
        return 0;
    }

    public void clearMap() {
    }

    public void init(MapController mapController) {
        this.mMapController = mapController;
        mMapController.setMapEventListener(this);
    }

    public void release() {
        this.mMapController.setMapEventListener(null);
    }

    @Override
    public void onClick(View v) {
        int index = markerList.indexOf(viewMarkerMap.get(v));
        if (index % 2 == 0) {
            if (index != lstIndex) {
                chooseMarker(index);

                // call listener
                if (listener != null) {
                    listener.onMarkerChosen(lstIndex == -1 ? -1 : (lstIndex / 2), index / 2);
                }
                lstIndex = index;
            }
        }
    }

    protected void onNewMarker(int index, MarkerInfo.ProxyMarker marker) {
        if (listener != null) {
            listener.onNewMarker(index / 2, marker);
        }
    }

    protected void onMarkerMoving(int index, MarkerInfo.ProxyMarker marker) {
        if (listener != null) {
            listener.onMarkerMoving(index / 2, marker);
        }
    }

    private void chooseMarker(int index) {
        for (int i = 0; i < markerList.size(); i++) {
            if (i % 2 == 0) {
                if (i == index) {
                    markerList.get(i).setIcon(BitmapUtils.getChooseNumberBitmap(markerViewWidth, String.valueOf((i + 1) / 2 + 1)));
                } else {
                    markerList.get(i).setIcon(BitmapUtils.getNumberBitmap(markerViewWidth, String.valueOf((i + 1) / 2 + 1)));
                }
            }
        }
    }

    protected void updateMarkerIcon() {
        for (int i = 0; i < markerList.size(); i++) {
            if (i % 2 == 0) {
                markerList.get(i).setIcon(BitmapUtils.getNumberBitmap(markerViewWidth, String.valueOf((i + 1) / 2 + 1)));
            }
        }
    }

    @Override
    public boolean onTouch(View v, MotionEvent event) {
        return false;
    }

    @Override
    public void onCameraChangeFinish() {
        updateMarkerViewPosition();
    }

    @Override
    public void onCameraChange(AppLatLng appLatLng, float bearing, float tilt, float zoom) {
    }

    protected MarkerInfo.ProxyMarker addMarker(MarkerInfo info) {
        return mMapController.addMarker(info);
    }

    protected Point toScreenLocation(AppLatLng latLng) {
        return mMapController.toScreenLocation(latLng);
    }

    protected AppLatLng fromScreenLocation(Point point) {
        return mMapController.fromScreenLocation(point);
    }

    protected void notifyDataSetChanged() {
    }

    protected void updateMarkerViewPosition() {
        for (MarkerInfo.ProxyMarker marker : markerList) {
            Point point = toScreenLocation(marker.getPosition());
            updateMarkerViewPosition(markerViewMap.get(marker), point.x, point.y);
        }
    }

    private void updateMarkerViewPosition(View v, int x, int y) {
        if (v != null) {
            MarginLayoutParams params = (MarginLayoutParams) v.getLayoutParams();
            params.leftMargin = x - (markerViewWidth / 2);
            params.topMargin = y - (markerViewWidth / 2);
            v.setLayoutParams(params);
        }
    }

    public interface MapBoardViewListener {
        void onMarkerChosen(int lstIndex, int index);

        void onNewMarker(int index, MarkerInfo.ProxyMarker marker);

        void onMarkerMoving(int index, MarkerInfo.ProxyMarker marker);
    }
}
