package dji.sampleV5.aircraft.lbs;

import android.app.Activity;
import android.graphics.Color;
import android.graphics.Point;
import android.util.Log;


import org.greenrobot.eventbus.Subscribe;

import java.util.ArrayList;
import java.util.List;

import dji.sampleV5.aircraft.SensorEventHelper;
import dji.sampleV5.aircraft.event.Events;
import dji.sampleV5.aircraft.lbs.bean.AppLatLng;
import dji.sampleV5.aircraft.lbs.bean.PolylineInfo;
import dji.sampleV5.aircraft.util.phone.DensityUtil;


public class MapPainter extends AircraftMapPainter implements SensorEventHelper.RotationListener {
    private static final int LOCK_DRONE_NONE = 0;
    private static final int LOCK_DRONE_INSIGHT = 1;
    private static final int LOCK_DRONE_CENTER = 2;

    private SensorEventHelper mSensorHelper;
    /**
     * 飞行轨迹
     */
    private PolylineInfo.ProxyPolyline trajectoryLine;
    private int lockDroneMode = LOCK_DRONE_CENTER;
    /**
     * 地图方向锁定
     */
    private boolean lockMap = true;
    private float sensorRotation;

    private boolean isMap;


    public MapPainter(Activity activity, MapController mapController, SensorEventHelper sensorEventHelper, boolean isMap) {
        super(activity, mapController);
        this.mSensorHelper = sensorEventHelper;
        this.isMap = isMap;
    }

    @Override
    public void onStart() {
        mSensorHelper.addRotationListener(this);
        super.onStart();
    }

    @Override
    public void onStop() {
        super.onStop();
        mSensorHelper.removeRotationListener(this);
    }

    public void changeLockMap() {
        this.lockMap = !lockMap;
        if (lockMap) {
            mMapController.changeBearing(0);
        } else {
            mMapController.changeBearing(this.sensorRotation);
            if (myLocationMarker != null) {
                myLocationMarker.setRotation(0);
            }
        }
    }

    public boolean isLockMap() {
        return this.lockMap;
    }

    public void moveTo(boolean drone) {
        AppLatLng loc = null;
        if (drone) {
            if (aircraftMarker != null) {
                loc = aircraftMarker.getPosition();
            }
        } else {
            if (homeMarker != null) {
                loc = homeMarker.getPosition();
            }
        }
        if (loc != null) {
            mMapController.newLatLngZoom(true, loc, zoom);
        }
    }

    public void setLockDroneMode(int mode) {
        this.lockDroneMode = mode;
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
    }

    @Override
    protected void onAircraftPositionChanged(AppLatLng pos) {
        super.onAircraftPositionChanged(pos);
//        Log.e("TAG", "onAircraftPositionChanged: " + pos);
        updateTrajectory(pos.getLat(), pos.getLng());

        //lockDrone(pos);
    }

    @Subscribe
    public void onIndexEvent(Events.IndexEvent event) {
        isMap = (event.getIndex() == Events.IndexEvent.INDEX_MAP);
        if (!isMap) {
            setLockDroneMode(LOCK_DRONE_CENTER);

            focus(true);
        } else {
            setLockDroneMode(LOCK_DRONE_NONE);

            focus(true);
        }
    }

    @Override
    protected void stopRefreshDroneStatus() {
        super.stopRefreshDroneStatus();
        removeTrajectory();
    }

    @Override
    public void onRotationChanged(float rotation) {
        this.sensorRotation = -rotation;
        if (!lockMap) {
            mMapController.changeBearing(this.sensorRotation);
        }
        if (myLocationMarker != null) {
            myLocationMarker.setRotation(rotation);
        }
    }

    /**
     * 有无人机，忽略
     * 没有无人机，人位置居中
     */
    @Override
    protected void focus(boolean isAnimate) {
        AppLatLng loc = null;
        if (aircraftMarker == null) {
            if (myLocationMarker != null) {
                loc = myLocationMarker.getPosition();
            }
        } else {
            if (lockDroneMode != LOCK_DRONE_CENTER) {
                zoom = MapController.DEFAULT_ZOOM;
            } else {
                zoom = 16;
            }
            loc = aircraftMarker.getPosition();
        }
        if (loc != null) {
            mMapController.newLatLngZoom(isAnimate, loc, zoom);
        }
    }

    private List<AppLatLng> points = new ArrayList<>();
    private void updateTrajectory(double lat, double lng) {
        AppLatLng latLng = new AppLatLng(lat, lng);
        points.add(latLng);
        if (trajectoryLine == null) {
            PolylineInfo info = new PolylineInfo();
            info.setColor(Color.RED);
            info.setPointList(points);
            info.setZIndex(MapIndex.TRAJECTORY_INDEX);
            trajectoryLine = mMapController.addPolyline(info);
        } else {
            trajectoryLine.setPoints(points);
        }
    }

    public void clearTrajectory() {
        points.clear();
        if (trajectoryLine != null) {
            trajectoryLine.setPoints(points);
        }
    }

    public void removeTrajectory() {
        if (trajectoryLine != null) {
            trajectoryLine.remove();
            trajectoryLine = null;
        }
    }

    private void lockDrone(AppLatLng pos) {
        switch (lockDroneMode) {
            case LOCK_DRONE_CENTER:
                mMapController.newLatLngZoom(false, pos, zoom);
                break;
            case LOCK_DRONE_INSIGHT:
                // 将飞机限制在视野之内
                Point point = mMapController.toScreenLocation(pos);
                int dx = 0;
                int dy = 0;
                int bound = this.isMap ? 200 : 40;
                if (point.y < bound) {
                    dy = bound - point.y;
                } else if (point.y > DensityUtil.getScreenHeight() - bound) {
                    dy = DensityUtil.getScreenHeight() - bound - point.y;
                }
                if (point.x < bound) {
                    dx = bound - point.x;
                } else if (point.x > DensityUtil.getScreenWidth() - bound) {
                    dx = DensityUtil.getScreenWidth() - bound - point.x;
                }
                if (dx != 0 || dy != 0) {
                    mMapController.scrollBy(-dx, -dy);
                }
                break;
        }
    }

}
