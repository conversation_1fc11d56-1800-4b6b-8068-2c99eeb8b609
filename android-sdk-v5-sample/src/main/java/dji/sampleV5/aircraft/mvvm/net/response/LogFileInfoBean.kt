package dji.sampleV5.aircraft.mvvm.net.response

import androidx.annotation.Keep
import androidx.databinding.BaseObservable
import java.io.File

/**
 * Copyright (C), 2015-2025
 * FileName: FileItemInfo
 * Author: 80945
 * Date: 2025/3/17 18:16
 * Description:
 */
@Keep
data class LogFileInfoBean(
    var file: File? = null,
    var fileName: String? = "",
    var fileSize: Long? = -1,
    var fileType: String? = "",
    var isChecked: Boolean = false,
): BaseObservable() {

    fun getRealFileSize(): String {
        return when {
            fileSize == -1L -> ""
            fileSize!! < 1024 -> "$fileSize B"
            fileSize!! < 1000000 -> "%.2f KB".format(fileSize!! / 1024.0)
            fileSize!! >= 1000000 -> "%.2f MB".format(fileSize!! / (1024.0 * 1024.0))
            else -> "%.2f KB".format(fileSize!! / 1024.0)
        }
    }

}