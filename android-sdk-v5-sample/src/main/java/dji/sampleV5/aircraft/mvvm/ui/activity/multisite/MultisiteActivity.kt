package dji.sampleV5.aircraft.mvvm.ui.activity.multisite

import android.animation.ValueAnimator
import android.annotation.SuppressLint
import android.graphics.BitmapFactory
import android.graphics.Color
import android.os.Build
import android.os.Bundle
import android.util.TypedValue
import android.view.LayoutInflater
import android.view.View
import android.view.animation.AccelerateDecelerateInterpolator
import android.view.animation.Animation
import android.view.animation.LinearInterpolator
import android.view.animation.TranslateAnimation
import androidx.activity.viewModels
import androidx.annotation.RequiresApi
import androidx.appcompat.widget.AppCompatTextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.constraintlayout.widget.ConstraintSet
import androidx.core.content.ContextCompat
import androidx.core.view.GravityCompat
import androidx.drawerlayout.widget.DrawerLayout.LOCK_MODE_LOCKED_OPEN
import androidx.lifecycle.lifecycleScope
import androidx.transition.AutoTransition
import androidx.transition.Transition
import androidx.transition.TransitionListenerAdapter
import androidx.transition.TransitionManager
import com.amap.api.maps.AMap
import com.amap.api.maps.CameraUpdateFactory
import com.amap.api.maps.model.BitmapDescriptorFactory
import com.amap.api.maps.model.CameraPosition
import com.amap.api.maps.model.LatLng
import com.amap.api.maps.model.Marker
import com.amap.api.maps.model.MarkerOptions
import com.amap.api.maps.model.MultiPointItem
import com.amap.api.maps.model.MultiPointOverlayOptions
import com.amap.api.maps.model.Polygon
import com.amap.api.maps.model.PolygonOptions
import com.amap.api.maps.model.Polyline
import com.amap.api.maps.model.PolylineOptions
import com.drake.brv.utils.bindingAdapter
import com.drake.brv.utils.linear
import com.drake.brv.utils.setup
import com.google.gson.JsonParser
import com.hjq.toast.Toaster
import com.lxj.xpopup.XPopup
import com.yc.video.player.VideoPlayer
import com.yc.video.player.VideoPlayerBuilder
import com.yc.video.ui.view.BasisVideoController
import com.yc.video.ui.view.CustomErrorView
import dji.sampleV5.aircraft.DJIAircraftApplication
import dji.sampleV5.aircraft.R
import dji.sampleV5.aircraft.data.preference.SpUtil
import dji.sampleV5.aircraft.databinding.ActivityMultisiteBinding
import dji.sampleV5.aircraft.lbs.MapIndex
import dji.sampleV5.aircraft.lbs.bean.AppLatLng
import dji.sampleV5.aircraft.mvvm.adapter.MissionMenuAdapter
import dji.sampleV5.aircraft.mvvm.base.BaseVMActivity
import dji.sampleV5.aircraft.mvvm.base.appContext
import dji.sampleV5.aircraft.mvvm.event.LiveDataEvent
import dji.sampleV5.aircraft.mvvm.ext.drawableToBitmap
import dji.sampleV5.aircraft.mvvm.ext.getColorExt
import dji.sampleV5.aircraft.mvvm.ext.getTimeAgoStartOfDay
import dji.sampleV5.aircraft.mvvm.ext.getTodayEndOfDayTime
import dji.sampleV5.aircraft.mvvm.ext.gone
import dji.sampleV5.aircraft.mvvm.ext.gpsToAmap
import dji.sampleV5.aircraft.mvvm.ext.isGone
import dji.sampleV5.aircraft.mvvm.ext.isVisible
import dji.sampleV5.aircraft.mvvm.ext.loadListSuccess
import dji.sampleV5.aircraft.mvvm.ext.loadMore
import dji.sampleV5.aircraft.mvvm.ext.mmkv
import dji.sampleV5.aircraft.mvvm.ext.setOnclickNoRepeat
import dji.sampleV5.aircraft.mvvm.ext.showDialogMessage
import dji.sampleV5.aircraft.mvvm.ext.showMapTypePopup
import dji.sampleV5.aircraft.mvvm.ext.toJsonStr
import dji.sampleV5.aircraft.mvvm.ext.toast
import dji.sampleV5.aircraft.mvvm.ext.visible
import dji.sampleV5.aircraft.mvvm.key.ValueKey
import dji.sampleV5.aircraft.mvvm.mission.MissionFragmentFactory
import dji.sampleV5.aircraft.mvvm.mission.MissionMenuFragment
import dji.sampleV5.aircraft.mvvm.net.LoadStatusEntity
import dji.sampleV5.aircraft.mvvm.net.response.FlightHistoryListBean
import dji.sampleV5.aircraft.mvvm.net.response.LocationInfoBean
import dji.sampleV5.aircraft.mvvm.net.response.MissionInfoBean
import dji.sampleV5.aircraft.mvvm.net.response.SiteAreaListBean
import dji.sampleV5.aircraft.mvvm.net.response.SiteListBean
import dji.sampleV5.aircraft.mvvm.ui.activity.realtime.RealTimeActivity.Companion.HIVE_MODE
import dji.sampleV5.aircraft.mvvm.ui.activity.realtime.RealTimeActivity.Companion.INS_STOP_CODE
import dji.sampleV5.aircraft.mvvm.ui.activity.realtime.RealTimeActivity.Companion.MISSION_TYPE
import dji.sampleV5.aircraft.mvvm.ui.activity.realtime.RealTimeActivity.Companion.PAUSE_CODE
import dji.sampleV5.aircraft.mvvm.ui.activity.realtime.RealTimeActivity.Companion.RECOVER_CODE
import dji.sampleV5.aircraft.mvvm.ui.activity.realtime.RealTimeActivity.Companion.RETURN_CODE
import dji.sampleV5.aircraft.mvvm.ui.activity.realtime.RealTimeActivity.Companion.STOP_CODE
import dji.sampleV5.aircraft.mvvm.ui.activity.realtime.RealTimeActivity.Companion.STOP_VIDEO_CODE
import dji.sampleV5.aircraft.mvvm.ui.activity.realtime.RealTimeActivity.Companion.TAKE_PICTURE_CODE
import dji.sampleV5.aircraft.mvvm.ui.activity.realtime.RealTimeActivity.Companion.TAKE_VIDEO_CODE
import dji.sampleV5.aircraft.mvvm.ui.activity.realtime.RealTimeViewModel
import dji.sampleV5.aircraft.mvvm.ui.viewModel.CameraStreamListVM
import dji.sampleV5.aircraft.mvvm.util.AMapLocationUtil
import dji.sampleV5.aircraft.mvvm.util.CoordinateConverterUtil
import dji.sampleV5.aircraft.mvvm.util.MapOverlayUtil
import dji.sampleV5.aircraft.mvvm.util.XLogUtil
import dji.sampleV5.aircraft.mvvm.util.marker.MarkerUtil
import dji.sampleV5.aircraft.mvvm.widget.floatmenu.FloatItem
import dji.sampleV5.aircraft.mvvm.widget.floatmenu.FloatLogoMenu
import dji.sampleV5.aircraft.mvvm.widget.floatmenu.FloatMenuView
import dji.sampleV5.aircraft.mvvm.widget.player.LiveControlView
import dji.sampleV5.aircraft.mvvm.widget.popup.HistoryFlightAreaPopup
import dji.sampleV5.aircraft.mvvm.widget.popup.MapTypePopup
import dji.sampleV5.aircraft.mvvm.widget.popup.SiteLocationPopup
import dji.sampleV5.aircraft.net.bean.qicloud.EventType
import dji.sampleV5.aircraft.net.bean.qicloud.ListItem
import dji.sampleV5.aircraft.net.bean.qicloud.SiteState
import dji.sampleV5.aircraft.page.login.LoginCache.SocketIOConfig
import dji.sampleV5.aircraft.page.plan.WaypointMission
import dji.sampleV5.aircraft.util.GCJ02_WGS84
import dji.sampleV5.aircraft.util.phone.DensityUtil
import dji.sdk.keyvalue.value.common.ComponentIndexType
import dji.v5.utils.common.JsonUtil
import dji.v5.ux.core.ui.setting.data.MenuBean
import dji.v5.ux.core.widget.fpv.FPVWidget
import dji.v5.ux.mapkit.core.utils.DJIGpsUtils
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.async
import kotlinx.coroutines.awaitAll
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import kotlinx.coroutines.yield
import org.json.JSONObject
import java.util.Timer
import java.util.TimerTask
import kotlin.concurrent.timerTask

@RequiresApi(Build.VERSION_CODES.N)
class MultisiteActivity : BaseVMActivity<MultisiteViewModel, ActivityMultisiteBinding>()  {
    private val TAG = this.javaClass.name
    private lateinit var siteMap: AMap
    //坐标和经纬度转换工具
    private var timer = Timer()
    private val timers = mutableMapOf<String, TimerTask>()
    private val timeout: Long = 5000 // 超时时间，单位毫秒
    private var multiPointList: MutableList<MultiPointItem> = mutableListOf()
    private val menuList = mutableListOf(
        MenuBean(
            R.drawable.ic_realtime_normal_task_selector,
            R.drawable.ic_realtime_normal_task_normal,
        ),
        MenuBean(
            R.drawable.ic_realtime_instancy_task_selector,
            R.drawable.ic_realtime_instancy_task_normal,
        ),
        MenuBean(
            R.drawable.ic_realtime_uav_selector,
            R.drawable.ic_realtime_uav_normal,
        ),
        MenuBean(
            R.drawable.ic_realtime_gimbal_selector,
            R.drawable.ic_realtime_gimbal_normal,
        ),
        MenuBean(
            R.drawable.ic_realtime_multimedia_selector,
            R.drawable.ic_realtime_multimedia_normal,
        ),
        MenuBean(
            R.drawable.ic_realtime_load_selector,
            R.drawable.ic_realtime_load_normal,
        ),
        /*        MenuBean(
                    R.drawable.ic_realtime_other_selector,
                    R.drawable.ic_realtime_other_normal,
                )*/
    )
    private val fragmentList: MutableList<MissionMenuFragment> = mutableListOf(
        MissionMenuFragment.newInstance(MissionFragmentFactory.FRAGMENT_TAG_NORMAL_TASK),
        MissionMenuFragment.newInstance(MissionFragmentFactory.FRAGMENT_TAG_INSTANCY_TASK),
        MissionMenuFragment.newInstance(MissionFragmentFactory.FRAGMENT_TAG_CONTROL),
        MissionMenuFragment.newInstance(MissionFragmentFactory.FRAGMENT_TAG_GIMBAL),
        MissionMenuFragment.newInstance(MissionFragmentFactory.FRAGMENT_TAG_MULTI),
        MissionMenuFragment.newInstance(MissionFragmentFactory.FRAGMENT_TAG_LOAD),
//        MissionMenuFragment.newInstance(MissionFragmentFactory.FRAGMENT_TAG_OTHER)
    )

    //右视图动画
    private var translateRightAniHide: TranslateAnimation? = null
    private var translateRightAniShow: TranslateAnimation? = null
    private lateinit var firstController: BasisVideoController
    private lateinit var secondController: BasisVideoController
    private val mVideoViews: ArrayList<VideoPlayer<*>> = ArrayList()
    private lateinit var missionMenuAdapter: MissionMenuAdapter
    private lateinit var markerManager: MarkerUtil
    private val operateModel: RealTimeViewModel by viewModels()
    private var isAddMarker = false
    private var startPoint: LatLng? = null
    private var polyline: Polyline? = null
    private var startToUavLine: Polyline? = null
    private var checkedSiteInfo: SiteListBean.SiteItem? = null
    private var missionBatch: String = ""
    private var isBatchInfo = true
    private var flightRecords: MutableList<LatLng> = mutableListOf()
    private var uavMarker: Marker? = null
    private var yawAngleValue: Double = 0.0
    private var mFloatMenu: FloatLogoMenu? = null
    private var floatItemList: MutableList<FloatItem> = mutableListOf()
    private var isRecording: Int = 0
    private var isSwapped = false
    private var marker: Marker? = null
    private var locationInfoBean = LocationInfoBean()
    private var subLocationList: MutableList<LocationInfoBean.MainLocation.SubLocation> = mutableListOf()
    private var subMarkerList: MutableList<Marker> = mutableListOf()
    private var allSiteArea: MutableList<Polygon> = mutableListOf()
    private var isShowSubLocation = LiveDataEvent.showSubLocationId.value
    private var allHistoryFlightPath: MutableList<Polyline> = mutableListOf()
    private var siteMissionListInfo: MutableList<MissionInfoBean> = mutableListOf()
    private var startTime: String = ""
    private var endTime: String = ""
    private var subLocationId: Int = -1
    private var subLocation: LocationInfoBean.MainLocation.SubLocation = LocationInfoBean.MainLocation.SubLocation()
    private var isShowHistory = mmkv.getBoolean(ValueKey.IS_SHOW_HISTORY, false)
    private lateinit var typePopup: MapTypePopup
    //是否是当前站点
    private var isCurrentSite: Boolean = false
    private val cameraViewModel: CameraStreamListVM by viewModels()
    private var onlyOneCamera = false
    private lateinit var cameraIndex: ComponentIndexType
    private var cameraSourceList: MutableList<ComponentIndexType> = mutableListOf()
    private var siteIDList: MutableList<String> = mutableListOf()
    @SuppressLint("ClickableViewAccessibility")
    override fun initView(savedInstanceState: Bundle?) {
        //这么做是为了优化界面加载的速度，如果不使用协程的话，进入界面会很卡
        lifecycleScope.launch(Dispatchers.Main) {
            mBinding.siteMap.onCreate(savedInstanceState)
            startTime = getTimeAgoStartOfDay(0)
            endTime = getTodayEndOfDayTime()
            MapOverlayUtil.bindLifecycle(this@MultisiteActivity)
            registerDefUIChange(operateModel)
            mBinding.siteMap.map.addOnMapLoadedListener {
                initMapView()
                initMyLocation()
                //初始化地图点击监听
                initMapClick()
                initMarkerClick()
                //初始化航线绘制
                markerManager = MarkerUtil(this@MultisiteActivity, mBinding.siteMap.map)
            }
            initSiteList()
            initAnimation()
            //初始化侧边任务操作面模板
            initMissionPanel()
            //初始化视频控件
            initVideoPlayer()
            //初始化视频流视角切换监听
            initVideoSwitch()
            //初始化菜单按钮数据
            initMenuData()
            //从缓存中获取上次选中的站点信息
            getAllLocation()
            //初始化地图选择popup
            typePopup = MapTypePopup(this@MultisiteActivity, object : MapTypePopup.OnItemClickListener{
                override fun onItemClick(position: Int) {
                    when(position) {
                        0 -> mBinding.siteMap.map.mapType = AMap.MAP_TYPE_NORMAL
                        1 -> mBinding.siteMap.map.mapType = AMap.MAP_TYPE_SATELLITE
                    }
                }
            })

            //设置全局同级单选
            mBinding.siteRv.bindingAdapter.singleMode = true
            //drawable抽屉布局透明背景
            mBinding.drawerLayout.setScrimColor(Color.TRANSPARENT)
            mBinding.siteRefresh.onRefresh {
                mViewModel.getMultisiteList(true)
            }.loadMore {
                mViewModel.getMultisiteList(false)
            }
            mBinding.scSiteMonitoring.setOnCheckedChangedListener { cell, isChecked ->
                if (isChecked) {
                    mBinding.conSecondFpv.visible()
                } else {
                    mBinding.conSecondFpv.gone()
                }
            }
            LiveDataEvent.run {
                showSubLocationId.observe(this@MultisiteActivity) {
                    isShowSubLocation = it
                    if (isShowSubLocation == true && mViewModel.siteAreaListLiveData.value != null) {
                        drawSubAreaNumber(mViewModel.siteAreaListLiveData.value!!)
                    } else {
                        clearSubMarkers()
                    }
                }
                switchAddMarker.observe(this@MultisiteActivity) {
                    // 切换是否添加标记的状态
                    isAddMarker = it
                }
                missionFlightInfo.observe(this@MultisiteActivity) {
                    drawWaypoint(it)
                }

                subLocationId.observe(this@MultisiteActivity) {
                    <EMAIL> = it
                    // 切换子场站时清空之前的任务数据，避免数据累积
                    siteMissionListInfo.clear()
                    // 立即清空全局任务信息，避免UI显示旧数据
                    LiveDataEvent.missionInfoList.value = mutableListOf()
                    operateModel.getAllMissionListInfo(
                        siteId = checkedSiteInfo?.siteId.toString(),
                        subLocationId = it
                    )
                }
            }

            //获取可用相机列表
            cameraViewModel.availableCameraListData.observe(this@MultisiteActivity) {
                cameraSourceList = it.toMutableList()
                updateAvailableCamera(cameraSourceList)
            }
        }
    }

    override fun initData() {
        lifecycleScope.launch(Dispatchers.Main) {
            delay(1000)
            mViewModel.getMultisiteList(true)
        }
    }

    override fun onBindViewClick() {
        setOnclickNoRepeat(
            mBinding.btnDrawerHandle,
            mBinding.btnSwitch,
            mBinding.widgetCompass.btnMenu,
            mBinding.widgetCompass.tvUavStatus,
            mBinding.btnSiteMonitoring,
            mBinding.secondFpvClose,
            mBinding.btnSiteChoose,
            mBinding.btnHistorySwitch,
            mBinding.btnMapType
        ) {
            when (it.id) {
                R.id.btn_drawer_handle -> {
                    updateInfoView(mBinding.linearSiteList.isGone())
                }
                R.id.btn_map_type -> {
                    if (typePopup.isShow) {
                        typePopup.dismiss()
                    }else {
                        showMapTypePopup(typePopup, it)
                    }
                }
                R.id.btn_switch -> {
                    if (isCurrentSite) {
                        toggleFullScreen(mBinding.djiFpvWidget, mBinding.siteMap, mBinding.constraintLayout, 240, 138)
                    }else {
                        toggleFullScreen(mBinding.firstFpvWidget, mBinding.siteMap, mBinding.constraintLayout, 240, 138)
                    }
                }
                R.id.btn_menu -> {
                    mBinding.drawerLayout.openDrawer(GravityCompat.END)
                    mBinding.drawerLayout.setDrawerLockMode(LOCK_MODE_LOCKED_OPEN)
                }

                R.id.tv_uav_status -> {
                    onBackPressed()
                }

                R.id.btn_site_monitoring -> {
                    if (mBinding.scSiteMonitoring.isGone()) {
                        mBinding.scSiteMonitoring.visible()
                    } else {
                        mBinding.scSiteMonitoring.gone()
                    }
                }

                R.id.second_fpv_close -> {
                    mBinding.scSiteMonitoring.isChecked = false
                }

                R.id.btn_site_choose -> {
                    showSitePopup()
                }

                R.id.btn_history_switch -> {
                    showHistoryFlightPath()
                }
            }
        }
    }

    override fun onRequestSuccess() {
        mViewModel.run {
            siteAreaListLiveData.observe(this@MultisiteActivity) {
                if (it.size > 0) {
                    //绘制子阵
                    drawSubArea(it)
                    if (isShowSubLocation == true) {
                        //绘制子阵编号
                        drawSubAreaNumber(it)
                    }
                } else {
                    "暂无子阵信息".toast()
                }
            }
            socketSiteInfoLiveData.observe(this@MultisiteActivity) {
                updateSiteState(it)
                if (checkedSiteInfo != null) {
                    handleWebSocketInfo(it)
                }
            }
            siteListLiveData.observe(this@MultisiteActivity) {
                mBinding.siteRv.bindingAdapter.loadListSuccess(it, mBinding.siteRefresh)
                drawSiteMarker(it.list)
                mmkv.putString(ValueKey.SITE_INFO, it.list[0].toJsonStr())
                checkedSiteInfo = it.list[0]
                it.list.forEach { siteInfo ->
                    siteIDList.add(siteInfo.siteId)
                }
                if (siteIDList.isNotEmpty()) {
                    initSiteSocket()
                }
            }
        }
        operateModel.run {
            missionListLiveData.observe(this@MultisiteActivity) {
                if (it.list.isEmpty()) {
                    clearHistoryFlightPath()
                    // 当任务列表为空时，立即通知UI显示无任务状态
                    siteMissionListInfo.clear()
                    LiveDataEvent.missionInfoList.value = mutableListOf()
                    return@observe
                }
                if (isShowHistory) {
                    operateModel.getFlightHistoryList(
                        true,
                        subLocationId,
                        checkedSiteInfo?.siteId.toString(),
                        startTime,
                        endTime
                    )
                }
                // 获取每个任务的详细信息
                it?.list?.forEach { missionInfo ->
                    operateModel.getMissionInfo(missionInfo.missionId)
                }
            }
            historyListLiveData.observe(this@MultisiteActivity) {
                if (it.list.isNotEmpty()) {
                    takeHistoryMission(it.list)
                }
            }
            missionInfoLiveData.observe(this@MultisiteActivity) {
                if (it == null) {
                    Toaster.show("暂无任务数据")
                    return@observe
                }
                siteMissionListInfo.add(it)
                // 修复判断逻辑：确保当前子场站的所有任务信息都已加载完成
                val currentMissionList = missionListLiveData.value?.list
                if (currentMissionList != null && siteMissionListInfo.size == currentMissionList.size) {
                    // 验证所有任务ID都匹配，确保数据一致性
                    val currentMissionIds = currentMissionList.map { mission -> mission.missionId }.toSet()
                    val loadedMissionIds = siteMissionListInfo.map { mission -> mission.missionId }.toSet()

                    if (currentMissionIds == loadedMissionIds) {
                        LiveDataEvent.missionInfoList.value = siteMissionListInfo
                    }
                }
            }

            issueCommandLiveData.observe(this@MultisiteActivity) {
                responseToast(it)
            }
            settingLiveData.observe(this@MultisiteActivity) {
                responseToast(it)
            }
            siteInfoLiveData.observe(this@MultisiteActivity) {
                //uavFlvUrl 第一视角
                //siteFlvUrl 第三视角
                //hiveFlvUrl 内窥视角
                //hiveRbtFlvUrl 机臂视角
                if (checkedSiteInfo?.uavInfo?.uavFlvUrl.isNullOrEmpty()) {
                    checkedSiteInfo?.uavInfo?.uavFlvUrl = it.uavInfo?.uavFlvUrl
                }
                if (checkedSiteInfo?.siteLocation!!.isEmpty()) {
                    checkedSiteInfo?.siteLocation = it.siteLocation as MutableList<Double>
                }
                /*   mBinding.btnFpvToMap.run {
                       text = if (it.uavInfo?.uavFlvUrl.isNullOrEmpty()) {
                           "NA"
                       } else {
                           ""
                       }
                   }*/
                checkedSiteInfo?.let { info ->
                    updateSiteInfo(info)
                }
            }
            batchInfoLiveData.observe(this@MultisiteActivity) {
                // 初始化任务航线列表
                val missionPaths: MutableList<MissionInfoBean.FlightParams.FlightPath> = mutableListOf()
                when (it.missionType) {
                    "2" -> {
                        // 检查是否存在子任务列表，并且子任务列表不为空
                        if (it.missionInfo.flightParams.childrenList?.isNotEmpty() == true) {
                            // 如果有非空的子任务列表，从子任务中收集任务路径
                            missionPaths.addAll(it.missionInfo.flightParams.flightPath!!)
                        } else {
                            // 如果没有子任务列表，尝试从第一个子任务中获取任务航线
                            it.missionInfo.flightParams.childrenList?.get(0)?.let { batchChildren ->
                                missionPaths.addAll(batchChildren.flightPath)
                            }
                        }
                    }

                    else -> {
                        // 如果当前任务名为“指点任务”，直接从当前任务的飞行参数中收集任务路径
                        if (it.missionInfo.missionName == "指点任务") {
                            missionPaths.addAll(it.missionInfo.flightParams.flightPath!!)
                        } else {
                            // 没有子任务直接绘制任务航点
                            missionPaths.addAll(it.missionInfo.flightParams.flightPath!!)
                        }
                    }
                }
                // 初始化航点任务
                val waypointMission = WaypointMission()
                // 为航点任务设置批次标识，使用当前时间戳
                waypointMission.missionBatch = System.currentTimeMillis().toString()
                // 遍历任务航线，将每个航点的坐标数据添加到航点任务中
                missionPaths.forEach { flightPath ->
                    val wayPoint = WaypointMission.Waypoint()
                    wayPoint.latLng = AppLatLng(flightPath.latitude, flightPath.longitude)
                    waypointMission.addWaypoint(wayPoint)
                }
                // 根据航点任务绘制航点路径
                drawWaypoint(waypointMission)
                //如果正在执行任务则将已执行的坐标传入绘制航线路径
                if (it.flightRecords.list.isNotEmpty() && polyline == null) {
                    // 将历史飞行记录的坐标取出绘制到地图上
                    it.flightRecords.list.forEach { flightLat ->
                        val newLatLng = CoordinateConverterUtil.convertWGS84ToAMap(
                            LatLng(
                                flightLat.latitude,
                                flightLat.longitude
                            )
                        )
                        if (!flightRecords.contains(newLatLng)) {
                            flightRecords.add(newLatLng)
                        }
                    }
                    addPolyline(flightRecords)
                }
            }
        }
    }

    override fun onRequestError(loadStatus: LoadStatusEntity) {
        showDialogMessage("请求码：${loadStatus.requestCode}\n 错误码：${loadStatus.errorCode},错误信息：${loadStatus.errorMessage}")
    }

    private fun initMapView() {
        siteMap = mBinding.siteMap.map
        siteMap.mapType = AMap.MAP_TYPE_SATELLITE
        siteMap.uiSettings.isZoomControlsEnabled = false
        siteMap.isTrafficEnabled = false
        siteMap.showBuildings(false)
        lifecycleScope.launch(Dispatchers.Default) {
            siteMap.setOnCameraChangeListener(object : AMap.OnCameraChangeListener {
                override fun onCameraChange(cameraPositon: CameraPosition) {}

                override fun onCameraChangeFinish(cameraPositon: CameraPosition) {
                    if (subLocation != null) {
                        MapOverlayUtil.showMapOverlay(siteMap, subLocation, cameraPositon)
                    }
                }
            })
        }
    }

    private fun initMyLocation() {
        AMapLocationUtil.getLocation(this, object : AMapLocationUtil.OnLocationResultListener {
            override fun onLocationResult(lat: Double, lon: Double) {
                //在地图上设置定位点
                mBinding.siteMap.map?.addMarker(
                    MarkerOptions()
                        .position(LatLng(lat, lon))
                        .icon(
                            BitmapDescriptorFactory.fromBitmap(
                                BitmapFactory.decodeResource(
                                    resources,
                                    R.drawable.ic_rc_home
                                )
                            )
                        )
                )
                //将视角移动到当前位置
                mBinding.siteMap.map?.moveCamera(CameraUpdateFactory.newLatLngZoom(LatLng(lat, lon), 18f))
            }
        })
    }

    private fun initMapClick() {
        mBinding.siteMap.map?.setOnMapClickListener {
            if (isAddMarker) {
                //设置选点样式
                val markerOptions = MarkerOptions()
                    .position(it)
                    .icon(
                        BitmapDescriptorFactory.fromBitmap(
                            BitmapFactory.decodeResource(
                                resources,
                                R.drawable.ic_realtime_marker
                            )
                        )
                    )
                if (marker == null) {
                    marker = mBinding.siteMap.map?.addMarker(markerOptions)
                } else {
                    marker?.position = it
                }
                //将坐标发送至紧急任务
                LiveDataEvent.markerLocation.postValue(it)
            }
        }
    }

    /**
     * 初始化标记点击事件
     * 该函数设置地图视图的标记点击监听器，当用户点击某个标记时，如果该标记被指定为"currentMarker"，则移除该标记。
     * @无参数
     * @无返回值
     */
    private fun initMarkerClick() {
        mBinding.siteMap.map?.setOnMarkerClickListener { marker ->
            // 如果点击的标记是"currentMarker"，则移除该标记
            if (marker == this.marker) {
                marker.remove()
                this.marker = null
            }
            true // 总是返回true，表示已处理点击事件
        }
    }

    private fun initSiteList() {
        mBinding.siteRv.linear().setup {
            addType<SiteListBean.SiteItem>(R.layout.item_multisite_info_layout)
            R.id.multi_site_task_control.onClick {
                mBinding.drawerLayout.openDrawer(GravityCompat.END)
                mBinding.drawerLayout.setDrawerLockMode(LOCK_MODE_LOCKED_OPEN)
                val checked = getModel<SiteListBean.SiteItem>().isCheck
                setChecked(layoutPosition, !checked)
            }
            R.id.multi_site_item.onClick {
                val checked = getModel<SiteListBean.SiteItem>().isCheck
                setChecked(layoutPosition, !checked)
            }
            onChecked { position, checked, allChecked ->
                val model = getModel<SiteListBean.SiteItem>(position)
                model.isCheck = checked
                model.notifyChange()
                if (checked) {
                    if (model != checkedSiteInfo) {
                        isBatchInfo = true
                    }
                    checkedSiteInfo = model
                    if (model.siteLocation.isNotEmpty()) {
                        siteMap.moveCamera(
                            CameraUpdateFactory.newLatLngZoom(
                                CoordinateConverterUtil.convertWGS84ToAMap(
                                    LatLng(model.siteLocation[1], model.siteLocation[0])
                                ), 16f
                            )
                        )
                    }
                    //显示状态组件并更新数据
                    if (mBinding.widgetCompass.toolbarView.isGone()) {
                        mBinding.widgetCompass.toolbarView.visible()
                    }
                    if (mBinding.telemetryWidget.telemetryLayout.isGone()) {
                        mBinding.telemetryWidget.telemetryLayout.visible()
                    }
                    updateUavStateInfo(model)
                    operateModel.getSiteInfo(model.siteId)
                    if (model.subState in MISSION_TYPE && mFloatMenu != null) {
                        mFloatMenu?.show()
                    } else {
                        destroyFloat()
                    }
                    // 获取 UAV 信息并存储到局部变量中，避免重复调用
                    val uavInfo = DJIAircraftApplication.getInstance().getUavInfoSN()
                    if (uavInfo != null) {
                        // 确保 data 和 siteInfo 不为空
                        val siteID = uavInfo.data?.siteInfo?.siteID
                        if (siteID != null && siteID == model.siteId) {
                            isCurrentSite = true
                            updateSurfaceViewZOrder(mBinding.djiFpvWidget, isCurrentSite)
                        } else {
                            if (isSwapped) {
                                toggleFullScreen(mBinding.djiFpvWidget, mBinding.siteMap, mBinding.constraintLayout, 240, 138)
                            }
                        }
                    } else {
                        // 从本地存储中获取 UAV 序列号
                        val localUavSn = mmkv.getString(ValueKey.UAV_SN, "")
                        //确保 model.uavInfo?.sn 不为空
                        if (!localUavSn.isNullOrEmpty() && model.uavInfo?.sn == localUavSn) {
                            isCurrentSite = true
                            updateSurfaceViewZOrder(mBinding.djiFpvWidget, isCurrentSite)
                        }
                    }
                    // 切换站点时清空之前的任务数据，避免数据累积
                    siteMissionListInfo.clear()
                    // 立即清空全局任务信息，避免UI显示旧数据
                    LiveDataEvent.missionInfoList.value = mutableListOf()
                    operateModel.getAllMissionListInfo(
                        siteId = model.siteId,
                        subLocationId = subLocationId
                    )
                } else {
                    isBatchInfo = true
                }
            }
        }
    }

    // 提取公共逻辑到单独的函数中
    private fun updateSurfaceViewZOrder(widget: FPVWidget, isCurrentSite: Boolean) {
        widget.setSurfaceViewZOrderOnTop(isCurrentSite)
        widget.setSurfaceViewZOrderMediaOverlay(isCurrentSite)
    }

    private fun initSiteSocket() {
        val socketIOConfig: SocketIOConfig = SpUtil.getLoginCache().socketIOConfig
        val query = String.format(
            "accessKeyId=%s&accessKeySecret=%s",
            socketIOConfig.accessKeyId,
            socketIOConfig.accessKeySecret
        )
        val siteSocketUrl = String.format("%s/user", socketIOConfig.IPAddress)
        mViewModel.connectSiteSocket(siteSocketUrl, query)
    }

    private fun initMissionPanel() {
        //设置viewpage不可滑动
        mBinding.settingViewpager.setPagingEnabled(false)
        //设置viewpage预加载页面数量
        mBinding.settingViewpager.offscreenPageLimit = 6
        //创建并设置MissionMenuAdapter适配器
        missionMenuAdapter = MissionMenuAdapter(supportFragmentManager, menuList, fragmentList)
        mBinding.settingViewpager.adapter = missionMenuAdapter
        //将VerticalTabLayout与Viewpage关联
        mBinding.missionVerticalTabLayout.setupWithViewPager(mBinding.settingViewpager)
        //设置VerticalTabLayout的tab点击监听
        mBinding.missionVerticalTabLayout.setOnTabSelectedListener { tab, position ->
            mBinding.settingViewpager.setCurrentItem(position, true)
        }
    }

    /**
     * 初始化视频播放器。
     * 该函数创建并配置两个视频播放器视图，每个视图都包含自己的视频构建器和控制器。
     * 两个播放器都禁用了音频焦点，并添加了自定义错误视图和直播控制条作为控制组件。
     */
    private fun initVideoPlayer() {
        // 创建第一个视频播放器的构建器，并配置其属性
        val firstBuilder = VideoPlayerBuilder.newBuilder()
        firstBuilder.setEnableAudioFocus(false)
        val videoPlayerBuilder = VideoPlayerBuilder(firstBuilder)
        mBinding.firstFpvWidget.setVideoBuilder(videoPlayerBuilder)

        // 设置第一个视频播放器的控制器，并配置其行为
        firstController = BasisVideoController(this)
        firstController.removeAllControlComponent()
        firstController.setEnableOrientation(false)
        firstController.addControlComponent(CustomErrorView(this))
        firstController.addControlComponent(LiveControlView(this))//直播控制条
        mBinding.firstFpvWidget.setController(firstController)
        mVideoViews.add(mBinding.firstFpvWidget)

        // 创建第二个视频播放器的构建器，并配置其属性
        val secondBuilder = VideoPlayerBuilder.newBuilder()
        secondBuilder.setEnableAudioFocus(false)
        val videoPlayerBuilder2 = VideoPlayerBuilder(secondBuilder)
        mBinding.secondFpvWidget.setVideoBuilder(videoPlayerBuilder2)

        // 设置第二个视频播放器的控制器，并配置其行为
        secondController = BasisVideoController(this)
        secondController.removeAllControlComponent()
        secondController.setEnableOrientation(false)
        secondController.addControlComponent(CustomErrorView(this))
        secondController.addControlComponent(LiveControlView(this))//直播控制条
        mBinding.secondFpvWidget.setController(secondController)
        mVideoViews.add(mBinding.secondFpvWidget)
    }

    private fun initVideoSwitch() {
        mBinding.secondFpvSwitch.setText("第三视角")
        mBinding.secondFpvSwitch.setOnItemClickListener { parent, view, position, id ->
            when (position) {
                0 -> {
                    mBinding.secondFpvWidget.release()
                    mBinding.secondFpvWidget.url = operateModel.siteInfoLiveData.value?.siteFlvUrl
                    mBinding.secondFpvWidget.setController(secondController)
                    mBinding.secondFpvWidget.start()
                }

                1 -> {
                    mBinding.secondFpvWidget.release()
                    mBinding.secondFpvWidget.url =
                        operateModel.siteInfoLiveData.value?.hiveInfo?.hiveFlvUrl
                    mBinding.secondFpvWidget.setController(secondController)
                    mBinding.secondFpvWidget.start()
                }

                2 -> {
                    mBinding.secondFpvWidget.release()
                    mBinding.secondFpvWidget.url =
                        operateModel.siteInfoLiveData.value?.hiveInfo?.hiveRbtFlvUrl
                    mBinding.secondFpvWidget.setController(secondController)
                    mBinding.secondFpvWidget.start()
                }
            }
        }
    }

    //初始化UI动画
    private fun initAnimation() {
        // 向左位移显示动画，从自身位置的最右端向左滑动自身宽度的距离
        translateRightAniShow = TranslateAnimation(
            Animation.RELATIVE_TO_SELF,
            1f,
            Animation.RELATIVE_TO_SELF,
            0f,
            Animation.RELATIVE_TO_SELF,
            0f,
            Animation.RELATIVE_TO_SELF,
            0f
        )
        translateRightAniShow?.repeatMode = Animation.REVERSE
        translateRightAniShow?.duration = 200
        // 向右位移隐藏动画，从自身位置的最左端向右滑动自身宽度的距离
        translateRightAniHide = TranslateAnimation(
            Animation.RELATIVE_TO_SELF,
            0f,
            Animation.RELATIVE_TO_SELF,
            1f,
            Animation.RELATIVE_TO_SELF,
            0f,
            Animation.RELATIVE_TO_SELF,
            0f
        )
        translateRightAniHide?.repeatMode = Animation.REVERSE
        translateRightAniHide?.duration = 200
    }

    private fun initMenuData() {
        MENU_ICONS.forEachIndexed { index, i ->
            floatItemList.add(
                FloatItem(
                    MENU_ITEMS[index],
                    getColorExt(R.color.white),
                    getColorExt(R.color.transparent_black),
                    drawableToBitmap(MENU_ICONS[index]),
                    "${index + 1}"
                )
            )
        }
    }


    //配置控制按钮
    private fun initControlBtn() {
        if (mFloatMenu == null) {
            mFloatMenu = FloatLogoMenu.Builder()
                .withActivity(this)
                .logo(drawableToBitmap(R.drawable.multi_site_control_ic))
                .drawCicleMenuBg(false)
                .backMenuColor(getColorExt(R.color.transparent_black))
                .setBgDrawable(ContextCompat.getDrawable(this, R.drawable.bg_notification))
                .setFloatItems(floatItemList)
                .defaultLocation(FloatLogoMenu.LEFT)
                .showWithListener(object : FloatMenuView.OnMenuClickListener {
                    override fun onItemClick(position: Int, title: String?) {
                        when (position) {
                            0 -> {
                                showDialogMessage(
                                    "是否暂停当前任务",
                                    positiveButtonText = "确认",
                                    positiveAction = {
                                        operateModel.missionSetting(
                                            checkedSiteInfo?.uavInfo?.uavId.toString(),
                                            PAUSE_CODE
                                        )
                                    },
                                    negativeButtonText = "取消",
                                    isVisible = true
                                )
                            }

                            1 -> {
                                showDialogMessage(
                                    "是否停止当前任务",
                                    positiveButtonText = "确认",
                                    positiveAction = {
                                        operateModel.missionSetting(
                                            checkedSiteInfo?.uavInfo?.uavId.toString(),
                                            STOP_CODE
                                        )
                                    },
                                    negativeButtonText = "取消",
                                    isVisible = true
                                )
                            }

                            2 -> {
                                showDialogMessage(
                                    "是否恢复当前任务",
                                    positiveButtonText = "确认",
                                    positiveAction = {
                                        operateModel.missionSetting(
                                            checkedSiteInfo?.uavInfo?.uavId.toString(),
                                            RECOVER_CODE
                                        )
                                    },
                                    negativeButtonText = "取消",
                                    isVisible = true
                                )
                            }

                            3 -> {
                                showDialogMessage(
                                    "是否返航至起始点",
                                    positiveButtonText = "确认",
                                    positiveAction = {
                                        if (checkedSiteInfo?.subState == 102) {
                                            "无人机未执行任务".toast()
                                            return@showDialogMessage
                                        }
                                        operateModel.missionSetting(
                                            checkedSiteInfo?.uavInfo?.uavId.toString(),
                                            RETURN_CODE
                                        )
                                    },
                                    negativeButtonText = "取消",
                                    isVisible = true
                                )
                            }

                            4 -> {
                                showDialogMessage(
                                    "是否紧急停止当前任务",
                                    positiveButtonText = "确认",
                                    positiveAction = {
                                        operateModel.missionSetting(
                                            checkedSiteInfo?.uavInfo?.uavId.toString(),
                                            INS_STOP_CODE
                                        )
                                    },
                                    negativeButtonText = "取消",
                                    isVisible = true
                                )
                            }

                            5 -> {
                                operateModel.cameraSetting(
                                    checkedSiteInfo?.uavInfo?.uavId.toString(),
                                    type = TAKE_PICTURE_CODE
                                )
                            }

                            6 -> {
                                if (isRecording == 1) {
                                    operateModel.cameraSetting(
                                        checkedSiteInfo?.uavInfo?.uavId.toString(),
                                        type = STOP_VIDEO_CODE
                                    )
                                } else {
                                    operateModel.cameraSetting(
                                        checkedSiteInfo?.uavInfo?.uavId.toString(),
                                        type = TAKE_VIDEO_CODE
                                    )
                                }
                            }
                        }
                    }

                    override fun dismiss() {}
                })

        }
    }

    //从缓存中取出所有场站数据
    private fun getAllLocation() {
        val locationInfoStr = mmkv.getString(ValueKey.LOCATION_LIST_INFO, "")
        if (locationInfoStr?.isEmpty() != true) {
            locationInfoBean = JsonUtil.toBean(locationInfoStr, LocationInfoBean::class.java)
        }
        locationInfoBean.mainLocationList.flatMap {
            it.subLocationList
        }.forEach { subLocation ->
            subLocationList.add(subLocation)
        }
        if(subLocationList.isEmpty()) {
            Toaster.show("暂无场站信息")
            return
        }
        this.subLocationId = subLocationList[0].id
        subLocation = subLocationList[0]
    }

    //显示子场站列表
    private fun showSitePopup() {
        XPopup.Builder(this)
            .isViewMode(true)
            .hasStatusBar(true)
            .popupWidth(resources.displayMetrics.widthPixels / 3)
            .offsetX(mBinding.btnSiteChoose.width + 20)
            .offsetY(-mBinding.btnSiteChoose.height)
            .atView(mBinding.btnSiteChoose)
            .asCustom(
                SiteLocationPopup(
                    this,
                    subLocationList,
                    object : SiteLocationPopup.OnItemClickListener {
                        override fun onItemClick(item: LocationInfoBean.MainLocation.SubLocation) {
                            lifecycleScope.launch(Dispatchers.IO) {
                                clearSubMarkers()
                                // 切换子场站时清空之前的任务数据，避免数据累积
                                siteMissionListInfo.clear()
                                // 立即清空全局任务信息，避免UI显示旧数据
                                withContext(Dispatchers.Main) {
                                    LiveDataEvent.missionInfoList.value = mutableListOf()
                                }
                                mViewModel.getSiteAreaList(mutableListOf(item.id))
                                operateModel.getAllMissionListInfo(siteId = checkedSiteInfo?.siteId.toString(), subLocationId = item.id)
                                subLocation = item
                                withContext(Dispatchers.Main) {
                                    mBinding.btnSiteChoose.text = item.location
                                    mBinding.btnSiteChoose.compoundDrawablePadding = TypedValue.applyDimension(TypedValue.COMPLEX_UNIT_DIP, 10f, resources.displayMetrics).toInt()
                                }
                                subLocationId = item.id
                                val newLatLng = GCJ02_WGS84.wgs84_To_Gcj02(item.locationLatitude, item.locationLongitude)
                                siteMap.moveCamera(CameraUpdateFactory.newLatLngZoom(LatLng(newLatLng.latitude, newLatLng.longitude), 16f))
                            }
                        }
                    })
            )
            .show()
    }

    /**
     * 是否显示历史飞行区域
     */
    private fun showHistoryFlightPath() {
        XPopup.Builder(this)
            .isViewMode(true)
            .hasStatusBar(false)
            .hasShadowBg(false)
            .isDestroyOnDismiss(true)
            .popupWidth(resources.displayMetrics.widthPixels / 3)
            .atView(mBinding.btnHistorySwitch)
            .asCustom(
                HistoryFlightAreaPopup(
                    this,
                    object : HistoryFlightAreaPopup.OnOperateClickListener {
                        override fun onShowEvent(position: Int, isVisible: Boolean) {
                            if (!isVisible) {
                                clearHistoryFlightPath()
                            } else {
                                startTime = getTimeAgoStartOfDay(position)
                                if (<EMAIL> != -1) {
                                    operateModel.getFlightHistoryList(
                                        true,
                                        <EMAIL>,
                                        checkedSiteInfo?.siteId,
                                        startTime,
                                        endTime
                                    )
                                }
                            }
                        }

                    })
            )
            .show()
    }

    private fun clearHistoryFlightPath() {
        allHistoryFlightPath.forEach { polyline ->
            polyline.remove()
        }
        allHistoryFlightPath.clear()
    }

    override fun onResume() {
        super.onResume()
        mBinding.siteMap.onResume()
    }

    override fun onPause() {
        super.onPause()
        destroyFloat()
        mBinding.siteMap.onPause()
    }

    override fun onLowMemory() {
        super.onLowMemory()
        mBinding.siteMap.onLowMemory()
    }

    override fun onBackPressed() {
        if (mBinding.drawerLayout.isDrawerOpen(GravityCompat.END)) {
            mBinding.drawerLayout.closeDrawers()
        } else {
            mVideoViews.forEach { vp ->
                if (vp.onBackPressed()) return
            }
            super.onBackPressed()
        }
    }

    override fun onDestroy() {
        destroyFloat()
        super.onDestroy()
        mBinding.siteMap.map.removeOnMapLoadedListener { mBinding.siteMap.map.clear() }
        mBinding.siteMap.onDestroy()
        mViewModel.cancelSocket()
        stopSiteStateTimer()
        missionMenuAdapter.destroy()
        LiveDataEvent.clearData()
        MapOverlayUtil.clearMapOverlay()
    }

    private fun toggleFullScreen(targetView: View?, secondaryView: View?, parentLayout: ConstraintLayout?, originalWidth: Int, originalHeight: Int) {
        requireNotNull(targetView) { "targetView must not be null" }
        requireNotNull(secondaryView) { "secondaryView must not be null" }
        requireNotNull(parentLayout) { "parentLayout must not be null" }
        require(originalWidth > 0 && originalHeight > 0) { "originalWidth and originalHeight must be positive values" }

        // 预先转换尺寸，避免重复计算
        val originalWidthPx = DensityUtil.dip2px(appContext, originalWidth.toFloat())
        val originalHeightPx = DensityUtil.dip2px(appContext, originalHeight.toFloat())

        if (!isSwapped) {
            animateExpand(targetView, secondaryView, parentLayout, originalWidthPx, originalHeightPx)
        } else {
            animateRestore(targetView, secondaryView, parentLayout, originalWidthPx, originalHeightPx)
        }
    }

    private fun animateExpand(targetView: View, secondaryView: View, parentLayout: ConstraintLayout, originalWidthPx: Int, originalHeightPx: Int) {
        // 第一阶段：对 targetView 执行全屏扩展动画
        val transition = AutoTransition().apply {
            duration = DEFAULT_ANIM_DURATION
            interpolator = AccelerateDecelerateInterpolator()
        }
        transition.addListener(object : TransitionListenerAdapter() {
            override fun onTransitionEnd(transition: Transition) {
                transition.removeListener(this)
                // 动画结束后，直接更新 secondaryView 的约束，无动画效果
                parentLayout.post {
                    val constraintSet = ConstraintSet().apply {
                        clone(parentLayout)
                        // 清除顶部和右侧约束
                        clear(secondaryView.id, ConstraintSet.TOP)
                        clear(secondaryView.id, ConstraintSet.END)
                        // 定位到左下角
                        connect(secondaryView.id, ConstraintSet.START, ConstraintSet.PARENT_ID, ConstraintSet.START)
                        connect(secondaryView.id, ConstraintSet.BOTTOM, ConstraintSet.PARENT_ID, ConstraintSet.BOTTOM)
                        // 固定尺寸
                        constrainWidth(secondaryView.id, originalWidthPx)
                        constrainHeight(secondaryView.id, originalHeightPx)
                        // 调整层级：targetView 全屏显示，放到底层；secondaryView 显示在前
                        setElevation(targetView.id, ELEVATION_FULL_SCREEN)
                        setElevation(secondaryView.id, ELEVATION_FOREGROUND)
                    }
                    // 直接更新，无过渡动画
                    constraintSet.applyTo(parentLayout)
                    isSwapped = true
                }
                if (targetView is FPVWidget) {
                    targetView.setSurfaceViewZOrderMediaOverlay(false)
                    targetView.setSurfaceViewZOrderOnTop(false)
                }
            }
        })
        val constraintSet1 = ConstraintSet().apply {
            clone(parentLayout)
            // 设置 targetView 全屏约束
            connect(targetView.id, ConstraintSet.TOP, ConstraintSet.PARENT_ID, ConstraintSet.TOP)
            connect(targetView.id, ConstraintSet.BOTTOM, ConstraintSet.PARENT_ID, ConstraintSet.BOTTOM)
            connect(targetView.id, ConstraintSet.START, ConstraintSet.PARENT_ID, ConstraintSet.START)
            connect(targetView.id, ConstraintSet.END, ConstraintSet.PARENT_ID, ConstraintSet.END)
            constrainWidth(targetView.id, ConstraintSet.MATCH_CONSTRAINT)
            constrainHeight(targetView.id, ConstraintSet.MATCH_CONSTRAINT)
        }
        TransitionManager.beginDelayedTransition(parentLayout, transition)
        constraintSet1.applyTo(parentLayout)
    }

    private fun animateRestore(targetView: View, secondaryView: View, parentLayout: ConstraintLayout, originalWidthPx: Int, originalHeightPx: Int) {
        // 第一阶段：对 secondaryView 执行全屏扩展动画
        val transition = AutoTransition().apply {
            duration = DEFAULT_ANIM_DURATION
            interpolator = AccelerateDecelerateInterpolator()
        }
        transition.addListener(object : TransitionListenerAdapter() {
            override fun onTransitionEnd(transition: Transition) {
                transition.removeListener(this)
                // 动画结束后，直接更新 targetView 的约束，无动画效果
                parentLayout.post {
                    val constraintSet = ConstraintSet().apply {
                        clone(parentLayout)
                        // 清除顶部和右侧约束
                        clear(targetView.id, ConstraintSet.TOP)
                        clear(targetView.id, ConstraintSet.END)
                        // 定位到左下角
                        connect(targetView.id, ConstraintSet.START, ConstraintSet.PARENT_ID, ConstraintSet.START)
                        connect(targetView.id, ConstraintSet.BOTTOM, ConstraintSet.PARENT_ID, ConstraintSet.BOTTOM)
                        // 固定尺寸
                        constrainWidth(targetView.id, originalWidthPx)
                        constrainHeight(targetView.id, originalHeightPx)
                        // 调整层级：secondaryView 全屏显示，放到底层；targetView 显示在前
                        setElevation(targetView.id, ELEVATION_FOREGROUND)
                        setElevation(secondaryView.id, ELEVATION_FULL_SCREEN)
                    }
                    constraintSet.applyTo(parentLayout)
                    isSwapped = false
                }
                if (targetView is FPVWidget) {
                    targetView.setSurfaceViewZOrderMediaOverlay(true)
                    targetView.setSurfaceViewZOrderOnTop(false)
                }
            }
        })
        val constraintSet1 = ConstraintSet().apply {
            clone(parentLayout)
            // 设置 secondaryView 全屏约束
            connect(secondaryView.id, ConstraintSet.TOP, ConstraintSet.PARENT_ID, ConstraintSet.TOP)
            connect(secondaryView.id, ConstraintSet.BOTTOM, ConstraintSet.PARENT_ID, ConstraintSet.BOTTOM)
            connect(secondaryView.id, ConstraintSet.START, ConstraintSet.PARENT_ID, ConstraintSet.START)
            connect(secondaryView.id, ConstraintSet.END, ConstraintSet.PARENT_ID, ConstraintSet.END)
            constrainWidth(secondaryView.id, ConstraintSet.MATCH_CONSTRAINT)
            constrainHeight(secondaryView.id, ConstraintSet.MATCH_CONSTRAINT)
        }
        TransitionManager.beginDelayedTransition(parentLayout, transition)
        constraintSet1.applyTo(parentLayout)
    }

    private fun destroyFloat() {
        if (mFloatMenu != null) {
            mFloatMenu?.destroyFloat()
        }
        mFloatMenu = null
    }


    /**
     * 绘制航线
     * @param waypointMission WaypointMission
     */
    private fun drawWaypoint(waypointMission: WaypointMission) {
        markerManager.clearAll()
        val latLngList: MutableList<LatLng> = mutableListOf()
        waypointMission.waypointList.forEach { waypoint ->
            latLngList.add(
                CoordinateConverterUtil.convertWGS84ToAMap(
                    LatLng(
                        waypoint.latLng.lat,
                        waypoint.latLng.lng
                    )
                )
            )
        }
        markerManager.addMarker(latLngList)
    }

    /**
     * 添加无人机marker并将地图中心移动到无人机上
     * @param latLng LatLng ic_gimbal_yaw
     */
    private fun addUavMarker(latLng: LatLng) {
        val inflater = LayoutInflater.from(appContext)
        val markerView = inflater.inflate(R.layout.show_map_uav_layout, null)
        val markerOptions = MarkerOptions()
            .position(latLng)
            .icon(BitmapDescriptorFactory.fromView(markerView))
            .anchor(0.5f, 0.5f)
            .setFlat(true)
            .zIndex(AIRCRAFT_MARKER_ELEVATION)
            .rotateAngle(((360 - yawAngleValue) % 360).toFloat())
        uavMarker = siteMap.addMarker(markerOptions)
    }

    /**
     * 无人机飞行路径
     * @param uavPoint LatLng
     */
    private fun addPolyline(uavPoint: MutableList<LatLng>) {
        val polyOptions = PolylineOptions()
            .addAll(uavPoint)
            .width(10f)
            .zIndex(PATH_LINE_ELEVATION)
            .color(ContextCompat.getColor(appContext, R.color.green))
        polyline = siteMap.addPolyline(polyOptions)
    }

    /**
     * 绘制从起点到无人机的连线
     * @param uavLatLng LatLng
     */
    private fun addStartToUav(toPosition: LatLng) {
        val polyOptions = PolylineOptions()
            .add(startPoint, toPosition)
            .width(10f)
            .zIndex(START_TO_UAV_LINE_ELEVATION)
            .color(ContextCompat.getColor(appContext, R.color.red))
        startToUavLine = siteMap.addPolyline(polyOptions)
    }

    /**
     * 对无人机位置进行动画处理
     * @param toPosition LatLng
     * @param fromPosition LatLng
     */
    private fun animateAircraftMarker(toPosition: LatLng, fromPosition: LatLng) {
        val flightAnimation = ValueAnimator.ofFloat(0f, 1f)
        flightAnimation.setDuration(FLIGHT_ANIM_DURATION)
        flightAnimation.interpolator = LinearInterpolator()
        flightAnimation.addUpdateListener { valueAnimator ->
            val progress = valueAnimator.animatedFraction
            val latitude =
                (toPosition.latitude - fromPosition.latitude) * progress + fromPosition.latitude
            val longitude =
                (toPosition.longitude - fromPosition.longitude) * progress + fromPosition.longitude
            val aircraftLatLng = LatLng(latitude, longitude)
            if (DJIGpsUtils.isAvailable(aircraftLatLng.latitude, aircraftLatLng.longitude)) {
                uavMarker?.position = aircraftLatLng
                uavMarker?.rotateAngle = ((360 - yawAngleValue) % 360).toFloat()
            }
        }
        flightAnimation.start()
    }


    private fun updateUavStateInfo(siteItem: SiteListBean.SiteItem) {
        mmkv.putString(ValueKey.UAV_ID, siteItem.uavInfo?.uavId)
        mmkv.putString(ValueKey.SITE_ID, siteItem.siteId)
        mmkv.putString(ValueKey.SITE_INFO, siteItem.toJsonStr())
        val listItem = ListItem()
        listItem.UAVInfo.UAVID = if (siteItem.uavInfo != null) siteItem.uavInfo?.uavId else ""
        listItem.siteID = siteItem.siteId
        listItem.siteName = siteItem.siteName
        /*  message.obj = listItem
          EventBus.getDefault().postSticky(message)*/
        LiveDataEvent.uavInfo.postValue(listItem)
        when (siteItem.subState) {
            0 -> {
                updateUavOfflineStateInfo()
                mBinding.widgetCompass.tvUavStatus.run {
                    text = SiteState.getByCode(siteItem.subState).value
                    setTextColor(getColor(R.color.multi_site_lx_color))
                    setCompoundDrawablesRelativeWithIntrinsicBounds(
                        ContextCompat.getDrawable(
                            this@MultisiteActivity,
                            R.drawable.ic_realtime_point_off
                        ), null, null, null
                    )
                }
            }

            else -> {
                mBinding.widgetCompass.tvTaskStatus.visible()
                mBinding.widgetCompass.tvTaskStatus.run {
                    text = SiteState.getByCode(checkedSiteInfo!!.subState).value
                    if (checkedSiteInfo?.subState == 102) {
                        setTextColor(getColor(R.color.multi_site_dm_color))
                    } else {
                        setTextColor(getColor(R.color.multi_site_qt_color))
                    }
                }

                mBinding.widgetCompass.tvUavStatus.run {
                    text = "在线"
                    setTextColor(getColor(R.color.multi_site_dm_color))
                    setCompoundDrawablesRelativeWithIntrinsicBounds(
                        ContextCompat.getDrawable(
                            this@MultisiteActivity,
                            R.drawable.ic_realtime_point_on
                        ), null, null, null
                    )
                }
            }
        }
    }

    private fun updateSiteInfo(siteInfoBean: SiteListBean.SiteItem) {
        mmkv.putString(ValueKey.SITE_INFO, JsonUtil.toJson(siteInfoBean))
        //通过站点类型来决定具体显示哪些视角
        when (siteInfoBean.siteMode) {
            1, 2 -> {
                if (mBinding.btnSiteMonitoring.isGone()) {
                    mBinding.btnSiteMonitoring.visible()
                }
                if (HIVE_MODE.contains(siteInfoBean.hiveInfo?.hiveModel)) {
                    mBinding.secondFpvSwitch.gone()
                } else {
                    mBinding.secondFpvSwitch.visible()
                }
                mBinding.scSiteMonitoring.isChecked = mBinding.conSecondFpv.isVisible()
                mmkv.putString(ValueKey.HIVE_ID, siteInfoBean.hiveInfo?.hiveId)
                mmkv.putBoolean(ValueKey.DEVICE_TYPE, true)
                updateFPVInfo(siteInfoBean)
            }

            3, 4, 5 -> {
                if (mBinding.btnSiteMonitoring.isVisible()) {
                    mBinding.conSecondFpv.gone()
                    mBinding.btnSiteMonitoring.gone()
                    mBinding.scSiteMonitoring.gone()
                }
                mmkv.putString(ValueKey.HIVE_ID, "")
                mmkv.putBoolean(ValueKey.DEVICE_TYPE, false)
            }
        }
        if (isCurrentSite) {
            if (mBinding.djiFpvWidget.isGone()) {
                updateAvailableCamera(cameraSourceList)
                mBinding.firstFpvWidget.release()
                mBinding.btnSwitch.visible()
            }
        } else {
            if (mBinding.firstFpvWidget.isGone() && (siteInfoBean.subState in MISSION_TYPE)) {
                mBinding.firstFpvWidget.visible()
                mBinding.btnSwitch.visible()
                mBinding.djiFpvWidget.gone()
                //第一视角
                mBinding.firstFpvWidget.release()
                mBinding.firstFpvWidget.url = siteInfoBean.uavInfo?.uavFlvUrl
                mBinding.firstFpvWidget.setController(firstController)
                mBinding.firstFpvWidget.start()
            } else {
                if (mBinding.djiFpvWidget.isVisible()) {
                    mBinding.djiFpvWidget.gone()
                }
                mBinding.firstFpvWidget.release()
                mBinding.firstFpvWidget.gone()
                mBinding.btnSwitch.gone()
            }
        }
        //获取站点坐标将地图中心移动到站点所在位置
        startPoint = CoordinateConverterUtil.convertWGS84ToAMap(
            LatLng(
                siteInfoBean.siteLocation[1],
                siteInfoBean.siteLocation[0]
            )
        )
    }

    private fun updateFPVInfo(siteInfoBean: SiteListBean.SiteItem) {
        //第二视角
        when (mBinding.secondFpvSwitch.text) {
            "第三视角" -> {
                mBinding.secondFpvWidget.release()
                mBinding.secondFpvWidget.url = siteInfoBean.siteFlvUrl
                mBinding.secondFpvWidget.setController(secondController)
                mBinding.secondFpvWidget.start()
            }

            "内窥视角" -> {
                mBinding.secondFpvWidget.release()
                mBinding.secondFpvWidget.url = siteInfoBean.hiveInfo?.hiveFlvUrl
                mBinding.secondFpvWidget.setController(secondController)
                mBinding.secondFpvWidget.start()
            }

            "机械臂视角" -> {
                mBinding.secondFpvWidget.release()
                mBinding.secondFpvWidget.url = siteInfoBean.hiveInfo?.hiveRbtFlvUrl
                mBinding.secondFpvWidget.setController(secondController)
                mBinding.secondFpvWidget.start()
            }
        }
    }

    //无人机离线状态下数据
    private fun updateUavOfflineStateInfo() {
        //偏航角
        mBinding.widgetCompass.tvYawAngle.text = getString(R.string.yaw_angle_str, "0")
        //GPS等级
        mBinding.widgetCompass.tvGpsLevel.text = getString(R.string.gps_level_str, "0")
        //上行信号
        mBinding.widgetCompass.tvUpSignal.text = getString(R.string.up_signal_str, 0)
        //下行信号
        mBinding.widgetCompass.tvDownSignal.text = getString(R.string.down_signal_str, 0)
        //云台俯仰角
        mBinding.widgetCompass.tvGimbalPitch.text = getString(R.string.gimbal_pitch_str, 0f)
        mBinding.widgetCompass.tvTaskStatus.gone()
        //实时高度
        mBinding.telemetryWidget.altitudeWidgetValue.text = "N/A"
        //垂直速度
        mBinding.telemetryWidget.verticalSpeedValue.text = "N/A"
        //水平速度
        mBinding.telemetryWidget.horizontalSpeedValue.text = "N/A"
        //距离终点
        mBinding.telemetryWidget.distanceWidgetValue.text = "N/A"
        //实时坐标
        mBinding.telemetryWidget.locationWidgetValue.text = "N/A"
    }


    /**
     * 处理websocket消息
     * @param jsonObject JSONObject
     */
    private fun handleWebSocketInfo(jsonObject: JSONObject) {
        if (jsonObject.optInt("type") != 1) return
        if (jsonObject.optString("STID") != checkedSiteInfo?.siteId) return
        missionBatch = jsonObject.optString("missionBatch")
        LiveDataEvent.socketData.postValue(jsonObject)
        updateUI(jsonObject)
    }

    //根据websocket更新站点状态
    private fun updateSiteState(jsonInfo: JSONObject) {
        if (mBinding.siteRv.bindingAdapter.models == null) return
        val siteModel = mBinding.siteRv.bindingAdapter.models as ArrayList<SiteListBean.SiteItem>
        // 使用 getObject 和类型安全地获取字段值
        val siteId = jsonInfo.optString("STID")
        val type = jsonInfo.optInt("type")
        var stateUpdated = false
        siteModel.forEach { siteInfo ->
            if (siteInfo.siteId == siteId) {
                when (type) {
                    EventType.UavType.value -> {
                        val isUAVOnline = jsonInfo.optInt("isUAVOnline")
                        val incomingSubState = jsonInfo.optInt("subState")
                        siteInfo.setNewIsUAVOnline(isUAVOnline)
                        siteInfo.setNewSubState(incomingSubState)
                    }

                    EventType.SiteType.value -> {
                        siteInfo.setNewIsHangarOnline(1)
                    }
                }
                stateUpdated = true
            }
        }
        if (stateUpdated) {
            resetSiteStateTimer(siteId)
        }
    }

    // 为每个站点启动或重置定时器
    private fun resetSiteStateTimer(siteId: String) {
        timers[siteId]?.cancel()
        val task = timerTask {
            updateSiteToOffline(siteId)
        }
        timers[siteId] = task
        try {
            timer.schedule(task, timeout)
        } catch (e: IllegalStateException) {
            // 捕获定时器异常并重建定时器
            timer = Timer()
            timer.schedule(task, timeout)
        }
    }

    // 更新特定站点为离线状态
    private fun updateSiteToOffline(siteId: String) {
        if (mBinding.siteRv.bindingAdapter.models == null) return
        val siteModel = mBinding.siteRv.bindingAdapter.models as ArrayList<SiteListBean.SiteItem>
        siteModel.forEach { siteInfo ->
            if (siteInfo.siteId == siteId) {
                siteInfo.setNewSubState(0) // 0 表示离线状态
                siteInfo.setNewIsUAVOnline(0)
                siteInfo.setNewIsHangarOnline(0)
                clearAllCurrentTaskMarkers()
                lifecycleScope.launch(Dispatchers.Main) {
                    updateUavOfflineStateInfo()
                    mBinding.widgetCompass.tvTaskStatus.gone()
                    mBinding.widgetCompass.tvUavStatus.run {
                        text = SiteState.getByCode(siteInfo.subState).value
                        setTextColor(getColor(R.color.multi_site_lx_color))
                        setCompoundDrawablesRelativeWithIntrinsicBounds(
                            ContextCompat.getDrawable(
                                this@MultisiteActivity,
                                R.drawable.ic_realtime_point_off
                            ), null, null, null
                        )
                    }
                }
            }
        }
        println("站点 $siteId 已更新为离线状态")
        // 检查是否所有站点都已离线，如果是，则停止所有定时器
        checkAllSitesOffline()
    }

    // 检查所有站点是否都为离线状态
    private fun checkAllSitesOffline() {
        if (mBinding.siteRv.bindingAdapter.models == null) return
        val siteModel = mBinding.siteRv.bindingAdapter.models as ArrayList<SiteListBean.SiteItem>
        val allOffline = siteModel.all { it.subState == 0 }
        if (allOffline) {
            stopSiteStateTimer()
            println("所有站点已离线，定时器已关闭")
        }
    }

    // 在需要时调用以释放资源
    private fun stopSiteStateTimer() {
        timers.values.forEach { it.cancel() }
        timers.clear()
        timer.cancel()
    }

    //根据接口返回的坐标，将所有场站的坐标点绘制在地图上
    private fun drawSiteMarker(siteList: MutableList<SiteListBean.SiteItem>) {
        siteList.forEach { siteInfo ->
            if (siteInfo.siteLocation.isNotEmpty()) {
                val siteGpsInfo = LatLng(siteInfo.siteLocation[1], siteInfo.siteLocation[0])
                val multiPointItem =
                    MultiPointItem(CoordinateConverterUtil.convertWGS84ToAMap(siteGpsInfo))
                multiPointItem.`object` = siteInfo
                multiPointList.add(multiPointItem)
            }
        }
        val bitmapDescriptor = BitmapDescriptorFactory.fromBitmap(
            BitmapFactory.decodeResource(
                resources,
                R.drawable.multi_site_uav_point_ic
            )
        )
        val overlayOptions = MultiPointOverlayOptions()
        overlayOptions.icon(bitmapDescriptor)
        overlayOptions.anchor(0.5f, 0.5f)
        val multiPointOverlay = siteMap.addMultiPointOverlay(overlayOptions)
        if (multiPointOverlay != null) {
            multiPointOverlay.items = multiPointList
            multiPointOverlay.setEnable(true)
        }
    }

    private fun updateInfoView(isVisible: Boolean) {
        if (isVisible) {
            mBinding.linearSiteList.startAnimation(translateRightAniShow)
            mBinding.linearSiteList.visible()
            mBinding.btnDrawerHandle.rotation = 0f
        } else {
            mBinding.linearSiteList.startAnimation(translateRightAniHide)
            mBinding.linearSiteList.gone()
            mBinding.btnDrawerHandle.rotation = 180f
        }
    }

    /**
     * UI更新
     * @param jsonObject JSONObject
     */
    private fun updateUI(jsonObject: JSONObject) {
        lifecycleScope.launch(Dispatchers.Main) {
            //是否正在录像
            isRecording(jsonObject)
            //是否在线
            isOnline(jsonObject)
            //GPS
            updateGPS(jsonObject)
            //图传信号
            downLinkSignal(jsonObject)
            //遥控信号
            upLinkSignal(jsonObject)
            //电池电量
            getBatteryColor(jsonObject)
            yawAngleValue = jsonObject.optDouble("azimuth")
            //偏航角
            mBinding.widgetCompass.tvYawAngle.text =
                getString(R.string.yaw_angle_str, "${jsonObject.optDouble("azimuth")}")
            //GPS等级
            mBinding.widgetCompass.tvGpsLevel.text =
                getString(R.string.gps_level_str, "${jsonObject.optInt("GPSLevel")}")
            //上行信号
            mBinding.widgetCompass.tvUpSignal.text =
                getString(R.string.up_signal_str, jsonObject.optInt("upLink"))
            //下行信号
            mBinding.widgetCompass.tvDownSignal.text =
                getString(R.string.down_signal_str, jsonObject.optInt("downLink"))
            //云台俯仰角
            mBinding.widgetCompass.tvGimbalPitch.text =
                getString(R.string.gimbal_pitch_str, jsonObject.optDouble("gimbalPitch"))
            //实时高度
            mBinding.telemetryWidget.altitudeWidgetValue.text =
                "${jsonObject.optDouble("altitude")} m"
            //垂直速度
            mBinding.telemetryWidget.verticalSpeedValue.text =
                "${jsonObject.optDouble("verticalSpeed")} m/s"
            //水平速度
            mBinding.telemetryWidget.horizontalSpeedValue.text =
                "${String.format("%.1f", jsonObject.optDouble("horizontalSpeed"))} m/s"
            //距离终点
            mBinding.telemetryWidget.distanceWidgetValue.text =
                "${jsonObject.optDouble("distanceEnd")}m"
            //实时坐标
            mBinding.telemetryWidget.locationWidgetValue.text = "${
                String.format(
                    "%.6f",
                    jsonObject.optDouble("longitudeWGS")
                )
            }，${String.format("%.6f", jsonObject.optDouble("latitudeWGS"))}"
            //当站点设备处于非待命状态时说明该站点正在执行任务，此时将无人机添加到地图上并在站点和无人机之间进行连线
            when (checkedSiteInfo?.subState) {
                in MISSION_TYPE -> {
                    if (isBatchInfo && missionBatch.isNotEmpty()) {
                        isBatchInfo = false
                        operateModel.missionBatchInfo(missionBatch)
                    }
                    val latLng = CoordinateConverterUtil.convertWGS84ToAMap(
                        LatLng(
                            jsonObject.optDouble("latitudeWGS"),
                            jsonObject.optDouble("longitudeWGS")
                        )
                    )
                    //将无人机添加到地图上
                    if (uavMarker == null) {
                        addUavMarker(latLng)
                    } else {
                        animateAircraftMarker(latLng, uavMarker?.position!!)
                    }
                    if (flightRecords.isNotEmpty() && polyline != null) {
                        flightRecords.add(latLng)
                        polyline?.points = flightRecords
                    }
                    if (startToUavLine == null && startPoint != null) {
                        addStartToUav(latLng)
                    } else {
                        startToUavLine?.points = mutableListOf(startPoint, latLng)
                    }
                    //当在线时非待命下初始化控制按钮
                    initControlBtn()
                    if (isCurrentSite) {
                        mBinding.btnSwitch.visible()
                    } else {
                        if (mBinding.firstFpvWidget.isGone()) {
                            mBinding.btnSwitch.visible()
                            mBinding.firstFpvWidget.visible()
                            //第一视角
                            mBinding.firstFpvWidget.release()
                            mBinding.firstFpvWidget.url = checkedSiteInfo?.uavInfo?.uavFlvUrl
                            mBinding.firstFpvWidget.setController(firstController)
                            mBinding.firstFpvWidget.start()
                        }

                    }
                }

                else -> {
                    if (isCurrentSite) {
                        mBinding.btnSwitch.visible()
                    } else {
                        if (!isSwapped) {
                            mBinding.firstFpvWidget.gone()
                        }
                        mBinding.firstFpvWidget.release()
                    }
                    clearAllCurrentTaskMarkers()
                    destroyFloat()
                }
            }
        }
    }

    /**
     * 是否正在录像
     * @param jsonObject JSONObject
     */
    private fun isRecording(jsonObject: JSONObject) {
        //判断是否正在录像
        isRecording = jsonObject.optInt("isRecording")
        when (isRecording) {
            1 -> {
                if (floatItemList.isNotEmpty()) {
                    floatItemList.forEach {
                        if (it.title == "录像") {
                            it.icon = drawableToBitmap(R.drawable.ic_realtime_stv)
                        }
                    }
                    mFloatMenu?.setFloatItemList(floatItemList)
                }
            }

            else -> {
                if (floatItemList.isNotEmpty()) {
                    floatItemList.forEach {
                        if (it.title == "录像") {
                            it.icon = drawableToBitmap(R.drawable.ic_realtime_tv)
                        }
                    }
                    mFloatMenu?.setFloatItemList(floatItemList)
                }
            }
        }
    }

    /**
     * 是否在线
     * @param jsonObject JSONObject
     */
    private fun isOnline(jsonObject: JSONObject) {
        mBinding.widgetCompass.tvTaskStatus.visible()
        mBinding.widgetCompass.tvTaskStatus.run {
            text = SiteState.getByCode(checkedSiteInfo!!.subState).value
            if (checkedSiteInfo?.subState == 102) {
                setTextColor(getColor(R.color.multi_site_dm_color))
            } else {
                setTextColor(getColor(R.color.multi_site_qt_color))
            }
        }
        //是否在线
        if (jsonObject.optInt("isUAVOnline") > 0) {
            mBinding.widgetCompass.tvUavStatus.run {
                text = "在线"
                setTextColor(getColor(R.color.multi_site_dm_color))
                setCompoundDrawablesRelativeWithIntrinsicBounds(
                    getDrawable(R.drawable.ic_realtime_point_on),
                    null,
                    null,
                    null
                )
            }
        } else {
            mBinding.widgetCompass.tvTaskStatus.gone()
            mBinding.widgetCompass.tvUavStatus.run {
                text = "离线"
                setTextColor(getColor(R.color.multi_site_lx_color))
                setCompoundDrawablesRelativeWithIntrinsicBounds(
                    getDrawable(R.drawable.ic_realtime_point_off),
                    null,
                    null,
                    null
                )
            }
        }
    }

    /**
     * GPS更新
     * @param jsonObject JSONObject
     */
    private fun updateGPS(jsonObject: JSONObject) {
        //GSP数量相关
        mBinding.widgetCompass.gpsCountValue.text = "${jsonObject.optInt("GPSCount")}"
        when (jsonObject.optInt("GPSCount")) {
            0 -> {
                mBinding.widgetCompass.gpsSignalIcon.setBackgroundResource(R.drawable.ic_realtime_topbar_gps_gray)
                mBinding.widgetCompass.gpsSignalRod.setBackgroundResource(R.drawable.ic_topbar_signal_level_0)
            }

            in 1..5 -> {
                mBinding.widgetCompass.gpsSignalIcon.setBackgroundResource(R.drawable.ic_realtime_topbar_gps)
                mBinding.widgetCompass.gpsSignalRod.setBackgroundResource(R.drawable.ic_topbar_signal_level_2)
            }

            in 6..10 -> {
                mBinding.widgetCompass.gpsSignalIcon.setBackgroundResource(R.drawable.ic_realtime_topbar_gps)
                mBinding.widgetCompass.gpsSignalRod.setBackgroundResource(R.drawable.ic_topbar_signal_level_4)
            }

            in 11..15 -> {
                mBinding.widgetCompass.gpsSignalIcon.setBackgroundResource(R.drawable.ic_realtime_topbar_gps)
                mBinding.widgetCompass.gpsSignalRod.setBackgroundResource(R.drawable.ic_topbar_signal_level_5)
            }

            else -> {
                mBinding.widgetCompass.gpsSignalIcon.setBackgroundResource(R.drawable.ic_realtime_topbar_gps)
                mBinding.widgetCompass.gpsSignalRod.setBackgroundResource(R.drawable.ic_topbar_signal_level_5)
            }
        }
    }

    /**
     * 电池电量
     * @param jsonObject JSONObject
     */
    private fun getBatteryColor(jsonObject: JSONObject) {
        val batteries = jsonObject.getJSONArray("batteries")
        when (batteries.length()) {
            1 -> {
                mBinding.widgetCompass.imageviewBatteryIcon.visible()
                mBinding.widgetCompass.textviewBatteryValue.visible()
                mBinding.widgetCompass.textviewBatteryValue.text =
                    "${batteries.getJSONObject(0).optInt("batteryPercent")}%"
                mBinding.widgetCompass.textviewBattery1Value.gone()
                mBinding.widgetCompass.textviewBattery2Value.gone()
                when (batteries.getJSONObject(0).optInt("batteryPercent")) {
                    0 -> {
                        mBinding.widgetCompass.imageviewBatteryIcon.setBackgroundResource(
                            R.drawable.ic_realtime_battery_gray
                        )
                        mBinding.widgetCompass.textviewBatteryValue.setTextColor(
                            getColor(R.color.uav_off)
                        )
                    }

                    in 1..25 -> {
                        mBinding.widgetCompass.imageviewBatteryIcon.setBackgroundResource(
                            R.drawable.ic_realtime_battery_red
                        )
                        mBinding.widgetCompass.textviewBatteryValue.setTextColor(
                            getColor(dji.v5.ux.R.color.uxsdk_battery_danger)
                        )
                    }

                    in 26..75 -> {
                        mBinding.widgetCompass.imageviewBatteryIcon.setBackgroundResource(
                            R.drawable.ic_realtime_battery_yellow
                        )
                        mBinding.widgetCompass.textviewBatteryValue.setTextColor(
                            getColor(dji.v5.ux.R.color.uxsdk_battery_overheating)
                        )
                    }

                    in 76..100 -> {
                        mBinding.widgetCompass.imageviewBatteryIcon.setBackgroundResource(
                            R.drawable.ic_realtime_battery_green
                        )
                        mBinding.widgetCompass.textviewBatteryValue.setTextColor(
                            getColor(dji.v5.ux.R.color.uxsdk_battery_healthy)
                        )
                    }
                }
            }

            2 -> {
                mBinding.widgetCompass.imageviewBatteryIcon.visible()
                mBinding.widgetCompass.textviewBatteryValue.gone()
                mBinding.widgetCompass.textviewBattery1Value.visible()
                mBinding.widgetCompass.textviewBattery2Value.visible()
                mBinding.widgetCompass.textviewBattery1Value.text =
                    "${batteries.getJSONObject(0).optInt("batteryPercent")}%"
                mBinding.widgetCompass.textviewBattery2Value.text =
                    "${batteries.getJSONObject(1).optInt("batteryPercent")}%"
                when (batteries.getJSONObject(1).optInt("batteryPercent")) {
                    0 -> {
                        mBinding.widgetCompass.imageviewBatteryIcon.setBackgroundResource(
                            R.drawable.ic_realtime_battery_double_gray
                        )
                        mBinding.widgetCompass.textviewBattery1Value.setTextColor(
                            getColor(R.color.uav_off)
                        )
                        mBinding.widgetCompass.textviewBattery2Value.setTextColor(
                            getColor(R.color.uav_off)
                        )
                    }

                    in 1..25 -> {
                        mBinding.widgetCompass.imageviewBatteryIcon.setBackgroundResource(
                            R.drawable.ic_realtime_battery_double_red
                        )
                        mBinding.widgetCompass.textviewBattery1Value.setTextColor(
                            getColor(dji.v5.ux.R.color.uxsdk_battery_danger)
                        )
                        mBinding.widgetCompass.textviewBattery2Value.setTextColor(
                            getColor(dji.v5.ux.R.color.uxsdk_battery_danger)
                        )
                    }

                    in 26..75 -> {
                        mBinding.widgetCompass.imageviewBatteryIcon.setBackgroundResource(
                            R.drawable.ic_realtime_battery_double_yellow
                        )
                        mBinding.widgetCompass.textviewBattery1Value.setTextColor(
                            getColor(dji.v5.ux.R.color.uxsdk_battery_overheating)
                        )
                        mBinding.widgetCompass.textviewBattery2Value.setTextColor(
                            getColor(dji.v5.ux.R.color.uxsdk_battery_overheating)
                        )
                    }

                    in 76..100 -> {
                        mBinding.widgetCompass.imageviewBatteryIcon.setBackgroundResource(
                            R.drawable.ic_realtime_battery_double_green
                        )
                        mBinding.widgetCompass.textviewBattery1Value.setTextColor(
                            getColor(dji.v5.ux.R.color.uxsdk_battery_healthy)
                        )
                        mBinding.widgetCompass.textviewBattery2Value.setTextColor(
                            getColor(dji.v5.ux.R.color.uxsdk_battery_healthy)
                        )
                    }
                }
            }
        }
    }

    /**
     *  图传信号
     * @param jsonObject JSONObject
     */
    private fun downLinkSignal(jsonObject: JSONObject) {
        when (jsonObject.optInt("downLink")) {
            0 -> {
                mBinding.widgetCompass.hdSignalIcon.setBackgroundResource(R.drawable.ic_realtime_hd_gray)
                mBinding.widgetCompass.hdSignalRod.setBackgroundResource(R.drawable.ic_topbar_signal_level_0)
            }

            in 1..30 -> {
                mBinding.widgetCompass.hdSignalIcon.setBackgroundResource(R.drawable.ic_realtime_hd)
                mBinding.widgetCompass.hdSignalRod.setBackgroundResource(R.drawable.ic_topbar_signal_level_2)
            }

            in 31..60 -> {
                mBinding.widgetCompass.hdSignalIcon.setBackgroundResource(R.drawable.ic_realtime_hd)
                mBinding.widgetCompass.hdSignalRod.setBackgroundResource(R.drawable.ic_topbar_signal_level_3)
            }

            in 61..90 -> {
                mBinding.widgetCompass.hdSignalIcon.setBackgroundResource(R.drawable.ic_realtime_hd)
                mBinding.widgetCompass.hdSignalRod.setBackgroundResource(R.drawable.ic_topbar_signal_level_4)
            }

            in 90..100 -> {
                mBinding.widgetCompass.hdSignalIcon.setBackgroundResource(R.drawable.ic_realtime_hd)
                mBinding.widgetCompass.hdSignalRod.setBackgroundResource(R.drawable.ic_topbar_signal_level_5)
            }
        }
    }

    /**
     *  遥控器信号
     * @param jsonObject JSONObject
     */
    private fun upLinkSignal(jsonObject: JSONObject) {
        when (jsonObject.optInt("upLink")) {
            0 -> {
                mBinding.widgetCompass.rcSignalIcon.setBackgroundResource(R.drawable.ic_realtime_rc_gray)
                mBinding.widgetCompass.rcSignalRod.setBackgroundResource(R.drawable.ic_topbar_signal_level_0)
            }

            in 1..30 -> {
                mBinding.widgetCompass.rcSignalIcon.setBackgroundResource(R.drawable.ic_realtime_rc)
                mBinding.widgetCompass.rcSignalRod.setBackgroundResource(R.drawable.ic_topbar_signal_level_2)
            }

            in 31..60 -> {
                mBinding.widgetCompass.rcSignalIcon.setBackgroundResource(R.drawable.ic_realtime_rc)
                mBinding.widgetCompass.rcSignalRod.setBackgroundResource(R.drawable.ic_topbar_signal_level_3)
            }

            in 61..90 -> {
                mBinding.widgetCompass.rcSignalIcon.setBackgroundResource(R.drawable.ic_realtime_rc)
                mBinding.widgetCompass.rcSignalRod.setBackgroundResource(R.drawable.ic_topbar_signal_level_4)
            }

            in 90..100 -> {
                mBinding.widgetCompass.rcSignalIcon.setBackgroundResource(R.drawable.ic_realtime_rc)
                mBinding.widgetCompass.rcSignalRod.setBackgroundResource(R.drawable.ic_topbar_signal_level_5)
            }
        }
    }

    //清除所有当前任务相关标记
    private fun clearAllCurrentTaskMarkers() {
        uavMarker?.remove()
        uavMarker = null
        flightRecords.clear()
        polyline?.remove()
        polyline = null
        startToUavLine?.remove()
        startToUavLine = null
        isBatchInfo = true
    }

    /**
     * 绘制子阵
     */
    private fun drawSubArea(subAreaListBean: MutableList<SiteAreaListBean>) {
        // 先清除旧多边形
        clearSiteArea()
        lifecycleScope.launch(Dispatchers.IO) {
            delay(1000) // 避免地图视角未完全切换时立即加载
            subAreaListBean.chunked(25).forEachIndexed { index, chunk ->
                val polygons = chunk.map { areaInfo ->
                    async {
                        val latLngList = areaInfo.features.geometry.coordinates.flatMap { areaPointList ->
                            areaPointList.map { latlngInfo ->
                                LatLng(latlngInfo[1], latlngInfo[0]).gpsToAmap()
                            }
                        }
                        PolygonOptions().apply {
                            addAll(latLngList)
                            strokeWidth(4f)
                            strokeColor(Color.YELLOW)
                            fillColor(Color.argb(0, 0, 0, 0))
                            zIndex(MapIndex.SUB_ARRAY_INDEX.toFloat())
                        }
                    }
                }.awaitAll()

                // 分帧添加，防止卡顿
                withContext(Dispatchers.Main.immediate) {
                    polygons.forEachIndexed { i, options ->
                        siteMap.addPolygon(options)
                        if (i % 5 == 0) delay(8) // 每5个多边形延迟8ms，防止卡顿
                    }
                }
                yield() // 让出线程
            }
        }
    }

    // 获取区域位置坐标以及区域编号
    private fun drawSubAreaNumber(siteAreaListBean: MutableList<SiteAreaListBean>) {
        lifecycleScope.launch(Dispatchers.IO) {
            delay(1200) // 先等待地图视角加载完成，避免 UI 线程阻塞

            val markerOptionsList = arrayListOf<MarkerOptions>()

            siteAreaListBean.forEachIndexed { index, areaInfo ->
                val latLng = LatLng(areaInfo.centPointLat, areaInfo.centPointLon).gpsToAmap()
                val bitmapDescriptor = withContext(Dispatchers.IO) {
                    BitmapDescriptorFactory.fromView(areaMarkerStyle(areaInfo.areaCode))
                }

                val markerOptions = MarkerOptions()
                    .position(latLng)
                    .icon(bitmapDescriptor)
                    .anchor(0.5f, 0.5f)
                    .zIndex(MapIndex.SUB_ARRAY_NUM_INDEX.toFloat())

                markerOptionsList.add(markerOptions)

                if (index % 10 == 0) delay(16) // 每10个标记延迟，防止UI线程卡死
            }

            // UI 线程添加标记
            withContext(Dispatchers.Main.immediate) {
                val allMarkers = mBinding.siteMap.map.addMarkers(markerOptionsList, false)
                subMarkerList.addAll(allMarkers)
            }
        }
    }

    private fun areaMarkerStyle(markerTitle: String): View {
        val markerLayout = LayoutInflater.from(appContext).inflate(R.layout.area_marker_layout, null)
        markerLayout.findViewById<AppCompatTextView>(R.id.marker_title).text = markerTitle
        return markerLayout
    }

    private fun takeHistoryMission(flightHistoryListInfo: ArrayList<FlightHistoryListBean>) {
        clearHistoryFlightPath()
        flightHistoryListInfo.flatMap { flightHistoryInfo ->
            siteMissionListInfo.filter { missionInfo ->
                flightHistoryInfo.missionId == missionInfo.missionId
            }.map { missionInfo ->
                flightHistoryInfo to missionInfo
            }
        }.forEach { (_, missionInfo) ->
            takeFlightHistory(missionInfo)
        }
    }

    //整理任务数据
    private fun takeFlightHistory(missionInfo: MissionInfoBean) {
        XLogUtil.i(TAG, "检索到对应飞行历史数据，任务名称：${missionInfo.missionName}")
        when (missionInfo.missionType) {
            1 -> {
                drawFlightPath(missionInfo.flightParams.flightPath!!)
            }

            9 -> {
                missionInfo.flightParams.childrenList?.forEach { children ->
                    drawFlightPath(children.flightPath)
                }
            }

            else -> {
                XLogUtil.e(TAG, "任务类型未定义")
            }
        }
    }

    //绘制任务航线
    private fun drawFlightPath(flightPaths: MutableList<MissionInfoBean.FlightParams.FlightPath>) {
        val latLngs: MutableList<LatLng> = mutableListOf()
        flightPaths.forEach { flightPath ->
            val sourceLatLng = LatLng(flightPath.latitude, flightPath.longitude)
            latLngs.add(sourceLatLng.gpsToAmap())
        }
        val flightPathPolyline = mBinding.siteMap.map.addPolyline(
            PolylineOptions()
                .addAll(latLngs)
                .width(10f)
                .color(ContextCompat.getColor(appContext, R.color.history_color2))
                .zIndex(999f)
        )
        allHistoryFlightPath.add(flightPathPolyline)
    }

    private fun clearSubMarkers() {
        subMarkerList.forEach { marker ->
            marker.remove() // 删除标记点
        }
        subMarkerList.clear() // 清空标记点引用列表
    }

    private fun clearSiteArea() {
        //清除所有已绘制子阵
        allSiteArea.forEach { polygon ->
            polygon.remove()
        }
        allSiteArea.clear()
    }

    private fun responseToast(message: String) {
        val jsonObject = JsonParser.parseString(message).asJsonObject
        val reason = jsonObject.get("reason").asString
        reason.toast()
    }

    private fun updateAvailableCamera(availableCameraList: List<ComponentIndexType>) {
        if (!isCurrentSite) return
        onlyOneCamera = availableCameraList.size == 1
        if (onlyOneCamera) {
            mBinding.djiFpvWidget.updateVideoSource(availableCameraList[0])
            mBinding.djiFpvWidget.visible()
            mBinding.firstFpvWidget.gone()
            return
        }
        cameraIndex = getSuitableSource(availableCameraList, ComponentIndexType.LEFT_OR_MAIN)
        mBinding.djiFpvWidget.updateVideoSource(cameraIndex)
        mBinding.djiFpvWidget.visible()
        mBinding.firstFpvWidget.gone()
    }

    private fun getSuitableSource(cameraList: List<ComponentIndexType>, defaultSource: ComponentIndexType): ComponentIndexType {
        if (cameraList.contains(ComponentIndexType.LEFT_OR_MAIN)) {
            return ComponentIndexType.LEFT_OR_MAIN
        } else if (cameraList.contains(ComponentIndexType.RIGHT)) {
            return ComponentIndexType.RIGHT
        } else if (cameraList.contains(ComponentIndexType.UP)) {
            return ComponentIndexType.UP
        }
        return defaultSource
    }

    companion object {
        private val DEFAULT_ANIM_DURATION = 200L
        private val ELEVATION_FOREGROUND = 9f
        private val ELEVATION_FULL_SCREEN = -1f
        private const val PATH_LINE_ELEVATION = 5f
        private const val START_TO_UAV_LINE_ELEVATION = 6f
        private const val AIRCRAFT_MARKER_ELEVATION = 7f
        private const val FLIGHT_ANIM_DURATION = 500L
        private val MENU_ITEMS =
            mutableListOf("暂停", "停止", "恢复", "返航", "急停", "拍照", "录像")
        private val MENU_ICONS = mutableListOf(
            R.drawable.selector_pause,
            R.drawable.selector_stop_task,
            R.drawable.selector_recover,
            R.drawable.selector_return,
            R.drawable.selector_ins_stop,
            R.drawable.selector_take_picture,
            R.drawable.ic_realtime_tv
        )
    }
}
