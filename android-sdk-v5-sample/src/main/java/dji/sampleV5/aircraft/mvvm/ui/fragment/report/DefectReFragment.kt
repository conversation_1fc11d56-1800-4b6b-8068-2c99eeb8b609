package dji.sampleV5.aircraft.mvvm.ui.fragment.report

import android.os.Bundle
import androidx.lifecycle.Observer
import com.drake.brv.BindingAdapter
import com.drake.brv.annotaion.DividerOrientation
import com.drake.brv.utils.bindingAdapter
import com.drake.brv.utils.divider
import com.drake.brv.utils.grid
import com.drake.brv.utils.setup
import com.drake.brv.utils.staggered
import dji.sampleV5.aircraft.R
import dji.sampleV5.aircraft.databinding.FragmentDefectReBinding
import dji.sampleV5.aircraft.mvvm.base.BaseVMFragment
import dji.sampleV5.aircraft.mvvm.base.appContext
import dji.sampleV5.aircraft.mvvm.event.LiveDataEvent
import dji.sampleV5.aircraft.mvvm.ext.getColorExt
import dji.sampleV5.aircraft.mvvm.ext.loadListError
import dji.sampleV5.aircraft.mvvm.ext.loadListSuccess
import dji.sampleV5.aircraft.mvvm.ext.showDialogMessage
import dji.sampleV5.aircraft.mvvm.ext.toast
import dji.sampleV5.aircraft.mvvm.net.LoadStatusEntity
import dji.sampleV5.aircraft.mvvm.net.response.DefectReportInfoBean
import dji.sampleV5.aircraft.mvvm.ui.activity.report.ReportViewModel
import dji.sampleV5.aircraft.net.DownloadUtil
import dji.sampleV5.aircraft.net.api.Api
import dji.sampleV5.aircraft.page.ai.PdfViewerActivity


class DefectReFragment : BaseVMFragment<ReportViewModel, FragmentDefectReBinding>() {
    private var subLocationId: Int = 0
    private var defectBindingAdapter: BindingAdapter = BindingAdapter()
    private var deletePosition: Int = -1
    override fun initView(savedInstanceState: Bundle?) {
        arguments?.let {
            subLocationId = it.getInt(ARG_PARAM)
        }
        LiveDataEvent.subLocationId.observe(this) {
            if (it != subLocationId) {
                subLocationId = it
                mBinding.defectRefresh.refresh()
            }
        }
        mBinding.defectRefresh.onRefresh {
            mViewModel.getDefectReportList(
                isRefresh = true,
                loadingDialog = false,
                subLocationId = subLocationId
            )
        }.onLoadMore {
            mViewModel.getDefectReportList(
                isRefresh = false,
                loadingDialog = false,
                subLocationId = subLocationId
            )
        }
        initReportList()
    }

    override fun lazyLoadData() {
        mViewModel.getDefectReportList(
            isRefresh = true,
            loadingDialog = true,
            subLocationId = subLocationId
        )
    }

    private fun initReportList() {
        mBinding.defectRecycler.run {
            staggered(2)
            divider {
                setColor(getColorExt(R.color.transparent))
                setDivider(20)
                includeVisible = true
                orientation = DividerOrientation.GRID
            }.setup {
                addType<DefectReportInfoBean>(R.layout.site_defect_item_layout)
                R.id.btn_look_pdf.onClick {
                    PdfViewerActivity.showPdf(
                        requireActivity(),
                        getModel<DefectReportInfoBean>(layoutPosition)
                    )
                }
                R.id.btn_delete_rep.onClick {
                    deletePosition = layoutPosition
                    showDialogMessage("确定要删除 “${getModel<DefectReportInfoBean>().reportName}” 报告吗？", title = "删除报告", positiveAction = {
                        mViewModel.deleteReport(getModel<DefectReportInfoBean>(layoutPosition).id)
                    }, isVisible = true)
                }
            }
            defectBindingAdapter = this.bindingAdapter
        }
    }

    override fun onRequestSuccess() {
        mViewModel.run {
            defectReportInfoLiveDate.observe(this@DefectReFragment) {
                defectBindingAdapter.loadListSuccess(it, mBinding.defectRefresh)
                /*if (it.records.isEmpty()) {
                    mBinding.defectRefresh.showEmpty("暂无场站报告")
                } else {
                    defectBindingAdapter.loadListSuccess(it, mBinding.defectRefresh)
                }*/
            }
            deleteReportInfoLiveDate.observe(this@DefectReFragment) {
                it?.toast()
                defectBindingAdapter.mutable.removeAt(deletePosition)
                defectBindingAdapter.notifyItemRemoved(deletePosition)
            }
        }
    }

    override fun onRequestError(loadStatus: LoadStatusEntity) {
        when (loadStatus.requestCode) {
            Api.SITE_DEFECT_REPORT_LIST -> {
                defectBindingAdapter.loadListError(loadStatus, mBinding.defectRefresh)
                mBinding.defectRefresh.showError(loadStatus)
            }
            else -> {
                showDialogMessage("请求码：${loadStatus.requestCode} \n错误码：${loadStatus.errorCode}, 错误信息：${loadStatus.errorMessage}")
            }
        }
    }

    companion object {
        private const val ARG_PARAM = "param"
        fun newInstance(param: Int) = DefectReFragment().apply {
            arguments = Bundle().apply {
                putSerializable(ARG_PARAM, param)
            }
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        DownloadUtil.get(appContext).cancelDownload()
    }

    override fun getPreferencesTitle(): String  = ""
}