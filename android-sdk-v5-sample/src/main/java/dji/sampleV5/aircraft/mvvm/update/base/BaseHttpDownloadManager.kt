package dji.sampleV5.aircraft.mvvm.update.base

import dji.sampleV5.aircraft.mvvm.update.base.bean.DownloadStatus
import kotlinx.coroutines.flow.Flow

/**
 * ProjectName: AppUpdate
 * PackageName: com.azhon.appupdate.base
 * FileName:    BaseHttpDownloadManager
 * CreateDate:  2022/4/7 on 10:24
 * Desc:
 *
 * <AUTHOR>
 */

abstract class BaseHttpDownloadManager {
    /**
     * download apk from apkUrl
     *
     * @param apkUrl
     * @param apkName
     */
    abstract fun download(apkUrl: String, apkName: String): Flow<DownloadStatus>

    /**
     * cancel download apk
     */
    abstract fun cancel()

    /**
     * release memory
     */
    abstract fun release()
}