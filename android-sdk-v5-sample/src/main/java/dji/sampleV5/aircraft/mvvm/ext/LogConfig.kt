package dji.sampleV5.aircraft.mvvm.ext

/**
 * Copyright (C), 2015-2024
 * FileName: LogConfig
 * Author: 80945
 * Date: 2024/11/12 14:09
 * Description:
 */
object LogConfig {
    const val appName: String = "yuguang"
    const val xLogPrint: Boolean = true
    const val xLogRoot: String = "/sdcard/$appName"
    const val xLogPath: String = "$xLogRoot/log/"
    const val xLogBackUp: String = "$xLogRoot/log-backup/"
    const val XLogTemp: String = "$xLogRoot/log-temp/"
    const val xLogCrashPath: String = "$xLogRoot/crash/"
    const val subLocationTilePath: String = "$xLogRoot/overlay/"

    const val xLogFormat: String = "{d MM-dd HH:mm:ss.SSS} {L}/{t}: {m}"
}