package dji.sampleV5.aircraft.mvvm.ext

import android.graphics.drawable.Drawable
import android.util.TypedValue
import android.view.View
import android.widget.ImageView
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.content.ContextCompat
import androidx.databinding.BindingAdapter
import com.bumptech.glide.Glide
import com.bumptech.glide.request.RequestOptions
import com.bumptech.glide.request.target.Target
import dji.sampleV5.aircraft.R
import dji.sampleV5.aircraft.mvvm.base.appContext

@BindingAdapter("imageUrl")
fun loadImage(view: ImageView, imageUrl: String?) {
    val options = RequestOptions()
        .error(R.drawable.ic_img_load_error)
        .timeout(30000)
        .placeholder(R.drawable.image_loading)
        .override(Target.SIZE_ORIGINAL, Target.SIZE_ORIGINAL)
    if (!imageUrl.isNullOrEmpty()) {
        Glide.with(view.context)
            .applyDefaultRequestOptions(options)
            .load(imageUrl)
            .into(view)
    } else {
        view.setImageDrawable(ContextCompat.getDrawable(appContext, R.drawable.site_default))
    }
}

@BindingAdapter("imageLocal")
fun loadImage(view: ImageView, drawableId: Int?) {
    drawableId?.let {
        view.setImageResource(it)
    }
}

@BindingAdapter("layout_marginStart")
fun setMarginStart(view: View, marginStart: Float) {
    val params = view.layoutParams as ConstraintLayout.LayoutParams
    params.marginStart = TypedValue.applyDimension(
        TypedValue.COMPLEX_UNIT_DIP, marginStart, view.context.resources.displayMetrics
    ).toInt()
    view.layoutParams = params
}


@BindingAdapter("drawableEndCompat")
fun setDrawableEnd(textView: TextView, drawableResId: Int) {
    val drawableEnd: Drawable? = ContextCompat.getDrawable(textView.context, drawableResId)
    val drawables = textView.compoundDrawables
    // 保留原有的 drawableLeft, drawableTop, drawableBottom
    textView.setCompoundDrawablesWithIntrinsicBounds(
        drawables[0], // drawableLeft
        drawables[1], // drawableTop
        drawableEnd,  // drawableRight (drawableEnd)
        drawables[3]  // drawableBottom
    )
}
