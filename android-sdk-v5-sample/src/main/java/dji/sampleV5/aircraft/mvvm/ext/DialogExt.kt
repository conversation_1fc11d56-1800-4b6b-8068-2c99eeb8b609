package dji.sampleV5.aircraft.mvvm.ext


import android.annotation.SuppressLint
import android.app.Activity
import android.app.Dialog
import android.content.Context
import android.graphics.Bitmap
import android.graphics.Rect
import android.graphics.drawable.ColorDrawable
import android.text.Editable
import android.text.TextWatcher
import android.view.Gravity
import android.view.KeyEvent
import android.view.LayoutInflater
import android.view.MotionEvent
import android.view.View
import android.view.ViewGroup
import android.widget.PopupWindow
import android.widget.TextView
import androidx.appcompat.app.AppCompatActivity
import androidx.appcompat.widget.AppCompatCheckBox
import androidx.appcompat.widget.AppCompatEditText
import androidx.appcompat.widget.AppCompatImageView
import androidx.appcompat.widget.AppCompatTextView
import androidx.fragment.app.Fragment
import androidx.recyclerview.widget.RecyclerView
import com.afollestad.materialdialogs.MaterialDialog
import com.afollestad.materialdialogs.customview.customView
import com.afollestad.materialdialogs.customview.getCustomView
import com.bumptech.glide.Glide
import com.drake.brv.utils.bindingAdapter
import com.drake.brv.utils.divider
import com.drake.brv.utils.grid
import com.drake.brv.utils.linear
import com.drake.brv.utils.setup
import com.lxj.xpopup.XPopup

import dji.sampleV5.aircraft.R
import dji.sampleV5.aircraft.mvvm.net.request.RegisterSiteRequest
import dji.sampleV5.aircraft.mvvm.net.request.RegisterUavRequest
import dji.sampleV5.aircraft.mvvm.net.response.LocationInfoBean
import dji.sampleV5.aircraft.mvvm.net.response.OverallPlatFormInfo
import dji.sampleV5.aircraft.mvvm.widget.popup.OverLongTextPopup
import dji.sampleV5.aircraft.mvvm.widget.view.ScaleImageView
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.cancel

private const val FULL_SCREEN_FLAG =
    (View.SYSTEM_UI_FLAG_LAYOUT_STABLE or View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION or View.SYSTEM_UI_FLAG_HIDE_NAVIGATION or View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY or View.SYSTEM_UI_FLAG_FULLSCREEN)


// 显示超长文本
fun Context.showFullText(view: View, text: String?) {
    XPopup.Builder(this)
        .hasStatusBar(false)
        .hasShadowBg(false)
        .isTouchThrough(true)
        .isDestroyOnDismiss(true)
        .atView(view)
        .asCustom(OverLongTextPopup(this, text))
        .show()
}

//跳转其他平台APP
fun Activity.showOtherPlatformApp(
    platFormList: MutableList<OverallPlatFormInfo>,
    positiveAction: (platInfo: OverallPlatFormInfo) -> Unit = {}
) {
    val platDialog: MaterialDialog
    if (!this.isFinishing) {
        platDialog = this.let {
            MaterialDialog(this)
                .cornerRadius(8f)
                .cancelOnTouchOutside(true)
                .customView(
                    R.layout.overall_platform_popup_layout,
                    scrollable = true,
                    noVerticalPadding = true,
                    horizontalPadding = false,
                    dialogWrapContent = true
                )
        }
        platDialog.window?.setWindowAnimations(dji.v5.ux.R.style.UXSDKDialogWindowAnim)
        platDialog.getCustomView().systemUiVisibility = FULL_SCREEN_FLAG
        platDialog.getCustomView().run {
            val platListRv = this.findViewById<RecyclerView>(R.id.popup_rv_platform)
            platListRv.grid(platFormList.size).divider {
                setColor(getColorExt(R.color.transparent))
                setDivider(20)
                includeVisible = true
            }.setup {
                addType<OverallPlatFormInfo>(R.layout.item_overall_platfoem_popup_layout)
                onBind {
                    findView<AppCompatImageView>(R.id.iv_platform_ic).setImageResource(getModel<OverallPlatFormInfo>(layoutPosition).platFormIcon)
                    findView<AppCompatTextView>(R.id.tv_platform_name).text = getModel<OverallPlatFormInfo>(layoutPosition).platFormName
                }
                R.id.item_platform.onClick {
                    positiveAction.invoke(getModel())
                    platDialog.dismiss()
                }
            }.models = platFormList
        }
        platDialog.show()
    }
}

//显示切换任务模式提示弹窗
fun Activity.showChangeTaskModeDialog(
    checkTips: String,
    positiveAction: (Boolean) -> Unit = {},
    negativeAction: (Boolean) -> Unit = {},
) {
    val acDialog: MaterialDialog
    if (!this.isFinishing) {
        acDialog = this.let {
            MaterialDialog(this)
                .cornerRadius(8f)
                .cancelOnTouchOutside(false)
                .customView(
                    R.layout.dialog_change_task_mode_layout,
                    scrollable = true,
                    noVerticalPadding = true,
                    horizontalPadding = false,
                    dialogWrapContent = true
                )
        }
        acDialog.window?.setWindowAnimations(R.style.DialogAnimationSlideBottom)
        acDialog.getCustomView().run {
            systemUiVisibility = FULL_SCREEN_FLAG
            findViewById<AppCompatTextView>(R.id.dialog_message).run {
                var modeContent = ""
                if(checkTips == "联网模式") {
                    modeContent = "是否切换到【离线模式】模式，离线模式模式下执行任务时，任务数据将保存在本地，您可在【飞行历史】中将本地的任务数据同步到服务器。"
                } else if(checkTips == "离线模式") {
                    modeContent = "是否切换到【联网模式】模式，联网模式模式下执行任务时，任务数据将实时上传到服务器，您可在【飞行历史】中查看。"
                }
                text = modeContent
            }
            val dialogBtnConfirm = findViewById<AppCompatTextView>(R.id.dialog_btn_submit)
            val checkBox = findViewById<AppCompatCheckBox>(R.id.dialog_checkbox)
            dialogBtnConfirm.run {
                setOnClickListener {
                    acDialog.dismiss()
                    positiveAction.invoke(checkBox.isChecked)
                }
            }
            this.findViewById<AppCompatTextView>(R.id.dialog_btn_cancel).run {
                setOnClickListener {
                    acDialog.dismiss()
                    negativeAction.invoke(checkBox.isChecked)
                }
            }
        }

        acDialog.setOnKeyListener { _, keyCode, keyEvent ->
            return@setOnKeyListener keyCode == KeyEvent.KEYCODE_BACK && keyEvent.action == KeyEvent.ACTION_UP
        }

        acDialog.show()
    }
}


/**
 * 显示注册站点弹窗
 * @receiver Activity
 * @param registerUavRequest RegisterUavRequest
 * @param positiveAction Function1<[@kotlin.ParameterName] RegisterUavRequest, Unit>
 * @param negativeAction Function0<Unit>
 */
fun Activity.showRegisterSiteDialog(
    registerSiteRequest: RegisterSiteRequest,
    positiveAction: (registerSiteInfo: RegisterSiteRequest) -> Unit = {},
    negativeAction: () -> Unit = {},
) {
    val acDialog: MaterialDialog
    if (!this.isFinishing) {
        acDialog = this.let {
            MaterialDialog(this)
                .cornerRadius(8f)
                .cancelOnTouchOutside(false)
                .customView(
                    R.layout.dialog_register_site_layout,
                    scrollable = true,
                    noVerticalPadding = true,
                    horizontalPadding = false,
                    dialogWrapContent = true
                )
        }
        acDialog.window?.setWindowAnimations(R.style.DialogAnimationSlideBottom)
        acDialog.getCustomView().run {
            systemUiVisibility = FULL_SCREEN_FLAG
            val dialogSiteName = findViewById<AppCompatEditText>(R.id.dialog_et_site_name)
            val dialogSiteLocation = findViewById<AppCompatEditText>(R.id.dialog_et_location)
            val dialogReturnHeight = findViewById<AppCompatEditText>(R.id.dialog_et_return_height)
            val dialogEllipsoidHeight = findViewById<AppCompatEditText>(R.id.dialog_et_ellipsoid_height)
            val dialogBtnConfirm = findViewById<AppCompatTextView>(R.id.dialog_btn_confirm)
            val dialogPullVideoUrl = findViewById<AppCompatEditText>(R.id.dialog_et_pull_video)
            val dialogPushVideoUrl = findViewById<AppCompatEditText>(R.id.dialog_et_push_video)
            registerSiteRequest.videoPullAddr = dialogPullVideoUrl.textString()
            registerSiteRequest.videoPushAddr = dialogPushVideoUrl.textString()
            registerSiteRequest.siteFlightMode = 1
            registerSiteRequest.siteMode = 4
            dialogSiteName.afterTextChange {
                registerSiteRequest.siteName = it
            }
            dialogReturnHeight.afterTextChange {
                if(it.toIntOrNull() != null) {
                    registerSiteRequest.siteRHAltitude = it.toInt()
                } else {
                    registerSiteRequest.siteRHAltitude = 0
                }
            }
            dialogEllipsoidHeight.afterTextChange {
                if(it.toIntOrNull() != null) {
                    registerSiteRequest.siteEllipsAltitude = it.toInt()
                } else {
                    registerSiteRequest.siteEllipsAltitude = 0
                }
            }
            dialogSiteLocation.setText(registerSiteRequest.siteLocation.joinToString(","))
            dialogBtnConfirm.let {
                InputTextManager.with(this@showRegisterSiteDialog)
                    .addView(dialogSiteName)
                    .addView(dialogSiteLocation)
                    .addView(dialogReturnHeight)
                    .addView(dialogEllipsoidHeight)
                    .setMain(it)
                    .build()
            }
            dialogBtnConfirm.run {
                setOnClickListener {
                    acDialog.dismiss()
                    positiveAction.invoke(registerSiteRequest)
                }
            }
            this.findViewById<AppCompatTextView>(R.id.dialog_btn_cancel).run {
                setOnClickListener {
                    acDialog.dismiss()
                    negativeAction.invoke()
                }
            }
            this.findViewById<AppCompatImageView>(R.id.dialog_btn_close).setOnClickListener {
                acDialog.dismiss()
            }
        }
        acDialog.setOnKeyListener { _, keyCode, keyEvent ->
            return@setOnKeyListener keyCode == KeyEvent.KEYCODE_BACK && keyEvent.action == KeyEvent.ACTION_UP
        }
        acDialog.show()
    }
}


/**
 * 显示注册无人机对话框
 * @receiver Activity
 * @param message String
 * @param title String
 * @param positiveButtonText String
 * @param positiveAction Function0<Unit>
 * @param negativeButtonText String
 * @param negativeAction Function0<Unit>
 */
fun Activity.showRegisterUavDialog(
    uavName:String,
    registerUavRequest: RegisterUavRequest,
    positiveAction: (registerUavInfo: RegisterUavRequest) -> Unit = {},
    negativeAction: () -> Unit = {},
) {
    val acDialog: MaterialDialog
    if (!this.isFinishing) {
        acDialog = this.let {
            MaterialDialog(this)
                .cornerRadius(8f)
                .cancelOnTouchOutside(false)
                .customView(
                    R.layout.dialog_register_uav_layout,
                    scrollable = true,
                    noVerticalPadding = true,
                    horizontalPadding = false,
                    dialogWrapContent = true
                )
        }
        acDialog.window?.setWindowAnimations(R.style.DialogAnimationSlideBottom)
        acDialog.getCustomView().run {
            systemUiVisibility = FULL_SCREEN_FLAG
            val dialogUavName = findViewById<AppCompatEditText>(R.id.dialog_et_uav_name)
            val dialogFcSn = findViewById<AppCompatEditText>(R.id.dialog_et_fc_sn)
            val dialogUavProducer = findViewById<AppCompatEditText>(R.id.dialog_et_uav_producer)
            val dialogUavModel = findViewById<AppCompatEditText>(R.id.dialog_et_uav_model)
            val dialogBtnConfirm = findViewById<AppCompatTextView>(R.id.dialog_btn_confirm)
            dialogUavName.setText(registerUavRequest.uavName)
            dialogFcSn.setText(registerUavRequest.sn)
            dialogUavModel.setText(uavName)
            dialogUavName.afterTextChange {
                registerUavRequest.uavName = it
            }
            dialogUavProducer.afterTextChange {
                registerUavRequest.brand = it
            }
            dialogBtnConfirm.let {
                InputTextManager.with(this@showRegisterUavDialog)
                    .addView(dialogUavName)
                    .addView(dialogFcSn)
                    .addView(dialogUavModel)
                    .addView(dialogUavProducer)
                    .setMain(it)
                    .build()
            }
            dialogBtnConfirm.run {
                setOnClickListener {
                    acDialog.dismiss()
                    positiveAction.invoke(registerUavRequest)
                }
            }
            this.findViewById<AppCompatTextView>(R.id.dialog_btn_cancel).run {
                setOnClickListener {
                    acDialog.dismiss()
                    negativeAction.invoke()
                }
            }
            this.findViewById<AppCompatImageView>(R.id.dialog_btn_close).setOnClickListener {
                acDialog.dismiss()
            }
        }
        acDialog.setOnKeyListener { _, keyCode, keyEvent ->
            return@setOnKeyListener keyCode == KeyEvent.KEYCODE_BACK && keyEvent.action == KeyEvent.ACTION_UP
        }
        acDialog.show()
    }
}

/**
 * 显示消息弹窗
 * @param message 显示对话框的内容 必填项
 * @param title 显示对话框的标题 默认 温馨提示
 * @param positiveButtonText 确定按钮文字 默认确定
 * @param positiveAction 点击确定按钮触发的方法 默认空方法
 * @param negativeButtonText 取消按钮文字 默认空 不为空时显示该按钮
 * @param negativeAction 点击取消按钮触发的方法 默认空方法
 *
 */
fun AppCompatActivity.showDialogMessage(
    message: String,
    title: String = "温馨提示",
    positiveButtonText: String = "确定",
    positiveAction: () -> Unit = {},
    negativeButtonText: String = "取消",
    negativeAction: () -> Unit = {},
    isVisible: Boolean = false
) {
    val acDialog: MaterialDialog
    if (!this.isFinishing) {
        acDialog = this.let {
            MaterialDialog(this)
                .cornerRadius(8f)
                .cancelOnTouchOutside(false)
                .customView(
                    R.layout.base_dialog,
                    scrollable = true,
                    noVerticalPadding = true,
                    horizontalPadding = false,
                    dialogWrapContent = true
                )
        }
        acDialog.getCustomView().systemUiVisibility = FULL_SCREEN_FLAG
        acDialog.getCustomView().run {
            this.findViewById<AppCompatTextView>(R.id.dialog_title).text = title
            this.findViewById<AppCompatTextView>(R.id.dialog_message).text = message
            this.findViewById<AppCompatTextView>(R.id.dialog_btn_cancel).text = negativeButtonText
            this.findViewById<AppCompatTextView>(R.id.dialog_btn_submit).text = positiveButtonText
            this.findViewById<AppCompatTextView>(R.id.dialog_btn_submit).run {
                setOnClickListener {
                    acDialog.dismiss()
                    positiveAction.invoke()
                }
            }
            this.findViewById<AppCompatTextView>(R.id.dialog_btn_cancel).run {
                if (isVisible) {
                    setOnClickListener {
                        acDialog.dismiss()
                        negativeAction.invoke()
                    }
                }else {
                    visibility = View.GONE
                }
            }
        }

        acDialog.setOnKeyListener { _, keyCode, keyEvent ->
            return@setOnKeyListener keyCode == KeyEvent.KEYCODE_BACK && keyEvent.action == KeyEvent.ACTION_UP
        }
        acDialog.show()
    }
}

/**
 * @param message 显示对话框的内容 必填项
 * @param title 显示对话框的标题 默认 温馨提示
 * @param positiveButtonText 确定按钮文字 默认确定
 * @param positiveAction 点击确定按钮触发的方法 默认空方法
 * @param negativeButtonText 取消按钮文字 默认空 不为空时显示该按钮
 * @param negativeAction 点击取消按钮触发的方法 默认空方法
 */
fun Fragment.showDialogMessage(
    message: String,
    title: String = "温馨提示",
    positiveButtonText: String = "确定",
    positiveAction: () -> Unit = {},
    negativeButtonText: String = "取消",
    negativeAction: () -> Unit = {},
    isVisible: Boolean = false
) {
    activity?.let {
        val acDialog: MaterialDialog
        if (!it.isFinishing) {
            acDialog = this.let {
                MaterialDialog(activity!!)
                    .cornerRadius(8f)
                    .cancelOnTouchOutside(false)
                    .customView(
                        R.layout.base_dialog,
                        scrollable = true,
                        noVerticalPadding = true,
                        horizontalPadding = false,
                        dialogWrapContent = true
                    )
            }
            acDialog.getCustomView().systemUiVisibility = FULL_SCREEN_FLAG
            acDialog.getCustomView().run {
                this.findViewById<AppCompatTextView>(R.id.dialog_title).text = title
                this.findViewById<AppCompatTextView>(R.id.dialog_message).text = message
                this.findViewById<AppCompatTextView>(R.id.dialog_btn_cancel).text = negativeButtonText
                this.findViewById<AppCompatTextView>(R.id.dialog_btn_submit).text = positiveButtonText
                this.findViewById<AppCompatTextView>(R.id.dialog_btn_submit).run {
                    setOnClickListener {
                        acDialog.dismiss()
                        positiveAction.invoke()
                    }
                }
                this.findViewById<AppCompatTextView>(R.id.dialog_btn_cancel).run {
                    if (isVisible) {
                        setOnClickListener {
                            acDialog.dismiss()
                            negativeAction.invoke()
                        }
                    }else {
                        visibility = View.GONE
                    }
                }
            }

            acDialog.setOnKeyListener { _, keyCode, keyEvent ->
                return@setOnKeyListener keyCode == KeyEvent.KEYCODE_BACK && keyEvent.action == KeyEvent.ACTION_UP
            }
            acDialog.show()
        }
    }
}

//-------------------------------------------首页场站popup--------------------------------------------------
/**
 * 搜索场站popup
 * @receiver Activity
 * @param anchorView View
 * @param chooseLocation String
 * @param locationList MutableList<MainLocation>
 * @param onLocationChooseListener Function1<[@kotlin.ParameterName] SubLocation, Unit>
 */
@SuppressLint("ClickableViewAccessibility")
fun Activity.showLocationPopup(anchorView: View, chooseLocation: String, locationList: MutableList<LocationInfoBean.MainLocation>, onLocationChooseListener: (data: LocationInfoBean.MainLocation.SubLocation) -> Unit = {}) {
    val inflater = this.getSystemService(Context.LAYOUT_INFLATER_SERVICE) as LayoutInflater
    val view = inflater.inflate(R.layout.search_location_popup_layout, null)
    val popupWindow = PopupWindow(
        view,
        ViewGroup.LayoutParams.MATCH_PARENT,
        ViewGroup.LayoutParams.MATCH_PARENT,
        true
    )
    popupWindow.setBackgroundDrawable(ColorDrawable(getColorExt(dji.v5.ux.R.color.uxsdk_black_20_percent)))
    popupWindow.isOutsideTouchable = true
    popupWindow.animationStyle = R.style.PopupWindowAnimation

    // 当前场站
    val currentLocationTv = view.findViewById<AppCompatTextView>(R.id.tv_choose_location)
    currentLocationTv.text = chooseLocation

    // 所有场站列表
    val allLocationRv = view.findViewById<RecyclerView>(R.id.rv_more_location)
    allLocationRv.linear(scrollEnabled = true)
        .divider(R.drawable.popup_location_divider)
        .setup {
            addType<LocationInfoBean.MainLocation>(R.layout.item_popup_main_location_layout)
            addType<LocationInfoBean.MainLocation.SubLocation>(R.layout.item_popup_sub_location_layout)
            onBind {
                when (itemViewType) {
                    R.layout.item_popup_main_location_layout -> {
                        findView<AppCompatTextView>(R.id.tv_main_location).text = getModel<LocationInfoBean.MainLocation>().mainLocation
                        findView<AppCompatImageView>(R.id.iv_main_location).background = if (getModel<LocationInfoBean.MainLocation>().itemExpand) {
                            resources.getDrawable(R.drawable.ic_black_down, null)
                        } else {
                            resources.getDrawable(R.drawable.ic_black_right, null)
                        }
                        getModel<LocationInfoBean.MainLocation>().itemHover = true
                    }
                    R.layout.item_popup_sub_location_layout -> {
                        findView<AppCompatTextView>(R.id.tv_sub_location).text = getModel<LocationInfoBean.MainLocation.SubLocation>().location
                    }
                }
            }
            R.id.main_location.onFastClick {
                if (itemViewType == R.layout.item_popup_main_location_layout) {
                    expandOrCollapse()
                }
            }
            R.id.sub_location.onFastClick {
                if (itemViewType == R.layout.item_popup_sub_location_layout) {
                    currentLocationTv.text = getModel<LocationInfoBean.MainLocation.SubLocation>().location
                    val selectorLocation = mutable[modelPosition] as LocationInfoBean.MainLocation.SubLocation
                    onLocationChooseListener.invoke(selectorLocation)
                    popupWindow.dismiss()
                }
            }
        }.models = locationList

    // 添加搜索场站
    val searchLocationRv = view.findViewById<RecyclerView>(R.id.rv_sub_location)
    searchLocationRv.linear(scrollEnabled = true)
        .divider(R.drawable.popup_location_divider)
        .setup {
            addType<LocationInfoBean.MainLocation.SubLocation>(R.layout.item_popup_sub_location_layout)
            onBind {
                findView<AppCompatTextView>(R.id.tv_sub_location).text = getModel<LocationInfoBean.MainLocation.SubLocation>().location
            }
            R.id.sub_location.onFastClick {
                currentLocationTv.text = getModel<LocationInfoBean.MainLocation.SubLocation>().location
                val selectorLocation = mutable[modelPosition] as LocationInfoBean.MainLocation.SubLocation
                onLocationChooseListener.invoke(selectorLocation)
                popupWindow.dismiss()
            }
        }

    // 搜索框
    val searchLocation = view.findViewById<AppCompatEditText>(R.id.et_popup_search)
    searchLocation.addTextChangedListener(object : TextWatcher {
        override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}
        override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {}
        override fun afterTextChanged(s: Editable?) {
            if (s.isNullOrEmpty()) {
                allLocationRv.visible()
                searchLocationRv.gone()
                allLocationRv.bindingAdapter.models = locationList
            } else {
                val filterLocationList: MutableList<LocationInfoBean.MainLocation.SubLocation> = mutableListOf()
                locationList.forEach { locationInfo ->
                    locationInfo.subLocationList.forEach { subLocation ->
                        if (subLocation.location.contains(s, true)) {
                            filterLocationList.add(subLocation)
                        }
                    }
                }
                allLocationRv.gone()
                searchLocationRv.visible()
                searchLocationRv.bindingAdapter.models = filterLocationList
            }
        }
    })

    popupWindow.showAtLocation(anchorView, Gravity.CENTER, 0, 0)
    // 为根布局设置触摸监听器
    view.setOnTouchListener { _, event ->
        if (event.action == MotionEvent.ACTION_DOWN) {
            val x = event.rawX.toInt()
            val y = event.rawY.toInt()
            val rect = Rect()
            view.findViewById<View>(R.id.popup_content_container).getGlobalVisibleRect(rect)
            if (!rect.contains(x, y)) {
                popupWindow.dismiss()
                return@setOnTouchListener true
            }
        }
        return@setOnTouchListener false
    }
}

//---------------------------------------------------------------------------------------------
@SuppressLint("ClickableViewAccessibility")
fun Activity.showPicturePopup(anchorView: View, imageUrl: String = "", imageBit: Bitmap? = null) {
    // 创建PopupWindow布局
    val inflater = this.getSystemService(Context.LAYOUT_INFLATER_SERVICE) as LayoutInflater
    val view = inflater.inflate(R.layout.preview_picture_popup_layout, null)
    val popupWindow = PopupWindow(
        view,
        ViewGroup.LayoutParams.MATCH_PARENT,
        ViewGroup.LayoutParams.MATCH_PARENT,
        true
    )
    popupWindow.setBackgroundDrawable(ColorDrawable(getColorExt(dji.v5.ux.R.color.uxsdk_black_50_percent)))
    popupWindow.contentView.systemUiVisibility = FULL_SCREEN_FLAG
    popupWindow.isOutsideTouchable = true

    // 为根布局设置触摸监听器
    view.setOnTouchListener { _, event ->
        if (event.action == MotionEvent.ACTION_DOWN) {
            // 获取图片视图的位置和尺寸
            val loadImageView = view.findViewById<ScaleImageView>(R.id.iv_preview_pic)
            val rect = Rect()
            loadImageView.getGlobalVisibleRect(rect)

            // 如果触摸点不在图片视图的区域内，则关闭弹窗
            if (!rect.contains(event.rawX.toInt(), event.rawY.toInt())) {
                popupWindow.dismiss()
                return@setOnTouchListener true
            }
        }
        return@setOnTouchListener false
    }

    val loadImageView = view.findViewById<ScaleImageView>(R.id.iv_preview_pic)
    if (imageUrl.isNotEmpty()) {
        // 加载图片到ImageView
        Glide.with(this)
            .load(imageUrl)
            .into(loadImageView)
    } else if (imageBit != null) {
        // 加载图片到ImageView
        Glide.with(this)
            .load(imageBit)
            .into(loadImageView)
    }
    val closePopup = view.findViewById<AppCompatImageView>(R.id.close_popup)
    closePopup.setOnClickListener {
        popupWindow.dismiss()
    }
    popupWindow.showAtLocation(anchorView, Gravity.CENTER, 0, 0)
}
/*****************************************loading框********************************************/
private var loadingDialog: Dialog? = null

/**
 * 打开等待框
 */
fun AppCompatActivity.showLoadingExt(message: String = "请求网络中...", coroutineScope: CoroutineScope? = null) {
    dismissLoadingExt()
    if (!this.isFinishing) {
        if (loadingDialog == null) {
            //弹出loading时 把当前界面的输入法关闭
            this.hideOffKeyboard()
            loadingDialog = Dialog(this, R.style.loadingDialogTheme).apply {
                window?.decorView?.systemUiVisibility = FULL_SCREEN_FLAG
                setCancelable(false)
                setCanceledOnTouchOutside(false)
                setContentView(
                    LayoutInflater.from(this@showLoadingExt)
                        .inflate(R.layout.layout_loading_view, null).apply {
                            this.findViewById<TextView>(R.id.loading_tips).text = message
                        })
                setOnCancelListener {
                    //关闭弹窗时 将请求也关闭了
                    coroutineScope?.cancel()
                }
            }
        }
        loadingDialog?.show()
    }
}

/**
 * 打开等待框
 */
fun Fragment.showLoadingExt(message: String = "请求网络中...", coroutineScope: CoroutineScope? = null) {
    dismissLoadingExt()
    activity?.let {
        if (!it.isFinishing) {
            if (loadingDialog == null) {
                //弹出loading时 把当前界面的输入法关闭
                it.hideOffKeyboard()
                loadingDialog = Dialog(requireActivity(), R.style.loadingDialogTheme).apply {
                    window?.decorView?.systemUiVisibility = FULL_SCREEN_FLAG
                    setCancelable(false)
                    setCanceledOnTouchOutside(false)
                    setContentView(
                        LayoutInflater.from(it)
                            .inflate(R.layout.layout_loading_view, null).apply {
                                this.findViewById<TextView>(R.id.loading_tips).text = message
                            })
                    setOnCancelListener {
                        //关闭弹窗时 将请求也关闭了
                        coroutineScope?.cancel()
                    }
                }
            }
            loadingDialog?.show()
        }
    }
}

/**
 * 关闭等待框
 */
fun AppCompatActivity.dismissLoadingExt() {
    loadingDialog?.dismiss()
    loadingDialog = null
}

/**
 * 关闭等待框
 */
fun Fragment.dismissLoadingExt() {
    loadingDialog?.dismiss()
    loadingDialog = null
}

