package dji.sampleV5.aircraft.mvvm.ktx

import android.app.Activity
import android.app.Application
import android.os.Bundle
import dji.sampleV5.aircraft.ContextUtil.addActivity
import dji.sampleV5.aircraft.ContextUtil.removeActivity
import dji.v5.utils.common.LogUtils

/**
 * 项目: uav_polit
 * 类描述:
 * 创建人: jinkai
 * 创建时间: 2023/3/7/16:25
 **/
class KtxActivityLifecycle: Application.ActivityLifecycleCallbacks {
    override fun onActivityPaused(activity: Activity) {
        LogUtils.i(activity.javaClass.simpleName)
    }

    override fun onActivityStarted(p0: Activity) {

    }

    override fun onActivityDestroyed(activity: Activity) {
        removeActivity(activity)
    }

    override fun onActivitySaveInstanceState(p0: Activity, p1: Bundle) {
    }

    override fun onActivityStopped(p0: Activity) {
    }

    override fun onActivityCreated(activity: Activity, p1: Bundle?) {
        LogUtils.d(activity.javaClass.simpleName)
        addActivity(activity)
    }

    override fun onActivityResumed(activity: Activity) {
        LogUtils.d(activity.javaClass.simpleName)
    }
}