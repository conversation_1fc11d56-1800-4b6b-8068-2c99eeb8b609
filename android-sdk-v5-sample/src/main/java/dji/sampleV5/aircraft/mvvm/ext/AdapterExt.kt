package dji.sampleV5.aircraft.mvvm.ext

import com.drake.brv.BindingAdapter
import com.drake.brv.PageRefreshLayout
import com.drake.statelayout.StateConfig
import com.drake.statelayout.StateLayout
import com.scwang.smart.refresh.layout.SmartRefreshLayout
import dji.sampleV5.aircraft.mvvm.net.BasePage
import dji.sampleV5.aircraft.mvvm.net.LoadStatusEntity
import dji.sampleV5.aircraft.mvvm.net.response.ApiPagerResponse
import dji.sampleV5.aircraft.mvvm.net.response.DefectInfoBean
import dji.sampleV5.aircraft.mvvm.net.response.DefectRecordInfoBean

/**
 * @projectName yg_ops
 * @FileName AdapterExt
 * @data 2024/1/11 18:20
 * <AUTHOR>
 * @description TODO
 **/
/**
 * 下拉刷新
 * @receiver PageRefreshLayout
 * @param refreshAction Function0<Unit>
 * @return PageRefreshLayout
 */
fun PageRefreshLayout.refresh(refreshAction: () -> Unit = {}): PageRefreshLayout {
    this.setOnRefreshListener {
        refreshAction.invoke()
    }
    return this
}

/**
 * 上拉加载
 * @receiver PageRefreshLayout
 * @param loadMoreAction Function0<Unit>
 * @return PageRefreshLayout
 */
fun PageRefreshLayout.loadMore(loadMoreAction: () -> Unit = {}): PageRefreshLayout {
    this.setOnLoadMoreListener {
        loadMoreAction.invoke()
    }
    return this
}

/**
 * 列表数据加载成功
 * @receiver BaseQuickAdapter<T,*>
 * @param baseListNetEntity BaseListNetEntity<T>
 */
fun <T> BindingAdapter.loadListSuccess(baseListNetEntity: BasePage<T>, pageRefreshLayout: PageRefreshLayout) {
    //关闭头部刷新
    if (baseListNetEntity.isRefresh()) {
        if (baseListNetEntity.getPageData().isEmpty()) {
            pageRefreshLayout.showEmpty("暂无历史数据")
            return
        } else {
            //如果是第一页 那么设置最新数据替换
            this.models = baseListNetEntity.getPageData()
        }
    } else {
        //不是第一页，累加数据
        this.addModels(baseListNetEntity.getPageData())
    }
    //如果还有下一页数据 那么设置 pageRefreshLayout 还可以加载更多数据
    if (baseListNetEntity.hasMore()) {
        pageRefreshLayout.showContent(hasMore = true)
    } else {
        //如果没有更多数据了，设置 pageRefreshLayout 加载完毕 没有更多数据
        pageRefreshLayout.showContent(hasMore = false)
    }
}

/**
 * 列表数据请求失败
 * @receiver BaseQuickAdapter<*, *>
 * @param loadStatus LoadStatusEntity
 * @param pageRefreshLayout SmartRefreshLayout
 */
fun BindingAdapter.loadListError(loadStatus: LoadStatusEntity, pageRefreshLayout: PageRefreshLayout) {
    if (loadStatus.isRefresh) {
        //显示错误界面
        pageRefreshLayout.showError(loadStatus)
    } else {
        // 不是第一页请求，让recyclerview设置加载失败
        pageRefreshLayout.showError(loadStatus)
    }
    //给个错误提示
    loadStatus.errorMessage.toast()
}

fun convertDefectRecordToDefectInfo(record: DefectRecordInfoBean): DefectInfoBean {
    return DefectInfoBean(
        degreeLat = null,
        degreeLon = null,
        subLocationArea = record.subLocationArea,
        subLocationStr = record.subLocationStr,
        pvDefectImmediateList = record.pvDefectRepairRecordList
    )
}

fun <T, U> convertApiPagerResponse(
    source: ApiPagerResponse<T>,
    convert: (T) -> U
): ApiPagerResponse<U> {
    val convertedRecords = source.records.map { convert(it) }
    return ApiPagerResponse(
        records = ArrayList(convertedRecords),
        total = source.total
    ).apply {
        curPage = source.curPage
    }
}