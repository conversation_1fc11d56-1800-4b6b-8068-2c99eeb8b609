package dji.sampleV5.aircraft.mvvm.util

import android.content.Context
import com.alibaba.sdk.android.oss.ClientConfiguration
import com.alibaba.sdk.android.oss.ClientException
import com.alibaba.sdk.android.oss.OSS
import com.alibaba.sdk.android.oss.OSSClient
import com.alibaba.sdk.android.oss.ServiceException
import com.alibaba.sdk.android.oss.callback.OSSCompletedCallback
import com.alibaba.sdk.android.oss.callback.OSSProgressCallback
import com.alibaba.sdk.android.oss.common.OSSHeaders
import com.alibaba.sdk.android.oss.common.auth.OSSCredentialProvider
import com.alibaba.sdk.android.oss.model.CannedAccessControlList
import com.alibaba.sdk.android.oss.model.CreateBucketRequest
import com.alibaba.sdk.android.oss.model.CreateBucketResult
import com.alibaba.sdk.android.oss.model.ListBucketsRequest
import com.alibaba.sdk.android.oss.model.ListBucketsResult
import com.alibaba.sdk.android.oss.model.ObjectMetadata
import com.alibaba.sdk.android.oss.model.PutObjectRequest
import com.alibaba.sdk.android.oss.model.PutObjectResult
import com.alibaba.sdk.android.oss.model.StorageClass

/**
 * Copyright (C), 2015-2025
 * FileName: OSSUploaderUtil
 * Author: 80945
 * Date: 2025/4/29 18:17
 * Description: 阿里云OSS上传文件
 */
object OSSUploaderUtil {
    private lateinit var ossClient: OSS

    /**
     * 上传监听器
     */
    interface UploadListener {
        /** 上传开始 */
        fun onStart()
        /** 上传进度，已上传字节数/总字节数 */
        fun onProgress(currentSize: Long, totalSize: Long)
        /** 上传成功，返回结果 */
        fun onSuccess(result: PutObjectResult)
        /** 上传失败，返回异常 */
        fun onFailure(clientEx: Exception?, serviceEx: Exception?)
    }

    /**
     * 初始化 OSS 客户端
     * @param context Application Context
     * @param endpoint OSS Endpoint，
     * @param akskProvider 凭证提供者
     */
    fun init(context: Context, endpoint: String, akskProvider: OSSCredentialProvider) {
        val conf = ClientConfiguration().apply {
            maxConcurrentRequest = 5
            socketTimeout = 30_000
            connectionTimeout = 15_000
            maxErrorRetry = 2
        }
        ossClient = OSSClient(context, endpoint, akskProvider, conf)
    }

    /**
     * 上传文件
     * @param bucketName String
     * @param objectKey String
     * @param filePath String
     * @param listener UploadListener
     */
    fun uploadFile(bucketName: String, objectKey: String, filePath: String, listener: UploadListener) {
        listener.onStart()
        // 列举 Bucket
        val listReq = ListBucketsRequest()
        ossClient.asyncListBuckets(listReq, object : OSSCompletedCallback<ListBucketsRequest, ListBucketsResult> {
            override fun onSuccess(request: ListBucketsRequest, result: ListBucketsResult) {
                val exists = result.buckets.any { it.name == bucketName }
                if (exists) {
                    doUpload(bucketName, objectKey, filePath, listener)
                } else {
                    // 创建 Bucket
                    val createReq = CreateBucketRequest(bucketName)
                    createReq.bucketACL = CannedAccessControlList.PublicReadWrite
                    createReq.bucketStorageClass = StorageClass.Standard
                    ossClient.asyncCreateBucket(createReq, object : OSSCompletedCallback<CreateBucketRequest, CreateBucketResult> {
                        override fun onSuccess(request: CreateBucketRequest, resultCreate: CreateBucketResult) {
                            doUpload(bucketName, objectKey, filePath, listener)
                            XLogUtil.d("OSSUploaderUtil", "OSS新建桶成功，onSuccess: ${request.bucketName}")
                        }

                        override fun onFailure(request: CreateBucketRequest?, clientException: ClientException?, serviceException: ServiceException?) {
                            if (clientException != null) {
                                XLogUtil.e("OSSUploaderUtil", "OSS新建桶失败，onFailure: ${clientException.localizedMessage}")
                            } else {
                                XLogUtil.e("OSSUploaderUtil", "OSS新建桶失败，onFailure: ${serviceException?.localizedMessage}")
                            }
                            listener.onFailure(clientException, serviceException)
                        }
                    })
                }
            }

            override fun onFailure(request: ListBucketsRequest?, clientException: ClientException?, serviceException: ServiceException?) {
                if (clientException != null) {
                    XLogUtil.e("OSSUploaderUtil", "OSS列举桶失败，onFailure: ${clientException.localizedMessage}")
                } else {
                    XLogUtil.e("OSSUploaderUtil", "OSS列举桶失败，onFailure: ${serviceException?.localizedMessage}")
                }
                listener.onFailure(clientException, serviceException)
            }
        })
    }

    /**
     * 执行实际上传
     */
    private fun doUpload(bucketName: String, objectKey: String, filePath: String, listener: UploadListener) {
        val putReq = PutObjectRequest(bucketName, objectKey, filePath)
        val metadata = ObjectMetadata().apply {
            // 明确设置对象ACL为公共读写
            setHeader(OSSHeaders.OSS_CANNED_ACL, CannedAccessControlList.PublicReadWrite.toString())
            setHeader(OSSHeaders.STORAGE_CLASS, StorageClass.Standard.toString())
        }
        putReq.metadata = metadata
        putReq.progressCallback = OSSProgressCallback { _, current, total ->
            listener.onProgress(current, total)
        }
        ossClient.asyncPutObject(putReq, object : OSSCompletedCallback<PutObjectRequest, PutObjectResult> {
            override fun onSuccess(request: PutObjectRequest, result: PutObjectResult) {
                XLogUtil.d("OSSUploaderUtil", "OSS上传成功，onSuccess: ${request.bucketName}")
                listener.onSuccess(result)
            }
            override fun onFailure(request: PutObjectRequest?, clientException: ClientException?, serviceException: ServiceException?) {
                if (clientException != null) {
                    XLogUtil.e("OSSUploaderUtil", "OSS上传失败，onFailure: ${clientException.localizedMessage}")
                } else {
                    XLogUtil.e("OSSUploaderUtil", "OSS上传失败，onFailure: ${serviceException?.localizedMessage}")
                }
                listener.onFailure(clientException, serviceException)
            }
        })
    }
}