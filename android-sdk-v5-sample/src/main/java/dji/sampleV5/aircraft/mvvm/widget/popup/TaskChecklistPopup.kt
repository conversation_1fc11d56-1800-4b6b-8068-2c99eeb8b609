package dji.sampleV5.aircraft.mvvm.widget.popup

import android.annotation.SuppressLint
import android.content.Context
import android.util.Log
import androidx.appcompat.widget.AppCompatButton
import androidx.appcompat.widget.AppCompatCheckBox
import androidx.appcompat.widget.AppCompatEditText
import androidx.appcompat.widget.AppCompatImageView
import androidx.appcompat.widget.AppCompatTextView
import com.alibaba.fastjson.JSONArray
import com.amap.api.maps.model.LatLng
import com.hjq.toast.Toaster
import com.lxj.xpopup.impl.FullScreenPopupView
import dji.sampleV5.aircraft.DJIAircraftApplication
import dji.sampleV5.aircraft.R
import dji.sampleV5.aircraft.enums.FinishAction
import dji.sampleV5.aircraft.enums.PictureFormat
import dji.sampleV5.aircraft.enums.TaskLostAction
import dji.sampleV5.aircraft.mvvm.ext.afterTextChange
import dji.sampleV5.aircraft.mvvm.ext.getColorExt
import dji.sampleV5.aircraft.mvvm.ext.gone
import dji.sampleV5.aircraft.mvvm.ext.toJsonStr
import dji.sampleV5.aircraft.mvvm.ext.visible
import dji.sampleV5.aircraft.mvvm.net.response.MissionInfoBean
import dji.sampleV5.aircraft.mvvm.util.WaylineUtil
import dji.sampleV5.aircraft.mvvm.util.XLogUtil
import dji.sampleV5.aircraft.mvvm.widget.spinner.EditSpinner
import dji.sampleV5.aircraft.net.bean.Flightpath
import dji.sampleV5.aircraft.net.bean.Mission
import dji.sampleV5.aircraft.net.bean.MissionJson
import dji.sampleV5.aircraft.util.CompressUtil
import dji.sdk.keyvalue.key.BatteryKey
import dji.sdk.keyvalue.key.CameraKey
import dji.sdk.keyvalue.key.FlightControllerKey
import dji.sdk.keyvalue.key.KeyTools
import dji.sdk.keyvalue.key.RemoteControllerKey
import dji.sdk.keyvalue.key.RtkMobileStationKey
import dji.sdk.keyvalue.value.camera.CameraMode
import dji.sdk.keyvalue.value.camera.CameraStorageInfos
import dji.sdk.keyvalue.value.camera.CameraStorageLocation
import dji.sdk.keyvalue.value.camera.CameraStreamSettingsInfo
import dji.sdk.keyvalue.value.camera.CameraVideoStreamSourceType
import dji.sdk.keyvalue.value.common.ComponentIndexType
import dji.sdk.keyvalue.value.common.IntValueConfig
import dji.sdk.keyvalue.value.flightcontroller.RemoteControllerFlightMode
import dji.sdk.keyvalue.value.product.ProductType
import dji.sdk.keyvalue.value.remotecontroller.BatteryInfo
import dji.v5.common.callback.CommonCallbacks
import dji.v5.common.error.IDJIError
import dji.v5.manager.KeyManager
import dji.v5.ux.core.util.SpUtil
import java.util.Locale

/**
 * Copyright (C), 2015-2025
 * FileName: TaskChecklistPopup
 * Author: 80945
 * Date: 2025/1/21 9:49
 * Description: 任务航线检查单
 */
open class TaskChecklistPopup(private val context: Context, private val cameraIndex: ComponentIndexType, private val isOffline: Boolean, private val missionInfo: Any, val closeCheck:() -> Unit, val startTask: (Any) -> Unit): FullScreenPopupView(context) {
    private val TAG = this.javaClass.name
    private val finishActionList = mutableListOf("退出航线模式", "自动返航", "原地降落", "返回航线起始点悬停")
    private val taskLostBehaviorList = mutableListOf("悬停", "原地降落", "自动返航", "继续执行")
    private val productType = DJIAircraftApplication.getInstance().productType
    private var checkMissionInfo: Any = Any()
    private var cameraVideoStreamSourceTypes: MutableList<CameraVideoStreamSourceType> = mutableListOf()

    private var storagePicType: MutableList<String> = mutableListOf()
    private var minGoHomeHeight = 0
    private var maxGoHomeHeight = 0
    private val defaultHeight = 120
    private val defaultTv = "--"
    private lateinit var dismissCheck: AppCompatImageView
    private lateinit var btnStartTask: AppCompatButton

    private lateinit var finishAction: EditSpinner
    private lateinit var taskLostBehavior: EditSpinner

    private lateinit var waylineLength: AppCompatTextView
    private lateinit var waylineTime: AppCompatTextView
    private lateinit var waypointCount: AppCompatTextView
    private lateinit var remoteModeTips: AppCompatTextView
    private lateinit var firstBattery:  AppCompatTextView
    private lateinit var secondBattery:  AppCompatTextView
    private lateinit var rcBattery: AppCompatTextView
    private lateinit var sdcardStorage: AppCompatTextView
    private lateinit var heightLimitRange: AppCompatTextView
    private lateinit var rtkIsEnable: AppCompatTextView

    private lateinit var homeAdd100: AppCompatButton
    private lateinit var homeAdd10: AppCompatButton
    private lateinit var homeMinus100: AppCompatButton
    private lateinit var homeMinus10: AppCompatButton
    private lateinit var startAdd100: AppCompatButton
    private lateinit var startAdd10: AppCompatButton
    private lateinit var startMinus100: AppCompatButton
    private lateinit var startMinus10: AppCompatButton

    private lateinit var goHomeHeight: AppCompatEditText
    private lateinit var startHeight: AppCompatEditText

    private lateinit var checkVisable: AppCompatCheckBox
    private lateinit var checkWidePic: AppCompatCheckBox
    private lateinit var checkZoomPic: AppCompatCheckBox
    private lateinit var checkIrPic: AppCompatCheckBox

    init {
        this.checkMissionInfo = missionInfo
    }

    override fun getImplLayoutId() = R.layout.popup_layout_check_notice_flight


    override fun onCreate() {
        super.onCreate()

        //关闭按钮
        dismissCheck = findViewById(R.id.iv_notice_close)
        dismissCheck.setOnClickListener {
            closeCheck.invoke()
            smartDismiss()
        }
        //开始任务按钮
        btnStartTask = findViewById(R.id.btn_start_task)
        btnStartTask.setOnClickListener {
            if (goHomeHeight.text.toString().toInt() in minGoHomeHeight..maxGoHomeHeight && startHeight.text.toString().toInt() in minGoHomeHeight..maxGoHomeHeight) {
                updateHeight()
                setCameraMode()
                startTask.invoke(checkMissionInfo)
                btnStartTask.gone()
                smartDismiss()
            } else {
                Toaster.show("请检查起降高度设置")
            }
        }
        //无人机相关
        finishAction = findViewById(R.id.sp_finish_action)
        taskLostBehavior = findViewById(R.id.sp_task_lostBehavior)
        remoteModeTips = findViewById(R.id.tv_remote_flight_mode)
        heightLimitRange = findViewById(R.id.tv_go_home_height_range)
        rtkIsEnable = findViewById(R.id.tv_rtk_status_value)

        //航线相关
        waylineLength = findViewById(R.id.tv_wayline_length)
        waylineTime = findViewById(R.id.tv_wayline_time)
        waypointCount = findViewById(R.id.tv_waypoint_count)
        //电池电量相关
        rcBattery = findViewById(R.id.tv_rc_battery)
        firstBattery = findViewById(R.id.tv_first_battery)
        secondBattery = findViewById(R.id.tv_second_battery)
        //存储相关
        sdcardStorage = findViewById(R.id.tv_scard_storge)

        //存储照片类型
        checkVisable = findViewById(R.id.cb_selected_visable_pic)
        checkWidePic = findViewById(R.id.cb_selected_wide_pic)
        checkZoomPic = findViewById(R.id.cb_selected_zoom_pic)
        checkIrPic = findViewById(R.id.cb_selected_ir_pic)
        //起降高度相关
        goHomeHeight = findViewById(R.id.et_go_home_height_value)
        homeAdd100 = findViewById(R.id.btn_go_home_add_100)
        homeAdd10 = findViewById(R.id.btn_go_home_add_10)
        homeMinus100 = findViewById(R.id.btn_go_home_minus_100)
        homeMinus10 = findViewById(R.id.btn_go_home_minus_10)

        //起飞高度
        startHeight = findViewById(R.id.et_safe_start_height_value)
        startAdd100 = findViewById(R.id.btn_safe_start_add_100)
        startAdd10 = findViewById(R.id.btn_safe_start_add_10)
        startMinus100 = findViewById(R.id.btn_safe_start_minus_100)
        startMinus10 = findViewById(R.id.btn_safe_start_minus_10)

        if (productType != null && (productType == ProductType.DJI_MAVIC_3_ENTERPRISE_SERIES || productType == ProductType.DJI_MATRICE_4_SERIES)) {
            checkZoomPic.gone()
            checkZoomPic.isChecked = false
            checkWidePic.gone()
            checkWidePic.isChecked = false
            checkVisable.visible()
            SpUtil.setSavePictureFormat("visable,ir")
        } else {
            checkVisable.gone()
            checkVisable.isChecked = false
            checkWidePic.visible()
            checkZoomPic.visible()
        }
        getDJIInfo()
        uavInfoListener()
        spinnerListener()
        checkPicTypeListener()
        clickHeightListener()
        initLostBehaviorData()
        initFinishActionData()
        loadMissionData()
        textChangeListener()
    }

    override fun doAfterShow() {
        super.doAfterShow()
        initLostBehaviorData()
        initFinishActionData()
    }

    private fun textChangeListener() {
        fun validateAndColor(input: String, editText: AppCompatEditText) {
            val value = input.toIntOrNull() // 使用安全转换代替toInt()
            when {
                value == null -> editText.setTextColor(getColorExt(R.color.red_light)) // 无效输入标
                value < minGoHomeHeight || value > maxGoHomeHeight -> editText.setTextColor(getColorExt(R.color.red_light))
                else -> editText.setTextColor(getColorExt(R.color.black)) // 有效值时恢复默认颜色
            }
        }

        goHomeHeight.afterTextChange {
            validateAndColor(it, goHomeHeight)
        }

        startHeight.afterTextChange {
            validateAndColor(it, startHeight)
        }
    }

    // 初始化航线失联行为数据
    private fun initLostBehaviorData() {
        // 航线失联行为
        taskLostBehavior.setItems(taskLostBehaviorList)
        val taskLostActionCode = SpUtil.getExecuteRCLostAction()?.let { TaskLostAction.getValue(it).code } ?: TaskLostAction.GO_BACK.code
        Log.i("initLostBehaviorData", "taskLostBehaviorList: $taskLostBehaviorList, ------taskLostActionCode: $taskLostActionCode")
        setDefaultSelection(taskLostBehavior, taskLostActionCode)
    }

    // 初始化任务结束动作数据
    private fun initFinishActionData() {
        // 任务结束动作
        finishAction.setItems(finishActionList)
        val finishActionCode = SpUtil.getFinishAction()?.let { FinishAction.getValue(it).code } ?: FinishAction.GO_HOME.code
        Log.i("initFinishActionData", "finishActionList: $finishActionList, ------finishActionCode: $finishActionCode")
        setDefaultSelection(this.finishAction, finishActionCode)
    }

    // 设置默认选中
    private fun setDefaultSelection(behavior: EditSpinner, selectionIndex: Int) {
        behavior.setSelection(selectionIndex)
    }

    private fun loadMissionData() {
        if (isOffline) {
            val missionData = checkMissionInfo as MissionInfoBean
            goHomeHeight.setText(missionData.outHeight.toInt().toString())
            startHeight.setText(missionData.inHeight.toInt().toString())
            updateOfflineMissionInfo(missionData)
            initPicFormatData()
        } else {
            val missionJson = checkMissionInfo as MissionJson
            goHomeHeight.setText(if (missionJson.uavRHAltitude == 0) defaultHeight.toString() else missionJson.uavRHAltitude.toString())
            startHeight.setText(if (missionJson.uavRHAltitude == 0) defaultHeight.toString() else missionJson.uavRHAltitude.toString())
            updateLineMissionInfo(missionJson)
            updatePicType()
        }
    }

    private fun updatePicType() {
        val missionJson = checkMissionInfo as MissionJson
        storagePicType.clear()
        if (missionJson.cameraSetting.mediaStorageViewTypeArray != null && missionJson.cameraSetting.mediaStorageViewTypeArray.isNotEmpty()) {
            missionJson.cameraSetting.mediaStorageViewTypeArray.forEach { typeCode ->
                if (productType != null && (productType == ProductType.DJI_MAVIC_3_ENTERPRISE_SERIES || productType == ProductType.DJI_MATRICE_4_SERIES) && (typeCode == 3 || typeCode == 2)) {
                    storagePicType.add(PictureFormat.VISABLE.format)
                } else {
                    storagePicType.add(PictureFormat.getByCode(typeCode).format)
                }
            }
            SpUtil.setSavePictureFormat(storagePicType.joinToString(","))
        }
        initPicFormatData()
    }

    // 监听按钮点击事件
    private fun clickHeightListener() {
        //返航高度
        homeAdd100.setOnClickListener { updateHomeHeight(100) }
        homeAdd10.setOnClickListener { updateHomeHeight(10) }
        homeMinus100.setOnClickListener { updateHomeHeight(-100) }
        homeMinus10.setOnClickListener { updateHomeHeight(-10) }
        //起飞高度
        startAdd100.setOnClickListener { updateStartHeight(100) }
        startAdd10.setOnClickListener { updateStartHeight(10) }
        startMinus100.setOnClickListener { updateStartHeight(-100) }
        startMinus10.setOnClickListener { updateStartHeight(-10) }
    }

    private fun updateHomeHeight(increment: Int) {
        val curHomeHeight = goHomeHeight.text.toString().toIntOrNull() ?: 20
        val newHeight = curHomeHeight + increment

        if (newHeight in minGoHomeHeight..maxGoHomeHeight) {
            goHomeHeight.setText(newHeight.toString())
        }
    }

    private fun updateStartHeight(increment: Int) {
        val curStartHeight = startHeight.text.toString().toIntOrNull() ?: 20
        val newHeight = curStartHeight + increment

        if (newHeight in minGoHomeHeight..maxGoHomeHeight) {
            startHeight.setText(newHeight.toString())
        }
    }

    private fun updateHeight() {
        if (isOffline) {
            (missionInfo as? MissionInfoBean)?.apply {
                outHeight = goHomeHeight.text.toString().toDouble()
                inHeight = startHeight.text.toString().toDouble()
            }
        } else {
            (missionInfo as? MissionJson)?.apply {
                uavRHAltitude = goHomeHeight.text.toString().toInt()
                uavSTAltitude = startHeight.text.toString().toInt()
            }
        }
    }

    // 初始化照片格式数据
    private fun initPicFormatData() {
        // 存储图片的格式
        val checkBoxMap = mapOf(
            PictureFormat.VISABLE to checkVisable,
            PictureFormat.ZOOM to checkZoomPic,
            PictureFormat.IR to checkIrPic,
            PictureFormat.WIDE to checkWidePic
        )
        //存储照片格式
        val picFormat = SpUtil.getSavePictureFormat()
        // 分割字符串并严格匹配
        val formats = picFormat.split(",").map { it.trim().lowercase(Locale.ROOT) }
        formats.forEach { format ->
            checkBoxMap.entries.find { it.key.format == format }?.value?.isChecked = true
        }
    }

    // 照片存储格式
    private fun checkPicTypeListener() {
        setupPicTypeListener("visable", checkVisable)
        setupPicTypeListener("wide", checkWidePic)
        setupPicTypeListener("zoom", checkZoomPic)
        setupPicTypeListener("ir", checkIrPic)
    }

    private fun setupPicTypeListener(type: String, checkBox: AppCompatCheckBox) {
        checkBox.setOnCheckedChangeListener { _, isChecked ->
            if (isChecked) {
                addPicType(type)
            } else {
                if (!removePicType(type)) {
                    Toaster.show("至少保留一种照片类型")
                    checkBox.isChecked = true
                    return@setOnCheckedChangeListener
                }
            }
            savePictureFormat()
        }
    }

    private fun addPicType(type: String) {
        if (!storagePicType.contains(type)) {
            storagePicType.add(type)
        }
    }

    private fun removePicType(type: String): Boolean {
        return if (storagePicType.size > 1) {
            storagePicType.remove(type)
            true
        } else {
            false
        }
    }

    private fun savePictureFormat() {
        SpUtil.setSavePictureFormat(storagePicType.joinToString(","))
        XLogUtil.i(TAG, "当前存储照片类型：$storagePicType")
    }

    // 监听下拉框选择事件
    private fun spinnerListener() {
        taskLostBehavior.setOnItemClickListener { _, _, position, _ ->
            val behavior = TaskLostAction.getByCode(position).value
            if (position == 3) {
                SpUtil.setExitOnRCLost(behavior)
            }
            SpUtil.setExecuteRCLostAction(behavior)
        }
        finishAction.setOnItemClickListener { _, _, position, _ ->
            val action = FinishAction.getByCode(position).value
            SpUtil.setFinishAction(action)
        }
    }

    private fun getDJIInfo() {
        //获取无人机限高范围
        KeyManager.getInstance().getValue(KeyTools.createKey(FlightControllerKey.KeyHeightLimitRange), object : CommonCallbacks.CompletionCallbackWithParam<IntValueConfig> {
            override fun onSuccess(intValueConfig: IntValueConfig?) {
                if (intValueConfig != null) {
                    heightLimitRange.text = "(${intValueConfig.min}~${intValueConfig.max}m)"
                    minGoHomeHeight = intValueConfig.min
                    maxGoHomeHeight = intValueConfig.max
                } else {
                    heightLimitRange.text = "(20~500m)"
                    minGoHomeHeight = 20
                    maxGoHomeHeight = 500
                }
            }
            override fun onFailure(djiError: IDJIError) {
                XLogUtil.d(TAG, "获取无人机限高范围失败：${djiError.toJsonStr()}")
            }
        })

        //遥控器电量
        KeyManager.getInstance().getValue(KeyTools.createKey(RemoteControllerKey.KeyBatteryInfo), object : CommonCallbacks.CompletionCallbackWithParam<BatteryInfo> {
            override fun onSuccess(batteryInfo: BatteryInfo?) {
                if (batteryInfo != null) {
                    rcBattery.text = "${batteryInfo.batteryPercent}%"
                }
            }

            override fun onFailure(djiError: IDJIError) {
                XLogUtil.d(TAG, "获取遥控器电量失败：${djiError.toJsonStr()}")
            }

        })
        //无人机主电池电量百分比监听
        KeyManager.getInstance().getValue(KeyTools.createKey(BatteryKey.KeyChargeRemainingInPercent), object : CommonCallbacks.CompletionCallbackWithParam<Int> {
            override fun onSuccess(uavBaterValue: Int?) {
                if (uavBaterValue != null) {
                    firstBattery.visible()
                    firstBattery.text = "${uavBaterValue}%"
                }
            }

            override fun onFailure(djiError: IDJIError) {
                firstBattery.gone()
                XLogUtil.d(TAG, "获取无人机主电池电量失败：${djiError.toJsonStr()}")
            }

        })
        //无人机右侧电池电量百分比监听
        KeyManager.getInstance().getValue(KeyTools.createKey(BatteryKey.KeyChargeRemainingInPercent, ComponentIndexType.RIGHT), object : CommonCallbacks.CompletionCallbackWithParam<Int> {
            override fun onSuccess(p0: Int?) {
                if (p0 != null) {
                    secondBattery.visible()
                    secondBattery.text = "${p0}%"
                }
            }

            override fun onFailure(djiError: IDJIError) {
                secondBattery.gone()
                XLogUtil.d(TAG, "获取无人机右侧电池电量失败：${djiError.toJsonStr()}")
            }

        })
    }


    @SuppressLint("DefaultLocale")
    private fun uavInfoListener() {
        //无人机连接状态
        /*KeyManager.getInstance().listen(KeyTools.createKey(FlightControllerKey.KeyConnection), context) { _, isConnected ->
            if (isConnected != null) {
                updateControlStyle(isConnected)
            }
        }*/
        //检测RTK是否已开启
        KeyManager.getInstance().listen(KeyTools.createKey(RtkMobileStationKey.KeyRTKEnable), this) { _, isEnable ->
            if (isEnable != null) {
                rtkIsEnable.text = if (isEnable) "已开启" else "未开启"
            }
        }
        /* //获取无人机当前返航高度
         KeyManager.getInstance().listen(KeyTools.createKey(FlightControllerKey.KeyGoHomeHeight), context) { _, newValue ->
             if (newValue != null) {
                 goHomeHeight.setText(newValue.toString())
             }
         }*/

        //遥控器模式监听
        KeyManager.getInstance().listen(KeyTools.createKey(FlightControllerKey.KeyRemoteControllerFlightMode), context) { _, newRcfm ->
            if (newRcfm != null) {
                if (newRcfm == RemoteControllerFlightMode.P) {
                    btnStartTask.isEnabled = true
                    remoteModeTips.gone()
                } else {
                    btnStartTask.isEnabled = false
                    remoteModeTips.visible()
                }
            }
        }
        //监听SD卡状态
        KeyManager.getInstance().listen(KeyTools.createKey(CameraKey.KeyCameraStorageInfos, cameraIndex),
            this) { oldValue: CameraStorageInfos?, cameraStorageInfos: CameraStorageInfos? ->
            if (cameraStorageInfos != null) {
                val sdcardInfo = cameraStorageInfos.getCameraStorageInfoByLocation(CameraStorageLocation.SDCARD)
                if (sdcardInfo != null) {
                    sdcardStorage.text = if (sdcardInfo.storageLeftCapacity > 0) {
                        String.format("%.2f G", sdcardInfo.storageLeftCapacity / 1024.0)
                    } else {
                        "--"
                    }
                }
            }
        }
        updateControlStyle()
    }

    private fun updateControlStyle() {
        homeAdd100.visible()
        homeAdd10.visible()
        homeMinus100.visible()
        homeMinus10.visible()

        startAdd100.visible()
        startAdd10.visible()
        startMinus100.visible()
        startMinus10.visible()

        finishAction.isEnabled = true
        finishAction.setInputDisabled(true)
        taskLostBehavior.isEnabled = true
        taskLostBehavior.setInputDisabled(true)

        goHomeHeight.isEnabled = true
        startHeight.isEnabled = true

        btnStartTask.visible()
        /* if (!isConnect) {
             firstBattery.text = defaultTv
             secondBattery.text = defaultTv
             rcBattery.text = defaultTv
             sdcardStorage.text = defaultTv

             goHomeHeight.setText(defaultTv)
             homeAdd100.gone()
             homeAdd10.gone()
             homeMinus100.gone()
             homeMinus10.gone()

             startHeight.setText(defaultTv)
             startAdd100.gone()
             startAdd10.gone()
             startMinus100.gone()
             startMinus10.gone()

             finishAction.setText(defaultTv)
             finishAction.isEnabled = false
             taskLostBehavior.setText(defaultTv)
             taskLostBehavior.isEnabled = false

             goHomeHeight.isEnabled = false
             startHeight.isEnabled = false

             btnStartTask.gone()
         } else {
             homeAdd100.visible()
             homeAdd10.visible()
             homeMinus100.visible()
             homeMinus10.visible()

             startAdd100.visible()
             startAdd10.visible()
             startMinus100.visible()
             startMinus10.visible()

             finishAction.isEnabled = true
             taskLostBehavior.isEnabled = true

             goHomeHeight.isEnabled = true
             startHeight.isEnabled = true

             btnStartTask.visible()
         }*/
    }

    @SuppressLint("DefaultLocale")
    private fun updateOfflineMissionInfo(missionInfo: MissionInfoBean) {
        val flightPath = missionInfo.flightParams.flightPath
        val childrenFlightPath = missionInfo.flightParams.childrenList?.getOrNull(0)?.flightPath

        val waypointList = if (flightPath?.isEmpty() == true && childrenFlightPath?.isNotEmpty() == true) {
            childrenFlightPath.map { pointInfo -> LatLng(pointInfo.latitude, pointInfo.longitude) }.toMutableList()
        } else if (flightPath?.isNotEmpty() == true) {
            flightPath.map { pointInfo -> LatLng(pointInfo.latitude, pointInfo.longitude) }.toMutableList()
        } else {
            // 处理 flightPath 和 childrenFlightPath 都为空或 null 的情况
            mutableListOf()
        }

        if (waypointList.isNotEmpty()) {
            val totalDistance = WaylineUtil.calculateTotalDistance(waypointList)
            waylineLength.text = String.format("%.2f m", totalDistance)
            if (missionInfo.flightParams.speed == 0.0) {
                updateWaylineTime(missionInfo.flightParams.cruiseSpeed.toInt(), totalDistance)
            }else {
                updateWaylineTime(missionInfo.flightParams.speed.toInt(), totalDistance)
            }
            waypointCount.text = missionInfo.flightParams.THPTotal.toString()
        }
    }

    // 处理线航任务信息
    private fun updateLineMissionInfo(missionJson: MissionJson) {
        try {
            val mission = if (missionJson.isGzipMission == 1) {
                val missionString = CompressUtil.decompressEncode(missionJson.gzipMission)
                JSONArray.parseArray(missionString, Mission::class.java)
            } else {
                missionJson.mission
            }

            if (mission.isNotEmpty()) {
                val waypointList = mission[0].flightpath.map { pointInfo -> LatLng(pointInfo.latitude, pointInfo.longitude) }.toList()
                val totalDistance = if (waypointList.isNotEmpty() && mission[0].flightpath.size > 1) {
                    WaylineUtil.calculateTotalDistance(waypointList)
                } else {
                    mission[0].distanceInterval
                }
                val averageSpeed = calculateAverageSpeed(mission[0].flightpath)
                waylineLength.text = String.format("%.2f m", totalDistance)
                updateWaylineTime(averageSpeed.toInt(), totalDistance)
                waypointCount.text = mission[0].flightpath.size.toString()
            }
        } catch (e: Exception) {
            // 处理异常，例如记录日志或通知用户
            e.printStackTrace()
            XLogUtil.e(TAG, "Error parsing mission JSON: ${e.message}")
        }
    }

    private fun calculateAverageSpeed(flightpath: List<Flightpath>): Double {
        if (flightpath.isEmpty()) return 0.0
        var totalSpeed = 0.0
        for (pointInfo in flightpath) {
            totalSpeed += pointInfo.speed
        }
        return totalSpeed / flightpath.size
    }

    //计算航线预计飞行时间
    private fun updateWaylineTime(speed: Int, distance: Float) {
        val totalMilliseconds: Float = distance / speed
        val minutes = (totalMilliseconds / 60).toInt()
        val seconds = (totalMilliseconds % 60).toInt()
        val formattedTime = String.format(Locale.getDefault(), "%02d m %02d s", minutes, seconds)
        waylineTime.text = formattedTime
    }

    private fun setCameraMode() {
        //设置照片存储类型前必须先把相机模式设置成单拍模式
        KeyManager.getInstance().setValue(KeyTools.createKey(CameraKey.KeyCameraMode, cameraIndex), CameraMode.PHOTO_NORMAL, object : CommonCallbacks.CompletionCallback {
            override fun onSuccess() {
                XLogUtil.d(TAG, "设置相机模式成功")
                setPhotoStorageType()
            }

            override fun onFailure(djiError: IDJIError) {
                XLogUtil.d(TAG, "设置相机模式失败，错误信息为：${djiError.description()}")
            }

        })
    }

    private fun setPhotoStorageType() {
        cameraVideoStreamSourceTypes.clear()
        storagePicType.map { type ->
            when (type) {
                "visable" -> CameraVideoStreamSourceType.VISION_CAMERA
                "wide" -> CameraVideoStreamSourceType.WIDE_CAMERA
                "zoom" -> CameraVideoStreamSourceType.ZOOM_CAMERA
                "ir" -> CameraVideoStreamSourceType.INFRARED_CAMERA
                else -> CameraVideoStreamSourceType.DEFAULT_CAMERA // 默认镜头
            }
        }.forEach { cameraVideoStreamSourceTypes.add(it) }
        val cameraStreamSettingsInfo = CameraStreamSettingsInfo()
        cameraStreamSettingsInfo.setRequestCurrentScreen(false)
        cameraStreamSettingsInfo.setCameraVideoStreamSources(cameraVideoStreamSourceTypes)
        KeyManager.getInstance().setValue(KeyTools.createKey(CameraKey.KeyCaptureCameraStreamSettings, cameraIndex), cameraStreamSettingsInfo, object : CommonCallbacks.CompletionCallback {
            override fun onSuccess() {
                XLogUtil.d(TAG, "设置照片存储类型成功，当前存储类型为：${cameraVideoStreamSourceTypes.toJsonStr()}")
            }

            override fun onFailure(djiError: IDJIError) {
                XLogUtil.d(TAG, "设置照片存储类型失败，错误信息为：${djiError.description()}")
            }

        })
    }
}