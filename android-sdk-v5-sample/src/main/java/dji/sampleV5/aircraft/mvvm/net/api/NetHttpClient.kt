package dji.sampleV5.aircraft.mvvm.net.api

import android.os.Build
import android.os.Environment
import androidx.annotation.RequiresApi
import dji.sampleV5.aircraft.mvvm.base.appContext
import dji.sampleV5.aircraft.mvvm.net.interceptor.HeadInterceptor
import dji.sampleV5.aircraft.mvvm.net.interceptor.log.LogRecordInterceptor
import okhttp3.OkHttpClient
import rxhttp.wrapper.cookie.CookieStore
import rxhttp.wrapper.ssl.HttpsUtils
import java.io.File
import java.util.concurrent.TimeUnit

/**
 * @projectName yg_ops
 * @FileName NetHttpClient
 * @data 2024/1/10 18:57
 * <AUTHOR>
 * @description TODO
 **/
object NetHttpClient {
    fun getDefaultOkHttpClient():  OkHttpClient.Builder {
        //在这里面可以写你想要的配置 太多了，我就简单的写了一点，具体可以看rxHttp的文档，有很多
        val sslParams = HttpsUtils.getSslSocketFactory()
        return OkHttpClient.Builder()
            //使用CookieStore对象磁盘缓存,自动管理cookie
            .cookieJar(CookieStore(File(appContext.externalCacheDir, "OpsCookie")))
            .connectTimeout(30, TimeUnit.SECONDS)//读取连接超时时间 15秒
            .readTimeout(30, TimeUnit.SECONDS)
            .writeTimeout(30, TimeUnit.SECONDS)
            .addInterceptor(HeadInterceptor())
            .addInterceptor(LogRecordInterceptor(true))
            .sslSocketFactory(sslParams.sSLSocketFactory, sslParams.trustManager) //添加信任证书
            .hostnameVerifier { hostname, session -> true } //忽略host验证
    }
}