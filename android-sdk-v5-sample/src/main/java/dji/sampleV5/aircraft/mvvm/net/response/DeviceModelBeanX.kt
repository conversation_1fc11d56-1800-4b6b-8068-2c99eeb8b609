package dji.sampleV5.aircraft.mvvm.net.response


import androidx.annotation.Keep

/**
{
"id": 1,
"modelName": "经纬 M300 RTK",
"modelCode": "DJI_M300_RTK",
"brand": "深圳大疆创新科技有限公司",
"appearanceUrl": "https://smartcity.skysys.cn:8061/images/uav_M300.png",
"modelType": 1,
"modelProperty": null
}
 */
@Keep
data class DeviceModelBeanX(
    var appearanceUrl: String, // https://smartcity.skysys.cn:8061/images/uav_M300.png
    var brand: String, // 深圳大疆创新科技有限公司
    var id: Int, // 1
    var modelCode: String, // DJI_M300_RTK
    var modelName: String, // 经纬 M300 RTK
    var modelProperty: Any?, // null
    var modelType: Int // 1
)