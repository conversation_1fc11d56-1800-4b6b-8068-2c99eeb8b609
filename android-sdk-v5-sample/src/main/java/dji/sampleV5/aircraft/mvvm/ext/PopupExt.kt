package dji.sampleV5.aircraft.mvvm.ext

import android.view.View
import androidx.appcompat.app.AppCompatActivity
import com.amap.api.maps.AMap
import com.lxj.xpopup.XPopup
import dji.sampleV5.aircraft.mvvm.widget.popup.MapTypePopup

/**
 * Copyright (C), 2015-2024
 * FileName: PopupExt
 * Author: 80945
 * Date: 2024/12/2 10:16
 * Description: 气泡窗拓展
 */


fun AppCompatActivity.showMapTypePopup(typePopup: MapTypePopup, view: View?) {
    XPopup.Builder(this)
        .hasStatusBar(true)
        .hasNavigationBar(true)
        .hasStatusBarShadow(false)
        .hasShadowBg(false)
        .isTouchThrough(true)
        .isRequestFocus(false)
        .isDestroyOnDismiss(true)
        .atView(view)
        .asCustom(typePopup).show()
}
