package dji.sampleV5.aircraft.mvvm.ui.activity.report

import android.os.Bundle
import androidx.activity.viewModels
import com.google.android.material.tabs.TabLayoutMediator
import com.lxj.xpopup.XPopup
import com.lxj.xpopup.enums.PopupAnimation
import com.lxj.xpopup.enums.PopupPosition
import dji.sampleV5.aircraft.R
import dji.sampleV5.aircraft.databinding.ActivityNewReportBinding
import dji.sampleV5.aircraft.mvvm.base.BaseVMActivity
import dji.sampleV5.aircraft.mvvm.event.LiveDataEvent
import dji.sampleV5.aircraft.mvvm.ext.setOnclickNoRepeat
import dji.sampleV5.aircraft.mvvm.ext.showDialogMessage
import dji.sampleV5.aircraft.mvvm.net.LoadStatusEntity
import dji.sampleV5.aircraft.mvvm.net.response.LocationInfoBean
import dji.sampleV5.aircraft.mvvm.ui.activity.defect.DefectViewModel
import dji.sampleV5.aircraft.mvvm.widget.popup.SubLocationPopup

class NewReportActivity : BaseVMActivity<ReportViewModel, ActivityNewReportBinding>() {
    private val tabList = arrayListOf("任务报告", "场站报告")
    private val defectViewModel: DefectViewModel by viewModels()
    private var locationListInfo: MutableList<LocationInfoBean.MainLocation> = mutableListOf()
    override fun initView(savedInstanceState: Bundle?) {

    }

    override fun initData() {
        defectViewModel.getLocationData()
    }

    override fun onBindViewClick() {
        setOnclickNoRepeat(mBinding.reportToolbar, mBinding.reportBtnBack) {
            when(it.id) {
                R.id.report_toolbar -> {
                    showLocationPopup()
                }
                R.id.report_btn_back -> {
                    finish()
                }
            }
        }
    }

    private fun initTab() {
        mBinding.vpTaskReport.adapter = ReportAdapter(this, locationListInfo[0].subLocationList[0].id)
        TabLayoutMediator(mBinding.tabTaskReport, mBinding.vpTaskReport) { tab, position ->
            tab.text = tabList[position]
        }.attach()
        //设置预加载页面数
        mBinding.vpTaskReport.offscreenPageLimit = tabList.size
    }

    override fun onRequestSuccess() {
        defectViewModel.siteInfoLiveData.observe(this@NewReportActivity) {
            if (it.mainLocationList.size > 0) {
                locationListInfo = it.mainLocationList
                mBinding.reportLocationName.text = it.mainLocationList[0].subLocationList[0].location
                initTab()
            } else {
                mBinding.reportLocationName.text = "暂无场站"
            }
        }
    }

    override fun onRequestError(loadStatus: LoadStatusEntity) {
        showDialogMessage("请求码：${loadStatus.requestCode} \n错误码：${loadStatus.errorCode}, 错误信息：${loadStatus.errorMessage}")
    }

    private fun showLocationPopup() {
        XPopup.Builder(this)
            .isViewMode(true)
            .hasStatusBar(true)
            .isCenterHorizontal(true)
            .atView(mBinding.reportToolbar)
            .popupPosition(PopupPosition.Bottom)
            .popupAnimation(PopupAnimation.ScrollAlphaFromTop)
            .popupWidth(resources.displayMetrics.widthPixels / 2)
            .asCustom(
                SubLocationPopup(
                    this,
                    mBinding.reportLocationName.getText().toString(),
                    locationListInfo
                ) { item ->
                    mBinding.reportLocationName.text = item.location
                    LiveDataEvent.subLocationId.value = item.id
                }
            )
            .show()
    }

    override fun onDestroy() {
        super.onDestroy()
        LiveDataEvent.clearData()
    }
}