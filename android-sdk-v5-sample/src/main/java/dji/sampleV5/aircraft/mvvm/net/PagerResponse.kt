package dji.sampleV5.aircraft.mvvm.net

/**
 * @projectName yg_ops
 * @FileName PagerResponse
 * @data 2024/1/11 9:11
 * <AUTHOR>
 * @description 分页帮助类
 **/
abstract class  BasePage<T> {
    /**
     * 列表数据
     * @return ArrayList<T>
     */
    abstract fun getPageData():ArrayList<T>

    /**
     * 是否是第一页数据
     */
    abstract fun isRefresh(): Boolean
    /**
     * 数据是否为空
     */
    abstract fun isEmpty(): Boolean
    /**
     * 是否还有更多数据
     */
    abstract fun hasMore(): Boolean

    /**
     * 设置分页大小
     */
    abstract fun setPageSize(pageSize: Int)
}