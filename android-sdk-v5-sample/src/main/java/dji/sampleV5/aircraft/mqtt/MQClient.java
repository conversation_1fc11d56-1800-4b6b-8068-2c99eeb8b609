package dji.sampleV5.aircraft.mqtt;

import static org.eclipse.paho.client.mqttv3.MqttConnectOptions.MQTT_VERSION_3_1_1;

import android.content.Context;
import android.text.TextUtils;
import android.util.Base64;
import androidx.annotation.Nullable;
import com.google.gson.Gson;
import org.eclipse.paho.client.mqttv3.IMqttDeliveryToken;
import org.eclipse.paho.client.mqttv3.MqttCallbackExtended;
import org.eclipse.paho.client.mqttv3.MqttClient;
import org.eclipse.paho.client.mqttv3.MqttConnectOptions;
import org.eclipse.paho.client.mqttv3.MqttException;
import org.eclipse.paho.client.mqttv3.MqttMessage;
import org.eclipse.paho.client.mqttv3.persist.MemoryPersistence;

import java.io.IOException;
import java.nio.charset.Charset;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.Timer;
import java.util.TimerTask;
import java.util.concurrent.atomic.AtomicBoolean;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;

import dji.sampleV5.aircraft.ContextUtil;
import dji.sampleV5.aircraft.DJIAircraftApplication;
import dji.sampleV5.aircraft.data.mission.MissionStatus;
import dji.sampleV5.aircraft.data.preference.SpUtil;
import dji.sampleV5.aircraft.lbs.bean.AppLatLng;
import dji.sampleV5.aircraft.mvvm.event.LiveDataEvent;
import dji.sampleV5.aircraft.mvvm.util.XLogUtil;
import dji.sampleV5.aircraft.net.bean.BatteryInfo;
import dji.sampleV5.aircraft.net.bean.MqttBean;
import dji.sampleV5.aircraft.net.bean.NetConfig;
import dji.sampleV5.aircraft.net.bean.UAVInfoSN;
import dji.sampleV5.aircraft.page.login.LoginCache;
import dji.sampleV5.aircraft.util.FormatUtil;
import dji.sampleV5.aircraft.util.ToastUtil;
import dji.sampleV5.aircraft.util.Util;
import dji.sampleV5.aircraft.util.coordinate.GeoSysConversion;
import dji.sampleV5.aircraft.util.coordinate.LatLngUtil;
import dji.sdk.keyvalue.key.BatteryKey;
import dji.sdk.keyvalue.key.CameraKey;
import dji.sdk.keyvalue.key.FlightControllerKey;
import dji.sdk.keyvalue.key.GimbalKey;
import dji.sdk.keyvalue.key.KeyTools;
import dji.sdk.keyvalue.value.camera.CameraThermalPalette;
import dji.sdk.keyvalue.value.common.Attitude;
import dji.sdk.keyvalue.value.common.ComponentIndexType;
import dji.sdk.keyvalue.value.common.LocationCoordinate2D;
import dji.sdk.keyvalue.value.common.Velocity3D;
import dji.v5.common.callback.CommonCallbacks;
import dji.v5.manager.KeyManager;
import okhttp3.Call;
import okhttp3.Callback;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;

public class MQClient {
    private static final String TAG = "MQTTClient";

    // 添加MQTT连接状态枚举
    private enum ConnectionState {
        DISCONNECTED,
        CONNECTING,
        CONNECTED,
        DISCONNECTING
    }
    
    // 添加当前连接状态
    private ConnectionState connectionState = ConnectionState.DISCONNECTED;
    
    // 添加已订阅主题的集合
    private Set<String> subscribedTopics = new HashSet<>();
    
    // 移除健康检查和超时验证相关字段

    private Context context;

    private boolean connected = false;

    private MQttListener mqttListener;
    private String dispatch;

    private String serverUrl;
    private String userName;
    private String pwdString;
    private char[] passWord;
    //private String topicPublish = "toConsoleDebug";
    //private String[] topicFilters = new String[]{"toUAV/p2p/#"};
    private String clientIdFinal;

    private MemoryPersistence mPersistence;
    private MqttConnectOptions options;
    private MqttClient mqttClient;
    final int[] qos = {0};
    private Timer timer_mqtt;
    private TimerTask task_mqtt;
    private String clientID;
    private List<BatteryInfo> batteryInfos = new ArrayList<>();
    private ComponentIndexType cameraIndex = ComponentIndexType.LEFT_OR_MAIN;
    private String testSN = "JINKAIM4TYGTEST";

    public MQClient(Context context) {
        this.context = context;
        LoginCache loginCache = SpUtil.getLoginCache();
        if (loginCache != null) {
            dispatch = loginCache.getDispatch();
        } else {
            XLogUtil.INSTANCE.e(TAG, "LoginCache is null, using default dispatch value");
            dispatch = null;
        }
        if (dispatch != null) {
            serverUrl = dispatch.equals("nj") ? NetConfig.BASE_MqttServerUrl :  NetConfig.MqttServerUrl;
            userName = dispatch.equals("nj") ? NetConfig.BASE_MqttUserName : NetConfig.MqttUserName;
            pwdString = dispatch.equals("nj") ? NetConfig.BASE_MqttPwd : NetConfig.MqttPwd;
        } else {
            serverUrl = NetConfig.MqttServerUrl;
            userName = NetConfig.MqttUserName;
            pwdString = NetConfig.MqttPwd;
        }
    }

    public void setMQttListener(MQttListener listener) {
        this.mqttListener = listener;
    }

    public void init(String sn) {
        if (sn == null) {
            XLogUtil.INSTANCE.e(TAG, "初始化失败：SN为空");
            return;
        }
        XLogUtil.INSTANCE.d(TAG, "开始初始化MQTT，SN: " + sn);
        getUavNameInfo(sn);
        cameraIndex = Util.getCameraIndex();
    }

    public void connect(String clientId) {
        // 只有在DISCONNECTED状态时才尝试连接
        if (connectionState != ConnectionState.DISCONNECTED) {
            XLogUtil.INSTANCE.e(TAG, "连接已存在或正在进行中，当前状态: " + connectionState);
            return;
        }
        
        if (clientId == null || clientId.isEmpty()) {
            XLogUtil.INSTANCE.e(TAG, "连接失败：clientId为空");
            return;
        }
        
        connectionState = ConnectionState.CONNECTING;
        XLogUtil.INSTANCE.d(TAG, "开始连接MQTT服务器: " + serverUrl + ", clientId: " + clientId);
        
        try {
            clientIdFinal = "GID_UAV@@@" + clientId;
            XLogUtil.INSTANCE.d(TAG, "最终clientId: " + clientIdFinal);
            
            if (!TextUtils.isEmpty(pwdString)) {
                passWord = pwdString.toCharArray();
                XLogUtil.INSTANCE.d(TAG, "使用配置的MQTT密码");
            } else {
                passWord = macSignature("GID_UAV", "DwyfjGIBBAtqt34IF9CPLxoEqyC9cI").toCharArray();
                XLogUtil.INSTANCE.d(TAG, "使用签名生成的MQTT密码");
            }

            mPersistence = new MemoryPersistence();
            options = new MqttConnectOptions();
            options.setServerURIs(new String[]{serverUrl});
            options.setUserName(userName);
            options.setPassword(passWord);
            options.setCleanSession(true);
            options.setMqttVersion(MQTT_VERSION_3_1_1);
            options.setAutomaticReconnect(true);
            options.setConnectionTimeout(5);
            options.setKeepAliveInterval(10);
            
            XLogUtil.INSTANCE.d(TAG, "MQTT连接参数设置完成，开始创建客户端");
            mqttClient = new MqttClient(serverUrl, clientIdFinal, mPersistence);
            mqttClient.setCallback(new MyMQTTCallback());
            
            XLogUtil.INSTANCE.d(TAG, "开始连接MQTT服务器...");
            mqttClient.connect(options);
            XLogUtil.INSTANCE.d(TAG, "MQTT连接请求已发送");
            
        } catch (Exception e) {
            connectionState = ConnectionState.DISCONNECTED;
            XLogUtil.INSTANCE.e(TAG, "MQTT连接异常: " + e.getLocalizedMessage() + ": " + e);
            
            if (ContextUtil.getCurrentActivity() != null) {
                ContextUtil.getCurrentActivity().runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                        ToastUtil.show("MQTT连接失败: " + e.getLocalizedMessage());
                    }
                });
            }
        }
    }

    public void getUavNameInfo(String sn) {
        if (NetConfig.mqttTest) {
            sn = testSN;
        }
        getBatteryList();
        // String url = "http://qiyun.skysys.cn/api/v1/uav/fcsns/" + sn + "/uavinfo"; 祺云南京测试站点
        //String url = "https://skyscout.skysys.cn:9921/skysys/api/v1/uav/fcsns/" + sn + "/uavinfo";
        String url = dispatch.equals("nj") ? NetConfig.BASE_JQURl2 : NetConfig.JQURl2 + "uav/fcsns/" + sn + "/uavinfo";
        OkHttpClient client = new OkHttpClient();
        Request request = new Request.Builder().url(url).build();
        client.newCall(request).enqueue(new Callback() {
            @Override
            public void onFailure(Call call, IOException e) {
                ContextUtil.getCurrentActivity().runOnUiThread(() -> ToastUtil.show("获取站点信息失败" + e.getLocalizedMessage()));
                XLogUtil.INSTANCE.e(TAG, "onFailure: " + e.getLocalizedMessage());
            }

            @Override
            public void onResponse(Call call, Response response) throws IOException {
                String result = response.body().string();
                XLogUtil.INSTANCE.e(TAG, "onResponse: " + result);

                // 检查响应是否为空或null
                if (result == null || result.trim().isEmpty()) {
                    XLogUtil.INSTANCE.e(TAG, "服务器返回空响应");
                    ContextUtil.getCurrentActivity().runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            ToastUtil.show("服务器返回空响应");
                        }
                    });
                    return;
                }

                // 检查响应是否为有效的JSON格式
                if (!isValidJson(result)) {
                    XLogUtil.INSTANCE.e(TAG, "服务器返回非JSON格式数据: " + result);
                    ContextUtil.getCurrentActivity().runOnUiThread(() -> ToastUtil.show("服务器返回格式错误: " + result));
                    return;
                }

                UAVInfoSN uavInfoSN;
                try {
                    uavInfoSN = new Gson().fromJson(result, UAVInfoSN.class);
                } catch (Exception e) {
                        XLogUtil.INSTANCE.e(TAG, "JSON解析失败: " + e.getMessage() + ", 原始数据: " + result);
                    ContextUtil.getCurrentActivity().runOnUiThread(() -> ToastUtil.show("数据解析失败: " + e.getMessage()));
                    return;
                }

                if (uavInfoSN == null || uavInfoSN.getData() == null || uavInfoSN.getData().getUAVID() == null) {
                    XLogUtil.INSTANCE.e(TAG, "解析后的数据无效或UAVID为空");
                    ContextUtil.getCurrentActivity().runOnUiThread(() -> ToastUtil.show("该无人机尚未在系统内注册"));
                    LiveDataEvent.INSTANCE.isUavRegister().postValue(false);
                    return;
                }

                LiveDataEvent.INSTANCE.getCurrentUavInfo().postValue(uavInfoSN);
                DJIAircraftApplication.getInstance().setUAVInfoSN(uavInfoSN);
                SpUtil.saveUavInfo(uavInfoSN);
                connect(uavInfoSN.getData().getUAVID());
                clientID = uavInfoSN.getData().getUAVID();
                XLogUtil.INSTANCE.e(TAG, "UAVID: " + uavInfoSN.getData().getUAVID());
            }
        });
    }

    public String macSignature(String text, String secretKey) throws InvalidKeyException, NoSuchAlgorithmException {
        Charset charset = Charset.forName("UTF-8");
        String algorithm = "HmacSHA1";
        Mac mac = Mac.getInstance(algorithm);
        mac.init(new SecretKeySpec(secretKey.getBytes(charset), algorithm));
        byte[] bytes = mac.doFinal(text.getBytes(charset));
        return new String(Base64.encode(bytes, Base64.NO_WRAP), charset);
        //return new String(Base64.encodeBase64(bytes), charset);
    }

    /**
     * 验证字符串是否为有效的JSON格式
     * @param jsonString 待验证的字符串
     * @return true表示有效的JSON，false表示无效
     */
    private boolean isValidJson(String jsonString) {
        if (jsonString == null || jsonString.trim().isEmpty()) {
            return false;
        }

        try {
            // 尝试解析为JSON对象或数组
            String trimmed = jsonString.trim();
            if (trimmed.startsWith("{") && trimmed.endsWith("}")) {
                // 可能是JSON对象
                new Gson().fromJson(trimmed, Object.class);
                return true;
            } else if (trimmed.startsWith("[") && trimmed.endsWith("]")) {
                // 可能是JSON数组
                new Gson().fromJson(trimmed, Object.class);
                return true;
            } else {
                // 不是JSON格式
                return false;
            }
        } catch (Exception e) {
            XLogUtil.INSTANCE.e(TAG, "JSON格式验证失败: " + e.getMessage());
            return false;
        }
    }
    
    class MyMQTTCallback implements MqttCallbackExtended {
        @Override
        public void connectComplete(boolean reconnect, String serverURI) {
            XLogUtil.INSTANCE.d(TAG, "MQTT连接完成 - 服务器: " + serverURI + ", 重连: " + reconnect);
            
            if (ContextUtil.getCurrentActivity() != null) {
                ContextUtil.getCurrentActivity().runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                        ToastUtil.show("MQTT " + (reconnect ? "重连" : "连接") + "成功");
                    }
                });
            }
            
            connected = true;
            connectionState = ConnectionState.CONNECTED;

            if (mqttListener != null) {
                mqttListener.onConnected();
            }

            // 处理订阅，防止重复订阅
            subscribeToTopics(clientID);

            // 无论是首次连接还是重连，都发送状态
            sendStatusDebug();
        }

        @Override
        public void connectionLost(Throwable cause) {
            XLogUtil.INSTANCE.e(TAG, "MQTT连接丢失: " + (cause != null ? cause.getLocalizedMessage() : "未知原因") + ": " + cause);
            
            if (ContextUtil.getCurrentActivity() != null) {
                ContextUtil.getCurrentActivity().runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                        ToastUtil.show("MQTT连接已断开");
                    }
                });
            }
            
            connected = false;
            connectionState = ConnectionState.DISCONNECTED;

            // 清空已订阅主题记录，因为重连后需要重新订阅
            subscribedTopics.clear();
            
            if (mqttListener != null) {
                mqttListener.onDisconnected();
            }
        }

        @Override
        public void messageArrived(String topic, final MqttMessage message) throws Exception {
            XLogUtil.INSTANCE.d(TAG, "收到MQTT消息 - 主题: " + topic + ", 内容: " + message.toString() +
                               ", QoS: " + message.getQos());

            if (mqttListener != null) {
                try {
                    mqttListener.onGetMsg(topic, message.toString());
                    XLogUtil.INSTANCE.d(TAG, "消息处理成功");
                } catch (Exception e) {
                    XLogUtil.INSTANCE.e(TAG, "消息处理失败: " + e.getMessage() +  ": " +  e);
                }
            } else {
                XLogUtil.INSTANCE.w(TAG, "MQTT监听器为空，无法处理消息");
            }
        }

        @Override
        public void deliveryComplete(final IMqttDeliveryToken token) {
            XLogUtil.INSTANCE.d(TAG, "MQTT消息发送完成");
            if (mqttListener != null) {
                mqttListener.onMsgDelivered();
            }
        }
    }

    public void reset() {
        XLogUtil.INSTANCE.d(TAG, "开始重置MQTT客户端");
        
        if (mqttListener != null) {
            mqttListener.onPreRelease();
        }
        
        try {
            if (timer_mqtt != null) {
                timer_mqtt.cancel();
                timer_mqtt.purge();
                timer_mqtt = null;
                XLogUtil.INSTANCE.d(TAG, "MQTT状态发送定时器已停止");
            }
        } catch (Exception e) {
            XLogUtil.INSTANCE.e(TAG, "重置计时器异常: " + e.getMessage() +  ": " +  e);
        }
        
        disconnect();
        connected = false;
        connectionState = ConnectionState.DISCONNECTED;
        subscribedTopics.clear();
        
        if (mqttListener != null) {
            mqttListener.onRelease();
        }
        
        XLogUtil.INSTANCE.d(TAG, "MQTT客户端重置完成");
    }

    public boolean isConnected() {
        boolean clientConnected = mqttClient != null && mqttClient.isConnected();
        boolean stateConnected = connectionState == ConnectionState.CONNECTED;
        
        XLogUtil.INSTANCE.d(TAG, String.format("MQTT连接状态检查 - 客户端连接: %s, 状态连接: %s, 最终结果: %s", 
                                              clientConnected, stateConnected, (clientConnected && stateConnected)));
        
        return clientConnected && stateConnected;
    }

    public void sendStatusDebug() {
        // 先取消现有计时器
        if (timer_mqtt != null) {
            timer_mqtt.cancel();
            timer_mqtt.purge();
            timer_mqtt = null;
        }
        
        // 创建新计时器
        timer_mqtt = new Timer();
        task_mqtt = new MQTTTimeTask();
        timer_mqtt.schedule(task_mqtt, 1500, 500);
    }

    MqttBean mqttBean = new MqttBean();
    double lat84 = 0;
    double lon84 = 0;
    double lat02 = 0;
    double lon02 = 0;
    double lat84home = 0;
    double lon84home = 0;
    double lat02home = 0;
    double lon02home = 0;
    LocationCoordinate2D locationCoordinate2D;
    LocationCoordinate2D locationHome;
    MissionStatus missionStatus;
    Attitude attitude;
    Attitude aircraftAttitude;

    class MQTTTimeTask extends TimerTask {
        @Override
        public void run() {
            boolean isAircraftConnected = DJIAircraftApplication.getInstance().isAircraftConnected();
            if (mqttClient == null || !mqttClient.isConnected() || !isAircraftConnected) {
                XLogUtil.INSTANCE.d(TAG, "MQTT发送条件不满足，跳过本次发送");
                return;
            }
            
            try {
                // 设置位置数据
                if(mqttBean.getLatitudeGCJ() == 0){
                    lat84 = 31.779967;
                    lon84 = 121.227815;
                    lat02 = 31.779967;
                    lon02 = 121.227815;
                    lat84home = 32.060172;
                    lon84home = 118.6471;
                    lat02home = 32.060172;
                    lon02home = 118.6471;
                }

                // 收集飞行器数据
                if (isAircraftConnected) {
                    locationCoordinate2D = KeyManager.getInstance().getValue(KeyTools.createKey(FlightControllerKey.KeyAircraftLocation));
                    if (locationCoordinate2D != null) {
                        Double aircraftLat = locationCoordinate2D.getLatitude();
                        Double aircraftLng = locationCoordinate2D.getLongitude();
                        if (aircraftLat != null && !Double.isNaN(aircraftLat)) {
                            lat84 = aircraftLat;
                            lon84 = aircraftLng;
                            double[] appLatLng02 = GeoSysConversion.wgs84toGCJ02(lat84, lon84);
                            lat02 = appLatLng02[0];
                            lon02 = appLatLng02[1];
                        }


                        locationHome = KeyManager.getInstance().getValue(KeyTools.createKey(FlightControllerKey.KeyHomeLocation));
                        if (locationHome != null) {
                            Double aircraftLatHome = locationHome.getLatitude();
                            Double aircraftLngHome = locationHome.getLongitude();
                            if (aircraftLatHome != null && !Double.isNaN(aircraftLatHome)) {
                                lat84home = aircraftLatHome;
                                lon84home = aircraftLngHome;
                                double[] appLatLngHome02 = GeoSysConversion.wgs84toGCJ02(lat84home, lon84home);
                                lat02home = appLatLngHome02[0];
                                lon02home = appLatLngHome02[1];
                            }
                        }
                    }
                }

                // 构建消息并发送
                try {
                    mqttBean.setLatitudeWGS(lat84);
                    mqttBean.setLongitudeWGS(lon84);
                    mqttBean.setLatitudeGCJ(lat02);
                    mqttBean.setLongitudeGCJ(lon02);
                    mqttBean.setLatitudeHomeGCJ(lat02home);
                    mqttBean.setLongitudeHomeGCJ(lon02home);
                    mqttBean.setLatitudeHomeWGS(lat84home);
                    mqttBean.setLongitudeHomeWGS(lon84home);
                    mqttBean.setIsUAVOnline(isAircraftConnected ? 1 : 0);
                    mqttBean.setState(1);
                    mqttBean.setCompassState(1);
                    mqttBean.setIsOSDKAvailable(0);
                    mqttBean.setVersionName("");
                    mqttBean.setVersionCode(1);
                    mqttBean.setUAVID(clientID);
                    if (NetConfig.mqttTest) {
                        mqttBean.setSN(testSN);
                    } else {
                        if (isAircraftConnected) {
                            String sn = KeyManager.getInstance().getValue(KeyTools.createKey(FlightControllerKey.KeySerialNumber));
                            mqttBean.setSN(sn);
                        } else {
                            //mqttBean.setSN("1ZNBJ8D00C001F");
                        }
                    }

                    missionStatus = DJIAircraftApplication.getInstance().getMissionStatus();
                    if (missionStatus != null) {
                        mqttBean.setWaypointIndex(missionStatus.getCurrentIndex());
                        mqttBean.setMissionID(missionStatus.getMissionID());
                        mqttBean.setMissionBatch(missionStatus.getMissionBatch());
                        mqttBean.setMissionName(missionStatus.getMissionName());
                    }

                    mqttBean.setSDTotalCapacity(DJIAircraftApplication.getInstance().getStorageCapacity());
                    mqttBean.setSDRemainingCapacity(DJIAircraftApplication.getInstance().getLeftStorageCapacity());
                    mqttBean.setIsGSOnline(1);
                    mqttBean.setIsSocketConnected(1);
                    attitude = KeyManager.getInstance().getValue(KeyTools.createKey(GimbalKey.KeyGimbalAttitude));
                    if (attitude != null) {
                        double gimbalPitch = attitude.getPitch();
                        double gimbalRoll = attitude.getRoll();
                        double gimbalYaw = attitude.getYaw();
                        mqttBean.setGimbalPitch((int) gimbalPitch);
                        mqttBean.setGimbalYaw((int) gimbalYaw);
                        mqttBean.setGimbalRoll((int) gimbalRoll);
                    }

                    aircraftAttitude = KeyManager.getInstance().getValue(KeyTools.createKey(FlightControllerKey.KeyAircraftAttitude));
                    if (aircraftAttitude != null) {
                        double yaw = aircraftAttitude.getYaw();
                        double pitch = aircraftAttitude.getPitch();
                        double roll = aircraftAttitude.getRoll();
                        mqttBean.setAzimuth((int) yaw);
                        mqttBean.setUavYaw((int) yaw);
                        mqttBean.setUavPitch((int) pitch);
                        mqttBean.setUavRoll((int) roll);
                    }
                    mqttBean.setSubState(DJIAircraftApplication.getInstance().getSubState());
                    mqttBean.setAreMotorsOn(DJIAircraftApplication.getInstance().isMotorOn());
                    mqttBean.setZoom(DJIAircraftApplication.getInstance().getZoom());

                    float startDistance = LatLngUtil.calculateLineDistance(lat84home, lon84home, lat84, lon84);
                    mqttBean.setDistanceStart((int) startDistance);
                    AppLatLng lastWaypoint = DJIAircraftApplication.getInstance().getLastWaypoint();
                    if(lastWaypoint != null){
                        float endDistance = LatLngUtil.calculateLineDistance(lastWaypoint.getLat(), lastWaypoint.getLng(), lat84, lon84);
                        mqttBean.setDistanceEnd((int) endDistance);
                    }else {
                        mqttBean.setDistanceEnd((int) startDistance);
                    }

                    mqttBean.setManualMode(DJIAircraftApplication.getInstance().isVsEnable);

                    SimpleDateFormat mSimpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                    mqttBean.setTimestamp(mSimpleDateFormat.format(System.currentTimeMillis()));

                    if (isAircraftConnected) {

                        if (batteryInfos.size() > 0 && !batteryInfos.get(0).getBatteryID().isEmpty()) {
                            mqttBean.setBatteries(batteryInfos);
                        }

                        double aircraftAltRel = KeyManager.getInstance().getValue(KeyTools.createKey(FlightControllerKey.KeyAltitude));
                        mqttBean.setAltitude((float) aircraftAltRel);
                        Velocity3D velocity3D = KeyManager.getInstance().getValue((KeyTools.createKey(FlightControllerKey.KeyAircraftVelocity)));
                        float vX = velocity3D.getX().floatValue();
                        float vY = velocity3D.getY().floatValue();
                        float vZ = velocity3D.getZ().floatValue();
                        mqttBean.setHorizontalSpeed(calHSpeed(vX, vY));
                        mqttBean.setVerticalSpeed(vZ);
                    } else {
                        mqttBean.setAltitude(0);
                        mqttBean.setHorizontalSpeed(0);
                        mqttBean.setVerticalSpeed(0);
                    }
                    Boolean isRecordObj = KeyManager.getInstance().getValue(KeyTools.createKey(CameraKey.KeyIsRecording));
                    boolean isRecord = isRecordObj != null ? isRecordObj : false;
                    mqttBean.setRecording(isRecord);

                    mqttBean.setDisplayMode(DJIAircraftApplication.getInstance().getDisplayMode());
                    mqttBean.setIsGimbalFollow(1);

                    Integer satelliteCount = KeyManager.getInstance().getValue(KeyTools.createKey(FlightControllerKey.KeyGPSSatelliteCount));
                    mqttBean.setGPSCount(satelliteCount);

                    CameraThermalPalette cameraThermalPalette = DJIAircraftApplication.getInstance().getCameraThermalPalette();
                    if(cameraThermalPalette != null){
                        MqttBean.ThlLensParam thlLensParam = new MqttBean.ThlLensParam();
                        thlLensParam.setThlPalette(cameraThermalPalette.name());
                        MqttBean.CameraSourceInfo cameraSourceInfo = new MqttBean.CameraSourceInfo();
                        cameraSourceInfo.setThlLensParam(thlLensParam);
                        mqttBean.setCameraSourceInfo(cameraSourceInfo);
                    }

                    mqttBean.setCameraNum(1);
                    mqttBean.setMediaupLoadComplete(false);

                    // 检查连接状态，避免在断开连接期间发送
                    if (mqttClient != null && mqttClient.isConnected()) {
                        String js = new Gson().toJson(mqttBean);
                        MqttMessage mqttMessage = new MqttMessage(js.getBytes());
                        mqttMessage.setQos(0);
                        mqttClient.publish(NetConfig.MqttTopicPublish, mqttMessage);
                        XLogUtil.INSTANCE.d(TAG, "MQTT发送成功：" +  js);
                    } else {
                        XLogUtil.INSTANCE.d(TAG, "MQTT客户端已断开，取消发送");
                    }
                } catch (Exception e) {
                    XLogUtil.INSTANCE.e(TAG, "MQTT发送失败: " + e.getMessage());
                }
            } catch (Exception e) {
                XLogUtil.INSTANCE.e(TAG, "MQTT任务异常: " + e.getMessage());
            }
        }
    }

    private float calHSpeed(float vX, float vY) {
        float hSpeed = 0;
        if (!Float.isNaN(vX) && !Float.isNaN(vY)) {
            hSpeed = (float) Math.sqrt(Math.pow(vX, 2) +
                    Math.pow(vY, 2));

        }
        return hSpeed;
    }

    private void disconnect() {
        if (connectionState == ConnectionState.DISCONNECTED) {
            XLogUtil.INSTANCE.d(TAG, "MQTT已断开连接，跳过断开操作");
            return;
        }
        
        XLogUtil.INSTANCE.d(TAG, "开始断开MQTT连接，当前状态: " + connectionState);
        connectionState = ConnectionState.DISCONNECTING;
        
        if (mqttClient != null) {
            try {
                // 取消所有订阅
                if (!subscribedTopics.isEmpty() && mqttClient.isConnected()) {
                    String[] topics = subscribedTopics.toArray(new String[0]);
                    mqttClient.unsubscribe(topics);
                    XLogUtil.INSTANCE.d(TAG, "取消订阅主题: " + subscribedTopics.size() + "个");
                }
                
                // 断开连接
                mqttClient.disconnect();
                mqttClient.setCallback(null);
                mqttClient = null;
                
                // 清空订阅记录
                subscribedTopics.clear();
                XLogUtil.INSTANCE.d(TAG, "MQTT连接已断开");
                
            } catch (MqttException e) {
                XLogUtil.INSTANCE.e(TAG, "MQTT断开连接异常: " + e.getLocalizedMessage() +  ": " +  e);
            } finally {
                connectionState = ConnectionState.DISCONNECTED;
                connected = false;
            }
        } else {
            XLogUtil.INSTANCE.d(TAG, "MQTT客户端为空，无需断开");
            connectionState = ConnectionState.DISCONNECTED;
            connected = false;
        }
    }

    public interface MQttListener {
        void onConnected();

        void onDisconnected();

        void onAuthenticationFailed();

        void onPreRelease();

        void onRelease();

        void onGetMsg(String topic, String msg);

        void onMsgDelivered();
    }

    /*private Runnable getBatteriesInfoRunnable = new Runnable() {
        @Override
        public void run() {
            getBatteryList();
        }
    };*/

    public void getBatteryList() {
        batteryInfos.clear();
        BatteryInfo batteryInfo1 = new BatteryInfo();
        batteryInfo1.setIndex(0);
        BatteryInfo batteryInfo2 = new BatteryInfo();
        batteryInfo2.setIndex(1);
        batteryInfos.add(batteryInfo1);
        batteryInfos.add(batteryInfo2);
        getBatteriesInfo(ComponentIndexType.LEFT_OR_MAIN);

    }

    public void getBatteriesInfo(ComponentIndexType componentIndexType) {
        XLogUtil.INSTANCE.e(TAG, "getBatteriesInfo: " + componentIndexType.toString());

        String sn = KeyManager.getInstance().getValue(KeyTools.createKey(BatteryKey.KeySerialNumber, componentIndexType));
        batteryInfos.get(0).setBatteryID(sn);
        batteryInfos.get(1).setBatteryID(sn);
        if (sn == null) {
            XLogUtil.INSTANCE.e(TAG, "getBatteriesInfo: 没有获取到电池信息，1.5秒后重新获取");
            ContextUtil.getHandler().postDelayed(new Runnable() {
                @Override
                public void run() {
                    getBatteriesInfo(ComponentIndexType.LEFT_OR_MAIN);
                }
            }, 1500);
            return;
        }
        XLogUtil.INSTANCE.e(TAG, "getBatteriesInfo: KeySerialNumber:" + sn);
        String fv = KeyManager.getInstance().getValue(KeyTools.createKey(BatteryKey.KeyFirmwareVersion, componentIndexType));
        batteryInfos.get(0).setFirmwareVersion(fv);
        batteryInfos.get(1).setFirmwareVersion(fv);
        XLogUtil.INSTANCE.e(TAG, "getBatteriesInfo: KeyFirmwareVersion:" + fv);
        List<Integer> cellVoltages = KeyManager.getInstance().getValue(KeyTools.createKey(BatteryKey.KeyCellVoltages, componentIndexType));
        //Log.e(TAG, "getBatteriesInfo: cellVoltages:" + cellVoltages.size());
        if (cellVoltages != null) {
            for (int j = 0; j < cellVoltages.size(); j++) {
                Integer cellVoltage = cellVoltages.get(j);
                if (j == 0) {
                    batteryInfos.get(0).setCellVoltage1(cellVoltage);
                    batteryInfos.get(1).setCellVoltage1(cellVoltage);
                } else if (j == 1) {
                    batteryInfos.get(0).setCellVoltage2(cellVoltage);
                    batteryInfos.get(1).setCellVoltage2(cellVoltage);
                } else if (j == 2) {
                    batteryInfos.get(0).setCellVoltage3(cellVoltage);
                    batteryInfos.get(1).setCellVoltage3(cellVoltage);
                } else if (j == 3) {
                    batteryInfos.get(0).setCellVoltage4(cellVoltage);
                    batteryInfos.get(1).setCellVoltage4(cellVoltage);
                } else if (j == 4) {
                    batteryInfos.get(0).setCellVoltage5(cellVoltage);
                    batteryInfos.get(1).setCellVoltage5(cellVoltage);
                } else if (j == 5) {
                    batteryInfos.get(0).setCellVoltage6(cellVoltage);
                    batteryInfos.get(1).setCellVoltage6(cellVoltage);
                } else if (j == 6) {
                    batteryInfos.get(0).setCellVoltage7(cellVoltage);
                    batteryInfos.get(1).setCellVoltage7(cellVoltage);
                } else if (j == 7) {
                    batteryInfos.get(0).setCellVoltage8(cellVoltage);
                    batteryInfos.get(1).setCellVoltage8(cellVoltage);
                }
            }
        }


        KeyManager.getInstance().listen(KeyTools.createKey(BatteryKey.KeyCurrent, componentIndexType), this, new CommonCallbacks.KeyListener<Integer>() {
            @Override
            public void onValueChange(@Nullable Integer oldValue, @Nullable Integer newValue) {
                if (newValue != null) {
                    batteryInfos.get(0).setCurrent(newValue);
                    batteryInfos.get(1).setCurrent(newValue);
                    //Log.e(TAG, componentIndexType + "getBatteriesInfo: KeyCurrent:" + newValue);
                }
            }
        });
        KeyManager.getInstance().listen(KeyTools.createKey(BatteryKey.KeyVoltage, componentIndexType), this, new CommonCallbacks.KeyListener<Integer>() {
            @Override
            public void onValueChange(@Nullable Integer oldValue, @Nullable Integer newValue) {
                if (newValue != null) {
                    batteryInfos.get(0).setVoltage(newValue);
                    batteryInfos.get(1).setVoltage(newValue);
                }
                //Log.e(TAG, componentIndexType + "getBatteriesInfo: voltage:" + newValue);
            }
        });
        KeyManager.getInstance().listen(KeyTools.createKey(BatteryKey.KeyChargeRemainingInPercent, componentIndexType), this, new CommonCallbacks.KeyListener<Integer>() {
            @Override
            public void onValueChange(@Nullable Integer oldValue, @Nullable Integer newValue) {
                if (newValue != null) {
                    batteryInfos.get(0).setBatteryPercent(newValue);
                    batteryInfos.get(1).setBatteryPercent(newValue);
                }
                //Log.e(TAG, componentIndexType + "getBatteriesInfo: chargeRemainingInPercent:" + newValue);
            }
        });
        KeyManager.getInstance().listen(KeyTools.createKey(BatteryKey.KeyBatteryTemperature, componentIndexType), this, new CommonCallbacks.KeyListener<Double>() {
            @Override
            public void onValueChange(@Nullable Double oldValue, @Nullable Double newValue) {
                if (newValue != null) {
                    batteryInfos.get(0).setTemperature(FormatUtil.getFloatByFormat(newValue.floatValue(), 2));
                    batteryInfos.get(1).setTemperature(FormatUtil.getFloatByFormat(newValue.floatValue(), 2));
                }
            }
        });

        KeyManager.getInstance().listen(KeyTools.createKey(BatteryKey.KeyNumberOfDischarges, componentIndexType), this, new CommonCallbacks.KeyListener<Integer>() {
            @Override
            public void onValueChange(@Nullable Integer oldValue, @Nullable Integer newValue) {
                if (newValue != null) {
                    batteryInfos.get(0).setCycleCount(newValue);
                    batteryInfos.get(1).setCycleCount(newValue);
                }
            }
        });


        batteryInfos.get(0).setIsCharge(0);
        batteryInfos.get(1).setIsCharge(0);
    }

    // 添加订阅管理方法
    private void subscribeToTopics(String clientId) {
        if (mqttClient == null || !mqttClient.isConnected()) {
            XLogUtil.INSTANCE.e(TAG, "MQTT客户端未连接，无法订阅");
            return;
        }

        if (clientId == null || clientId.isEmpty()) {
            XLogUtil.INSTANCE.e(TAG, "clientId为空，无法订阅主题");
            return;
        }

        try {
            String topic;
            if(TextUtils.isEmpty(NetConfig.TOPIC_PREFIX)){
                topic = clientId;
                XLogUtil.INSTANCE.d(TAG, "使用无前缀主题: " + topic);
            } else {
                topic = NetConfig.TOPIC_PREFIX + clientId + "/";
                XLogUtil.INSTANCE.d(TAG, "使用带前缀主题: " + topic + " (前缀: " + NetConfig.TOPIC_PREFIX + ")");
            }

            String[] topicFilters = new String[]{"toUAV/p2p/" + topic};
            XLogUtil.INSTANCE.d(TAG, "准备订阅主题: " + topicFilters[0]);

            // 检查是否有任何新主题需要订阅
            boolean needToSubscribe = false;
            for (String topicFilter : topicFilters) {
                if (!subscribedTopics.contains(topicFilter)) {
                    needToSubscribe = true;
                    break;
                }
            }

            // 如果有新主题，执行订阅
            if (needToSubscribe) {
                XLogUtil.INSTANCE.d(TAG, "开始订阅新主题...");
                mqttClient.subscribe(topicFilters, qos);

                // 更新已订阅主题集合
                for (String topicFilter : topicFilters) {
                    if (!subscribedTopics.contains(topicFilter)) {
                        subscribedTopics.add(topicFilter);
                        XLogUtil.INSTANCE.d(TAG, "成功订阅主题: " + topicFilter);
                    }
                }
                XLogUtil.INSTANCE.d(TAG, "订阅完成，当前已订阅主题数: " + subscribedTopics.size());
            } else {
                XLogUtil.INSTANCE.d(TAG, "所有主题已订阅，跳过订阅操作");
            }
        } catch (Exception e) {
            XLogUtil.INSTANCE.e(TAG, "MQTT订阅异常: " + e.getLocalizedMessage() +  ": " +  e);

            // 订阅失败时，清空订阅记录以便重试
            subscribedTopics.clear();

            // 延迟重试订阅
            ContextUtil.getHandler().postDelayed(new Runnable() {
                @Override
                public void run() {
                    XLogUtil.INSTANCE.d(TAG, "尝试重新订阅主题...");
                    subscribeToTopics(clientId);
                }
            }, 5000); // 5秒后重试
        }
    }

    // 添加手动重试订阅的方法
    public void retrySubscription() {
        if (clientID != null && !clientID.isEmpty()) {
            XLogUtil.INSTANCE.d(TAG, "手动重试订阅，clientID: " + clientID);
            subscribeToTopics(clientID);
        } else {
            XLogUtil.INSTANCE.e(TAG, "无法重试订阅：clientID为空");
        }
    }
    
    // 获取订阅状态信息
    public String getSubscriptionStatus() {
        StringBuilder status = new StringBuilder();
        status.append("MQTT订阅状态:\n");
        status.append("- 连接状态: ").append(connectionState).append("\n");
        status.append("- 客户端连接: ").append(mqttClient != null && mqttClient.isConnected()).append("\n");
        status.append("- 已订阅主题数: ").append(subscribedTopics.size()).append("\n");

        if (!subscribedTopics.isEmpty()) {
            status.append("- 订阅的主题:\n");
            for (String topic : subscribedTopics) {
                status.append("  * ").append(topic).append("\n");
            }
        }

        return status.toString();
    }


}
