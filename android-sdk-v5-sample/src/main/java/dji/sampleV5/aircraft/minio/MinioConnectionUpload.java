package dji.sampleV5.aircraft.minio;

import android.media.ExifInterface;
import android.os.Environment;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.io.File;
import java.io.IOException;
import java.net.URI;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.Locale;
import java.util.concurrent.TimeUnit;

import dji.sampleV5.aircraft.data.mission.LaserUploadBean;
import dji.sampleV5.aircraft.data.preference.SpUtil;
import dji.sampleV5.aircraft.ftp.OnThreadResultListener;
import dji.sampleV5.aircraft.mvvm.ext.CommExtKt;
import dji.sampleV5.aircraft.mvvm.util.XLogUtil;
import dji.sampleV5.aircraft.net.bean.NetConfig;
import dji.sampleV5.aircraft.util.ToastUtil;
import dji.v5.manager.datacenter.media.MediaFile;
import okhttp3.Call;
import okhttp3.Callback;
import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;
import okhttp3.ResponseBody;
import software.amazon.awssdk.auth.credentials.AwsBasicCredentials;
import software.amazon.awssdk.auth.credentials.StaticCredentialsProvider;
import software.amazon.awssdk.http.urlconnection.UrlConnectionHttpClient;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.s3.S3Client;
import software.amazon.awssdk.services.s3.model.CreateBucketRequest;
import software.amazon.awssdk.services.s3.model.PutObjectRequest;
import software.amazon.awssdk.services.s3.model.Bucket;

public class MinioConnectionUpload {
    private final String TAG = this.getClass().getName();
    private OnThreadResultListener listener;
    private ArrayList<MediaFile> uploadMediaList;
    private JSONArray jsonArray = new JSONArray();
    private String minioPath;
    private String missionBatch;
    private S3Client s3Client;
    private boolean is_connected = false;
    private int index = 0;
    private SimpleDateFormat sf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.CHINA);
    private List<LaserUploadBean> laserUploadBeanList;
    private long lastNoticeTime;
    private String lastDir;

    /**
     * 构造函数
     */
    public MinioConnectionUpload(String missionBatch, String minioPath, ArrayList<MediaFile> uploadMediaList, OnThreadResultListener listener) {
        this.minioPath = minioPath;
        this.missionBatch = missionBatch;
        this.uploadMediaList = uploadMediaList;
        this.listener = listener;
        is_connected = false;
        laserUploadBeanList = SpUtil.getLaserList();
        
        // 初始化MinIO客户端
        s3Client = S3Client.builder()
                .httpClientBuilder(UrlConnectionHttpClient.builder())
                .endpointOverride(URI.create(NetConfig.MINIO_ENDPOINT))
                .credentialsProvider(StaticCredentialsProvider.create(
                        AwsBasicCredentials.create(NetConfig.MINIO_ACCESS_KEY, NetConfig.MINIO_SECRET_KEY)))
                .region(Region.of(NetConfig.MINIO_REGION))
                .build();
                
        try {
            XLogUtil.INSTANCE.e(TAG, "MinioConnectionUpload: start connect");
            initConnect();
        } catch (IOException e) {
            XLogUtil.INSTANCE.e(TAG, "initConnect IOException:" + e.getCause() + "," + e.getLocalizedMessage() + "," + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 初始化连接
     */
    private void initConnect() throws IOException {
        try {
            // 检查桶是否存在，不存在则创建
            boolean bucketExists = false;
            for (Bucket bucket : s3Client.listBuckets().buckets()) {
                if (bucket.name().equals(NetConfig.MINIO_BUCKET_NAME)) {
                    bucketExists = true;
                    break;
                }
            }
            
            if (!bucketExists) {
                s3Client.createBucket(CreateBucketRequest.builder().bucket(NetConfig.MINIO_BUCKET_NAME).build());
            }
            
            // 创建必要的目录结构
            createDirectories(minioPath + "photo/THRM/");
            createDirectories(minioPath + "photo/ZOOM/");
            createDirectories(minioPath + "photo/WIDE/");
            createDirectories(minioPath + "photo/V/");
            createDirectories(minioPath + "video/THRM/");
            createDirectories(minioPath + "video/ZOOM/");
            createDirectories(minioPath + "video/WIDE/");
            createDirectories(minioPath + "video/V/");
            createDirectories(minioPath + "video/S/");
            
            is_connected = true;
            XLogUtil.INSTANCE.e(TAG, "-----minio connect-");
        } catch (Exception e) {
            is_connected = false;
            XLogUtil.INSTANCE.e(TAG, "Can't connect to MinIO server: " + e.getMessage());
            throw new IOException("Can't connect to MinIO server: " + e.getMessage());
        }
    }

    public void reConnect() {
        try {
            XLogUtil.INSTANCE.e(TAG, "MinioConnectionUpload: start reConnect");
            initConnect();
        } catch (IOException e) {
            XLogUtil.INSTANCE.e(TAG, "reConnect IOException:" + e.getCause() + "," + e.getLocalizedMessage() + "," + e.getMessage());
            e.printStackTrace();
        }
    }

    public void start() {
        try {
            if (index >= uploadMediaList.size()) {
                if (System.currentTimeMillis() - lastNoticeTime < 10000) {
                    return;
                }
                ToastUtil.show("上传完毕");
                XLogUtil.INSTANCE.e("MinioConnectionUpload", "start: 上传完毕");
                noticeNJ();
                disconnect();
                SpUtil.setLaserList(null);
                return;
            }
            
            MediaFile mediaFile = uploadMediaList.get(index);
            String originFilename = mediaFile.getFileName();
            String downloadName = originFilename.substring(0, originFilename.lastIndexOf("."));
            String suffix = ".jpg";
            if (originFilename.toLowerCase().contains("mp4")) {
                suffix = ".mp4";
            }
            File file = new File(Environment.getExternalStorageDirectory().getPath() + "/Skysys/download/" + downloadName + suffix);

            String uploadName = file.getName();
            String mediaUrl = setpath(minioPath, uploadName);
            XLogUtil.INSTANCE.e("MinioConnectionUpload", "index = " + index + "  mediaUrl: " + mediaUrl);

            float targetDistance = -1;
            if (laserUploadBeanList != null && laserUploadBeanList.size() > 0) {
                for (LaserUploadBean laserUploadBean : laserUploadBeanList) {
                    if (laserUploadBean.getIndex() == mediaFile.getFileIndex()) {
                        targetDistance = laserUploadBean.getTargetDistance();
                        break;
                    }
                }
            }

            // 构建上传元数据
            JSONObject jsonObject = buildMetadata(uploadName, mediaFile, targetDistance, file);
            jsonArray.put(jsonObject);

            upload(mediaUrl, uploadName, file);
        } catch (IOException | JSONException e) {
            listener.onError(index, "upload " + e.getLocalizedMessage(), null);
            XLogUtil.INSTANCE.e("MinioConnectionUpload", "start upload error: " + e.getLocalizedMessage());
            e.printStackTrace();
        }
    }

    private JSONObject buildMetadata(String uploadName, MediaFile mediaFile, float targetDistance, File file) throws JSONException, IOException {
        JSONObject jsonObject = new JSONObject();
        String fileType = "";
        String mediaViewType = "";

        jsonObject.put("mediaFileName", uploadName);
        if (uploadName.contains("T")) {
            fileType = "THRM";
            mediaViewType = "THRM";
        } else if (uploadName.contains("W")) {
            fileType = "WIDE";
            mediaViewType = "WIDE";
        } else if (uploadName.contains("Z")) {
            fileType = "ZOOM";
            mediaViewType = "ZOOM";
        } else if (uploadName.contains("V")) {
            fileType = "V";
            mediaViewType = "VISIBLE";
        } else if (uploadName.contains("S")) {
            fileType = "S";
            mediaViewType = "S";
        }

        if (uploadName.toLowerCase().contains("mp4")) {
            jsonObject.put("mediaUrl", minioPath + "video/" + fileType + "/");
            jsonObject.put("mediaType", "video");
            jsonObject.put("resolution", mediaFile.getResolution());
        } else {
            jsonObject.put("mediaUrl", minioPath + "photo/" + fileType + "/");
            jsonObject.put("mediaType", "photo");
            ExifInterface exifInterface = new ExifInterface(file.getAbsolutePath());
            int width = exifInterface.getAttributeInt(ExifInterface.TAG_IMAGE_WIDTH, 0);
            int height = exifInterface.getAttributeInt(ExifInterface.TAG_IMAGE_LENGTH, 0);
            jsonObject.put("resolution", width + "x" + height);
        }

        jsonObject.put("mediaViewType", mediaViewType);
        jsonObject.put("targetDistance", targetDistance);
        jsonObject.put("takePhotoTime", mediaFile.getDate().getYear() + "-" + mediaFile.getDate().getMonth() + "-" + mediaFile.getDate().getDay() + " " + mediaFile.getDate().getHour() + ":" + mediaFile.getDate().getMinute() + ":" + mediaFile.getDate().getSecond());
        
        ExifInterface exifInterface = new ExifInterface(file.getAbsolutePath());
        jsonObject.put("absoluteAltitude", 0);
        jsonObject.put("relativeAltitude", score2dimensionality(exifInterface.getAttribute(ExifInterface.TAG_GPS_ALTITUDE)));
        jsonObject.put("mediaFileSize", mediaFile.getFileSize());
        
        JSONObject shootPositionJsonObject = new JSONObject();
        shootPositionJsonObject.put("latitude", score2dimensionality(exifInterface.getAttribute(ExifInterface.TAG_GPS_LATITUDE)));
        shootPositionJsonObject.put("longitude", score2dimensionality(exifInterface.getAttribute(ExifInterface.TAG_GPS_LONGITUDE)));
        jsonObject.put("shootPosition", shootPositionJsonObject);

        return jsonObject;
    }

    private double score2dimensionality(String string) {
        double dimensionality = 0.0;
        if (null == string) {
            return dimensionality;
        }

        String[] split = string.split(",");
        for (int i = 0; i < split.length; i++) {
            String[] s = split[i].split("/");
            double v = Double.parseDouble(s[0]) / Double.parseDouble(s[1]);
            dimensionality = dimensionality + v / Math.pow(60, i);
        }
        return dimensionality;
    }

    public void upload(String path, String minioFileName, File localFile) throws IOException {
        XLogUtil.INSTANCE.e(TAG, "upload: " + path + minioFileName);
        
        if (!is_connected) {
            listener.onError(index, "minio--未连接", localFile);
            return;
        }
        
        if (s3Client == null) {
            listener.onError(index, "minio client is null", localFile);
            return;
        }
        
        if (!localFile.exists()) {
            listener.onError(index, "This file doesn't exist", localFile);
            throw new IOException("Can't upload '" + localFile.getAbsolutePath() + "'. This file doesn't exist.");
        }

        try {
            String key = path + minioFileName;
            PutObjectRequest putObjectRequest = PutObjectRequest.builder()
                    .bucket(NetConfig.MINIO_BUCKET_NAME)
                    .key(key)
                    .contentType(getContentType(minioFileName))
                    .build();

            s3Client.putObject(putObjectRequest, software.amazon.awssdk.core.sync.RequestBody.fromFile(localFile));
            
            listener.onFinish(index);
            localFile.delete();
            index++;
            XLogUtil.INSTANCE.e(TAG, "下一个: " + index);
            start();
            
        } catch (Exception e) {
            listener.onError(index, "Upload failed: " + e.getMessage(), localFile);
            throw new IOException("Can't upload file '" + minioFileName + "' to MinIO server: " + e.getMessage());
        }
    }

    private String getContentType(String fileName) {
        if (fileName.toLowerCase().endsWith(".jpg") || fileName.toLowerCase().endsWith(".jpeg")) {
            return "image/jpeg";
        } else if (fileName.toLowerCase().endsWith(".mp4")) {
            return "video/mp4";
        }
        return "application/octet-stream";
    }

    private void noticeNJ() {
        JSONObject data = new JSONObject();
        try {
            data.put("missionBatch", missionBatch);
            data.put("endpoint", NetConfig.MINIO_ENDPOINT);
            data.put("accessKeyId", NetConfig.MINIO_ACCESS_KEY);
            data.put("secretAccessKey", NetConfig.MINIO_SECRET_KEY);
            data.put("bucketName", NetConfig.MINIO_BUCKET_NAME);
            data.put("mediaRecordList", jsonArray);
            data.put("storageType", 4); // 使用4表示MinIO存储
        } catch (JSONException e) {
            XLogUtil.INSTANCE.e("MinioConnectionUpload", "构建JSON失败" + e.getLocalizedMessage());
            return;
        }

        OkHttpClient httpClient = new OkHttpClient.Builder()
                .connectTimeout(15, TimeUnit.SECONDS)
                .writeTimeout(20, TimeUnit.SECONDS)
                .readTimeout(30, TimeUnit.SECONDS)
                .build();
                
        MediaType JSON = MediaType.parse("application/json;charset=utf-8");
        XLogUtil.INSTANCE.e("MinioConnectionUpload", "noticeNJ: " + CommExtKt.toJsonStr(data));
        RequestBody requestBody = RequestBody.create(JSON, data.toString());
        String url = NetConfig.NJ_MEDIA + "media/upload";

        Request request = new Request.Builder()
                .url(url)
                .post(requestBody)
                .build();

        Call call = httpClient.newCall(request);
        call.enqueue(new Callback() {
            @Override
            public void onFailure(Call call, IOException e) {
                ToastUtil.show("通知媒体服务失败:" + e.getMessage());
                XLogUtil.INSTANCE.e(TAG, "noticeNJ onFailure: " + call.request() + "    error:" + e.getMessage());
            }

            @Override
            public void onResponse(Call call, Response response) throws IOException {
                try (ResponseBody responseBody = response.body()) {
                    if (response.isSuccessful()) {
                        ToastUtil.show("已通知媒体服务去处理数据");
                        XLogUtil.INSTANCE.e(TAG, "noticeNJ onResponse: " + call.toString() + "  response:" + response);
                    } else {
                        ToastUtil.show("通知媒体服务失败:" + response.code());
                        XLogUtil.INSTANCE.e(TAG, "Unexpected code " + response.code());
                    }
                } catch (Exception e) {
                    XLogUtil.INSTANCE.e(TAG, "Failed to close response body on success: " + e.getMessage());
                }
            }
        });
    }

    public void disconnect() {
        if (s3Client != null) {
            s3Client.close();
            is_connected = false;
        }
    }

    private String setpath(String path, String minioFileName) {
        String pathTo;
        if (minioFileName.contains("JPG") || minioFileName.contains("jpg")) {
            if (minioFileName.contains("T")) {
                pathTo = path + "photo/THRM/";
            } else if (minioFileName.contains("W")) {
                pathTo = path + "photo/WIDE/";
            } else if (minioFileName.contains("Z")) {
                pathTo = path + "photo/ZOOM/";
            } else {
                pathTo = path + "photo/V/";
            }
        } else if (minioFileName.toLowerCase().contains("mp4")) {
            if (minioFileName.contains("V")) {
                pathTo = path + "video/V/";
            } else if (minioFileName.contains("T")) {
                pathTo = path + "video/THRM/";
            } else if (minioFileName.contains("W")) {
                pathTo = path + "video/WIDE/";
            } else if (minioFileName.contains("Z")) {
                pathTo = path + "video/ZOOM/";
            } else if (minioFileName.contains("S")) {
                pathTo = path + "video/S/";
            } else {
                pathTo = path + "video/SINGlELEN/";
            }
        } else {
            pathTo = path;
        }
        return pathTo;
    }

    private void createDirectories(String path) {
        if (path.isEmpty()) return;
        
        String key = path.endsWith("/") ? path : path + "/";
        try {
            PutObjectRequest putReq = PutObjectRequest.builder()
                    .bucket(NetConfig.MINIO_BUCKET_NAME)
                    .key(key)
                    .contentType("application/x-directory")
                    .build();
                    
            s3Client.putObject(putReq, software.amazon.awssdk.core.sync.RequestBody.fromBytes(new byte[0]));
        } catch (Exception e) {
            XLogUtil.INSTANCE.e(TAG, "createDirectories error: " + e.getLocalizedMessage());
        }
    }

    public boolean isConnected() {
        return is_connected;
    }
} 