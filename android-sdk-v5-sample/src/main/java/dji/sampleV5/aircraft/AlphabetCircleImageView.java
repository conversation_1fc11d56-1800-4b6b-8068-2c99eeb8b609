package dji.sampleV5.aircraft;

import android.content.Context;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.util.AttributeSet;

import androidx.annotation.ColorInt;

/**
 * 无人机头像?
 */
public class AlphabetCircleImageView extends CircleImageView {
    private final Paint mAlphabetPaint = new Paint();
    private String alphabet;

    private Paint.FontMetrics fm;
    private float textWidth;

    public AlphabetCircleImageView(Context context, AttributeSet attrs) {
        super(context, attrs);

        init();
    }

    public void setAlphabetColor(@ColorInt int color) {
        mAlphabetPaint.setColor(color);
    }

    /**
     * 设置需要显示的字母，不会与图片同时显示
     */
    public void setAlphabet(String alphabet) {
        if (alphabet.length() > 0) {
            try {
                this.alphabet = HanziToPinyin.getInstance().get(alphabet.substring(0, 1)).get(0).target.substring(0, 1).toUpperCase();
            } catch (Exception e) {
                this.alphabet = "";
            }
        } else {
            this.alphabet = "";
        }
        measureText(this.alphabet);
        setImageBitmap(null);
    }

    @Override
    protected void drawOther(Canvas canvas) {
        super.drawOther(canvas);
        if (fm != null) {
            int mWidth = getWidth();
            int mHeight = getHeight();

            float textCenterVerticalBaselineY = mHeight / 2 - fm.descent + (fm.descent - fm.ascent) / 2;
            canvas.drawText(alphabet, mWidth / 2 - textWidth / 2, textCenterVerticalBaselineY, mAlphabetPaint);
        }
    }

    private void init() {
        mAlphabetPaint.setAntiAlias(true);
        mAlphabetPaint.setTextSize(getResources().getDimensionPixelSize(R.dimen.space_24));
        mAlphabetPaint.setColor(Color.WHITE);
    }

    private void measureText(String alphabet) {
        fm = mAlphabetPaint.getFontMetrics();
        textWidth = mAlphabetPaint.measureText(alphabet);
    }
}
