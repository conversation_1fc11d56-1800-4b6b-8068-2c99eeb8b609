package dji.sampleV5.aircraft.net.bean.qicloud;

/**
 * 站点|无人机 状态
 */
public enum SiteState {
    offline(0, "离线"),
    ch<PERSON><PERSON><PERSON>(31, "初期化"),
    ch<PERSON><PERSON><PERSON><PERSON>(310101, "初期化"),
    ch<PERSON><PERSON><PERSON>(101, "初始化"),
    daim<PERSON>(102, "待命"),
    ka<PERSON><PERSON>(200, "开机库"),
    ren<PERSON><PERSON><PERSON><PERSON>(201, "任务自检"),
    q<PERSON><PERSON><PERSON><PERSON>(202, "起飞自检"),
    chong<PERSON><PERSON><PERSON><PERSON><PERSON>(203, "充电中任务"),
    q<PERSON><PERSON>(204, "起飞"),
    pash<PERSON>(206, "爬升"),
    ren<PERSON><PERSON><PERSON>(209, "任务中"),
    l<PERSON><PERSON><PERSON><PERSON>(210, "陆点任务"),
    <PERSON><PERSON><PERSON><PERSON><PERSON>(215, "动作待命"),
    dog<PERSON><PERSON><PERSON><PERSON>(218, "动作执行"),
    zanti<PERSON>(221, "暂停"),
    xuanting(222, "悬停"),
    quan<PERSON><PERSON><PERSON><PERSON>(223, "全手动"),
    ludian<PERSON><PERSON><PERSON>(224, "陆点悬停"),
    kaijiku2(300, "开机库"),
    deng<PERSON><PERSON>ngdai(301, "等状态"),
    z<PERSON><PERSON><PERSON><PERSON>(302, "自动返航"),
    z<PERSON><PERSON><PERSON><PERSON>(303, "自定义返航"),
    beiji<PERSON>dianjiangluo(305, "备降点降落"),
    fanhangjiangluo(306, "返航降落"),
    zidingyijiangluo(309, "自定义降落"),
    shoudongjierrukongzhi(310, "手动介入控制"),
    guanjiku(312, "关机库"),
    yutian(500, "雨天"),
    wutian(501, "雾天"),
    dafeng(502, "大风"),
    yewan(503, "夜晚"),
    lengque(601, "冷却"),
    bukefei(602, "不可飞"),
    kediaofei(603, "可调飞"),
    shebeiweihu(701, "设备维护"),
    weizhi(999, "未知"),
    daiming2(1000, "待命"),
    wurenjikaiqi(1001, "无人机开启"),
    tongzhiSDKshangchuan(1002, "通知OSDK上传"),
    OSDKtupianshangchuanzhong(1003, "OSDK图片上传中"),
    wurenjinxuguanbi(1004, "无人机需关闭"),
    ruanjiangengxin(1010, "软件更新"),

    // 网联新增

    chuqihuashibai(1998, "初始化失败"),
    chuqihua3(1999, "初始化"),
    zhunbeizhong(2000, "准备中"), // 开机库
    dajiang(2999, "打桨"),
    qifeizhong(3000, "起飞中"), // 开机库
    pasheng1(3999, "爬升"),
    renwuzhong2(4000, "任务中"),
    fanhangzhong2(5000, "返航中"), // 雨天
    jiangluozhong2(6000, "视觉降落中"),
    jinjijiangluo(6001, "紧急降落中"),
    beijiangjiangluo(6002, "备降降落中"),
    tingjiang(6999, "停桨"),
    weihuzhong(7000, "维护中"),
    chongdianzhong(8000, "充电中"),
    chongdiankezhifei(8001, "充电可执飞"),
    chongdianlengque(8002, "充电冷却"),
    dianliangbuzu(9998, "电量不足"),
    xuantigndengdai(9999, "悬停等待"),

    // 小飞机新增状态
    jinjifanhangzhong(5001, "紧急返航中"),
    zhidianzhongfeixing(9997, "指点飞行中"),
    ;

    public final int code;
    public final String value;

    /**
     * 任务类型
     *
     * @param code  编号
     * @param value 内容
     */
    private SiteState(int code, String value) {
        this.code = code;
        this.value = value;
    }

    /**
     * int => strin
     *
     * @param c
     * @return
     */
    public static SiteState getByCode(int c) {
        SiteState[] datas = SiteState.values();
        for (SiteState data : datas) {
            if (data.code == c) {
                return data;
            }
        }
        return null;
    }
}
