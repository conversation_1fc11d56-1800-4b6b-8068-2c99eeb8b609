package dji.sampleV5.aircraft.net.bean;


public class UAVInfo {

    private static UAVInfo sInfo;

    /*public UITIP uitip = UITIP.NONE;
    public GpsInfo gpsInfo = new GpsInfo();


    public String uavNameServer = "";
    public String uavFlightControllerSN = "";
    public UAVAircraft uavAircraft = null;
    public UAVAirLink uavAirLink = null;
    public UAVGimbal uavGimbal = null;
    public UAVBattery uavBattery = null;
    public UAVFlightController uavFlightController = null;
    public UAVCamera uavCamera = null;
    public UAVRemoteController uavRemoteController = null;

    public UwbInfo uwbInfo = new UwbInfo();

    public boolean isFullBattery = false;

    public double firstPotHeight = 2;       //JoyStickMode first pot height

    //JoyStickInfo
    public MissionArray missionArray = new MissionArray();      //无人机动作录制信息
    public UavAttitude uavAttitude = new UavAttitude();         //无人机姿态信息

    public PAGE_TYPE pageType = PAGE_TYPE.NONE;

    public String getUavComponentsLess(){
        String result = "";
        if(uavAircraft==null)result+="Aircraft ";
        if(uavAirLink==null)result+="AirLink ";
        if(uavGimbal==null)result+="Gimbal ";
        if(uavBattery==null)result+="Battery ";
        if(uavFlightController==null)result+="FC ";
        if(uavCamera==null)result+="Camera ";
        if(uavRemoteController==null)result+="Remote ";
        return result.trim();
    }

    //UAV界面视频显示
    public VideoFeedType videoFeedTypePrimary;
    public VideoFeedType videoFeedTypeSecondary;
    public boolean isVideoShowMain = false;
    public boolean isVideoShowSub = false;

//    public boolean isShowPrimary = false;
//    public boolean isShowSecoundary = false;

    //USB串口相关
    public USB_PERMISSION usbPermission = USB_PERMISSION.Unknown;
    public boolean usbSerialConnected = false;
    public UsbSerialPort usbSerialPort;
    public RSState rsState = RSState.INIT;

    //USB UWB串口相关
    public USB_PERMISSION usbUwbPermission = USB_PERMISSION.Unknown;
    public boolean usbUwbSerialConnected = false;
    public UsbSerialPort usbUwbSerialPort;

    public boolean heartBeatToOsdk = false;
    public boolean heartBeatToOsdkPause = false;
    public boolean osdkLostGohomeEnable = false;

    // 高度配置信息
    public ConfigAltitude configAltitude = new ConfigAltitude();
    public float hiveAltitude = 5;      //站点高度
    public float RHAltitude = 100;      //返航高度
    // 机库状态信息
    public HiveStateInfo hiveStateInfo = new HiveStateInfo();
    public boolean permissionPass = false;
    public boolean registrationPass = false;
    public boolean isCheckBeforeLaunchPass = false;
    public boolean uavAlreadyActive = false;
    public boolean isActivityDroneSuccess = false;

    public boolean isNetWorkAvailable = false;
    public long netWorkDelay = 0;
    public static Date uploadDate = DateTimeUtil.getDateInc(new Date(), Calendar.DATE, -1);

    public static LocateInfo landPot02 = null;        //备降点坐标信息
    public static LocateInfo landPot84 = null;        //备降点坐标信息
    public static LocateInfo hiveLocation02 = null;     //机库坐标
    public static LocateInfo UAVSafeLocation02 = null;   //安全点坐标

    //变量
    //public int virtualStickModeEnabled = CONSTANT.BOOLEAN_DEFAULT;
    //public int virtualStickAdvancedModeEnabled = -1;
    //public int pitchRangeExtensionEnabled = -1;

    public HIVE_STATUS hiveStatus = HIVE_STATUS.INIT;
    public HIVE_STATE hiveState = HIVE_STATE.INIT;
    public RTH_TYPE rtnType = RTH_TYPE.RTH_NO;
    public UPLOAD_TYPE uploadType = UPLOAD_TYPE.INIT;
    public long osdk_datarec_time = new Date().getTime();
    public int osdk_heartbeat = -1;
    public boolean isOSDKAvailable = false;                 //MQTT上传时更新状态

    public long joystick_control_time = new Date().getTime();

    public RTH_STATE rtnState = RTH_STATE.RTH_NO;
    public FLYING_STATE flyingState = FLYING_STATE.FLYING_NO;
    public MISSION_SORT mission_sort = MISSION_SORT.UNKNOWN;

    public LAUNCH_FLOW_STATE launchFlowState = LAUNCH_FLOW_STATE.NONE;
    public int isRTBS = 0;          //0未进自降  1自降开始  2 自降失败

    private float distanceStart = MISSION_C.INT_ZERO;
    private double distanceMission = MISSION_C.INT_ZERO;
    private double distanceReturn = MISSION_C.INT_ZERO;
    public float headingAngle = MISSION_C.INT_ZERO;
    public float headingAngleLaunch = MISSION_C.INT_ZERO;
    public int ocuSyncLinkChannelNumber = MISSION_C.INT_ZERO;                //current OcuSync Link Channel Number
    public float gimbalPitch = MISSION_C.INT_ZERO;
    public float gimbalRoll = MISSION_C.INT_ZERO;
    public float gimbalYaw = MISSION_C.INT_ZERO;
    public double uavPitch =  MISSION_C.INT_ZERO;
    public double uavRoll =  MISSION_C.INT_ZERO;
    public double uavYaw =  MISSION_C.INT_ZERO;
    public int frequency = MISSION_C.INT_ONE;                               //无人机传输的频率，枚举型，2.4G，5.8G，M3G，M4G，M5G(1,2,3,4,5)(默认值1)
    public String missionID = "";
    public String missionBatch = "";
    public int versionCode = 0;
    public String versionName = "";
    public boolean missionOKConfirming = false;
    public boolean missionOKConfirmed = false;
    public int aliveTime = MISSION_C.INT_ZERO;
    public int cellular = MISSION_C.INT_ZERO;

    public float theIDATRMin = -1.0f;       //热成像 相机动态区域温度最小值，默认值-1.0
    public float theIDATRMax = -1.0f;       //热成像 相机动态区域温度最大值，默认值-1.0
    public float theIT2AT = -1.0f;            //热成像 相机平均温度值，默认值 -1.0
    public Point minposion = new Point(-1,-1);                             //最小温度坐标
    public Point maxposion = new Point(-1,-1);                             //最大温度坐标
    public ArrayList<Point> theIDATLocation = new ArrayList<Point>(){{add(new Point(-1,-1));add(new Point(-1,-1));}};  // 0 最小温度坐标，1 最大温度坐标

    //拍照缓存
    public float theIDATRMin2 = -1.0f;       //热成像 相机动态区域温度最小值，默认值-1.0
    public float theIDATRMax2 = -1.0f;       //热成像 相机动态区域温度最大值，默认值-1.0
    public float theIT2AT2 = -1.0f;            //热成像 相机平均温度值，默认值 -1.0
    public Point minposion2 = new Point(-1,-1);                             //最小温度坐标
    public Point maxposion2 = new Point(-1,-1);                             //最大温度坐标
    public ArrayList<Point> theIDATLocation2 = new ArrayList<Point>(){{add(new Point(-1,-1));add(new Point(-1,-1));}};  // 0 最小温度坐标，1 最大温度坐标
    public double Longitude84;                  //打点84
    public double Latitude84;                   //打点84


    public float vfov = 0.0f;           //纵方向 fov
    public float hfov = 0.0f;           //横放心 fov


    public int checkTimesBeforeLaunch = MISSION_C.INT_ZERO;

    public String flightModeSwitch = "P" ;
    public boolean isFlightModeSwitchP = false;                     //无人机挡位是否切过
    public List<RemoteControllerFlightMode> remoteControllerFlightModes = new ArrayList<>();  //保存遥控器挡位数组

    public boolean isSDCardInserted = false;
    public int SDRemainingCapacity = MISSION_C.INT_ZERO;                     //SD卡剩余容量
    public int SDTotalSpaceInMB = MISSION_C.INT_ZERO;                       //SD卡总容量

    public List<String> errors = new ArrayList<>();                                           //扫图任务大疆给出error

    public int stayTime = MISSION_C.WAYPOINT_STAY_DEFAULT;
    public List<Integer> stayTime2;                        //陆点悬停时间
    public long dateFormat_start;                         //获取起飞前收到变焦时间
    public int dozoom_folds;                              //获取起飞前变焦倍数
    public int zoom_Default = 1;                              //设置默认变焦倍数
    public boolean isVideo;                              //设置默认是否全程录像
    public int batteryPercentageNeededToGoHome = 0;

    //焦距调整
    public boolean isSupportZoom = false;                               //是否支持变焦
    public boolean isDigitalZoom = true;                                //是否为数码变焦
    public float digitalZoomFactor = 1f;                                //当前数码焦距 zoomFactor,supported by Osmo with X3 camera, Phantom 4 camera, Z3 camera, Mavic Pro camera and XT 2 camera
    public int ipticalFocalLenght = MISSION_C.INT_ZERO;                                  //当前光学焦距
    public int currentZoomLevel = MISSION_C.INT_ONE;                                     //当前焦距Level
    public int opticalMinFocalLength = MISSION_C.INT_ZERO;                               //光学变焦最小焦距
    public int opticalMaxFocalLength = MISSION_C.INT_ZERO;                               //光学变焦最大焦距
    public int focalLengthstep = MISSION_C.INT_ZERO;                                     //光学变焦步距
    public int opticalNowFocalLength = MISSION_C.INT_ONE;                                //当前云台焦距
    public float nowCamerFolds = MISSION_C.INT_ONE;                                      //当前云台倍数
    public float MaxcamerFolds = MISSION_C.INT_ONE;                                      //当前云台最大放大倍数

    public boolean isUavOnline = false;                     //用于临时存储不作为状态变量
    public boolean isOnlineRemoter = false;                 //遥控器连接状态（即：工控机是否已连接到遥控器）
    public boolean isMqttConnected = false;                 //MQTT连接状态
    public long socketTimeStamps = DateTimeUtil.getDateByIncHour(new Date(), -1).getTime();    //上次SOCKET有效连接时间戳
    public boolean isSocketConnected = false;               //SOCKET连接状态
    public boolean isVideoPlaying = false;
    public boolean isDJUserLogined = false;                 //大疆账号是否已经登录

    public static boolean isHiveOpen = false;                   //机库是否为打开状态
    public boolean isGarageOpening = false;
    public boolean isGarageClosing = false;

    public DISPLAY_MODE display_mode = DISPLAY_MODE.VISUAL_ONLY;
    public VIDEO_STREAM_SOURCE video_stream_source = VIDEO_STREAM_SOURCE.UNKNOWN;
    public boolean display_mode_config = false;

    //机库环境变量
    private static long windyDelayMs = (CONFIG_C.WINDY_DELAY_MINITE+1)*60*1000;
    private static long windyMsFrom = (new Date()).getTime() - windyDelayMs;

    public boolean isFoggy = false;
    public String ipCurrent = "0.0.0.0";

    public String sn = "None";
    public MISSION_FINISH_ACTION finishAction = MISSION_FINISH_ACTION.RTHS;
    public String missionName = "noMission";                //当前任务名称（暂时未使用）
    public boolean isInMission = false;                     //正在执行任务
    public boolean isJustLaunchMission = false;             //true:远控台起飞任务  false:陆点任务
    public boolean joystickAnable = false;                  //是否可以进行JoyStick操作
    public boolean isEmergentStop = false;                  //是否急停
    public boolean isEmergentStopInRTH = false;             //是否在返航中急停

    public boolean isOSDKUploadConfirm = false;
    public boolean isOSDKLandConfirm = false;
    public boolean isOSDKCheckLaunch = false;

    public boolean isPhotopath = false;                    //是否是扫图任务
    public MISSION_CHECK_CODE error_info_realtime = MISSION_CHECK_CODE.OK;
    public MISSION_CHECK_CODE error_checkbeforelaunch = MISSION_CHECK_CODE.OK;

    public int isGimbalFollow = -1;                         //云台跟随




    //点测光模式
    public float thermalTem = 0.0f;                            //大气温度

    public boolean isaircraftonline = false;

    public CAMERA_TYPE camera_type = CAMERA_TYPE.UNKNOWN;
    public String cameraType = camera_type.getName();
    public List<Double> laserTargetLocation = new ArrayList<>();        //打点 参数  0 lon 1 lat  2alt
    public float juli = -1.0f;

    public float gimbalpitch2;          //用作恢复
    public float gimbalyaw2;            //用作恢复



    //火警 socket 参数
    public SocketBean socketBean = new SocketBean();
    public String imageData;
    public  String timestamp;   //当前时间
    public int imageWidth = 0;         //照片宽度
    public int imageHeight = 0;        //照片高度
    public List<ImageInfo> imageInfos = new ArrayList<>();
    public List<Double> laserTargetLocation2 = new ArrayList<>();        //打点 参数  0 lon 1 lat  2alt
    public double Longitude2 = 0.0;
    public double Latitude2 = 0.0;
    public float templimit =999;       //温度限制

    public int imgcount = 0;   //当前下载图片张数
    public boolean isupload = false;        ///标记是否正在上传

    public MissionControlState currentState = MissionControlState.IDLE;
    public boolean iswaiting = false;


    //M300 飞行时间预估
    public double flytime = 0;       //剩余飞行时间


    //35米内返航使用joy控制 临时变量
    public boolean isjoygohome = false;
    public int isAisleUse = 0;              //是否通道飞行
    public int isRtkReferenceStationSource = -1;        // 0 未设置  1 设置失败  2 设置成功
    public UAVInfoSN.Data uavdata = new UAVInfoSN().getData();

    //图像识别
    public int ischeck = -1;                //-1 开始识别未返回结果   0 不需要调整  1 调整
    public int pointx = 0;                       //
    public int pointy = 0;                       //

    public float AngleArrival = -999f;          //云台与机身夹角（正负180）


    public boolean isPhoto = false;             //是否开启路点间拍照（扫图）

    public long uwblastTime = new Date().getTime();                    //上次获取uwb数据时间
    public DJIFlightControlData joydata= new DJIFlightControlData();

    public int Voicevolume;    // 缓存设置声音大小

    public String  concentration = "";           //甲烷浓度
    public String  lighting = "";                 //光强度
    public long dataTime = System.currentTimeMillis();                    //有效数据最后时间


    public double[] landSpot;           //0 lon 1 lat 2 alt
    public boolean isnoticeRe;

    public  int windowwidth = 1920;
    public  int windowheight = 1080;
    public  int videowidth = 0;
    public  int videoheight = 0;

    public boolean isdowning = false;//是否正在下载
    public boolean isuping = false;  //是否在上传
    public boolean isCanFetch = true;  //是否可以刷新相机内sd卡
    public boolean isCanFtpUp = true;
    public boolean isCanup = false;     //记录是否由后台促发上传
    public boolean isUpFinish = true;

    public boolean isSetUpLoadImg = false;      //区域温度是否开启
    public boolean isOSDKControl = false;       //是否osdk 控制  用于发送终止自降true   收到反馈状态改为false

    public MissionMediaInfo missionMediaInfo;
    public boolean isFtp_Control = false;       //是否在执行
    //-------------------------  构造函数  -----------------------------
    private UAVInfo() {}

    public static UAVInfo getInstance() {
        if (sInfo == null) {
            synchronized (UAVInfo.class) {
                if (sInfo == null) {sInfo = new UAVInfo();}
            }
        }
        return sInfo;
    }

    public static void init(){
        sInfo = new UAVInfo();
    }

    *//**发送给调用方的CODE*//*
    public int getStatusSend(){
        if(hiveStatus == HIVE_STATUS.INIT){
            return HIVE_STATUS_SEND.INIT.getCode();
        }else if(hiveStatus == HIVE_STATUS.IDLE){
            if(isFoggy) return HIVE_STATUS_SEND.FOGGY.getCode();
            if(hiveStateInfo != null && hiveStateInfo.isRaining()) return HIVE_STATUS_SEND.RAINING.getCode();
            int state_code = hiveState.getCode();
            long windyMsCurrent = (new Date()).getTime();
            if(hiveStateInfo != null && hiveStateInfo.getWindSpeed() > ((CONFIG_C.hive == HIVE.TONGLIAO)?4:CONFIG_C.WINDY_MAX)){
                windyMsFrom = windyMsCurrent;
                return HIVE_STATUS_SEND.WINDY.getCode();
            } else if(windyMsCurrent-windyMsFrom < CONFIG_C.WINDY_DELAY_MINITE*60*1000){
                return HIVE_STATUS_SEND.WINDY.getCode();
            } else if(state_code==HIVE_STATE.READY.getCode()){
                return HIVE_STATUS_SEND.READY.getCode();
            } else if(state_code==HIVE_STATE.READY_RAINING.getCode()){
                return HIVE_STATUS_SEND.RAINING.getCode();
            } else if(state_code==HIVE_STATE.READY_FOGGY.getCode()){
                return HIVE_STATUS_SEND.FOGGY.getCode();
            } else if(state_code==HIVE_STATE.READY_WINDY.getCode()){
                return HIVE_STATUS_SEND.WINDY.getCode();
            } else if(state_code==HIVE_STATE.READY_NIGHT.getCode()){
                return HIVE_STATUS_SEND.NIGHT.getCode();
            } else if(state_code== HIVE_STATE.UNDER_REPAIR.getCode()){
                return HIVE_STATUS_SEND.OFFLINE.getCode();
            } else if(state_code== HIVE_STATE.FLIGHTMODESWITCH_NOP.getCode()){
                return HIVE_STATUS_SEND.OFFLINE.getCode();
            }
            return HIVE_STATUS_SEND.READY.getCode();
        } else if(hiveStatus == HIVE_STATUS.FLYING){
            return HIVE_STATUS_SEND.FLYING.getCode();
        } else if(hiveStatus == HIVE_STATUS.RTH){
            return HIVE_STATUS_SEND.RTH.getCode();
        } else if(hiveStatus == HIVE_STATUS.CHARGING){
            if(hiveState==HIVE_STATE.CHARGING_UNMUST){
                long windyMsCurrent = (new Date()).getTime();
                if(hiveStateInfo != null && hiveStateInfo.getWindSpeed() > ((CONFIG_C.hive == HIVE.TONGLIAO)?4:CONFIG_C.WINDY_MAX)){
                    windyMsFrom = windyMsCurrent;
                    return HIVE_STATUS_SEND.WINDY.getCode();
                } else if(windyMsCurrent-windyMsFrom < CONFIG_C.WINDY_DELAY_MINITE*60*1000){
                    return HIVE_STATUS_SEND.WINDY.getCode();
                }  else if(hiveState.getCode()==HIVE_STATE.READY_RAINING.getCode()){
                    return HIVE_STATUS_SEND.RAINING.getCode();
                } else if(hiveState.getCode()==HIVE_STATE.READY_WINDY.getCode()){
                    return HIVE_STATUS_SEND.WINDY.getCode();
                } else{
                    return HIVE_STATUS_SEND.READY.getCode();
                }
            }
            return HIVE_STATUS_SEND.CHARGING.getCode();
        }
        return HIVE_STATUS_SEND.UNKNOW.getCode();
    }

    public float getDistanceStart(){distanceReset();return distanceStart;}
    public double getDistanceMission(){return distanceMission;}
    public void setDistanceMission(double distance){this.distanceMission = distance;}
    public double getDistanceReturn(){return distanceReturn;}
    public void setDistanceReturn(double distance){this.distanceReturn = distance;}

    private void distanceReset(){
        LocateInfo currentGCJ02 = uavFlightController.getLocationGCJ02();
        LocateInfo launchGCJ02 = uavFlightController.getLocateInfoLaunchGCJ02();
        if(launchGCJ02==null||Double.isNaN(launchGCJ02.getLatitude())||Double.isNaN(launchGCJ02.getLongitude())){
            distanceStart = MISSION_C.INT_ZERO;
        }else if(currentGCJ02==null||Double.isNaN(currentGCJ02.getLatitude())||Double.isNaN(currentGCJ02.getLongitude())){
            distanceStart = MISSION_C.INT_ZERO;
        }else if(hiveStatus== HIVE_STATUS.CHARGING ||(hiveStatus ==HIVE_STATUS.IDLE && CONFIG_C.isWithGarage)){
            distanceStart = MISSION_C.INT_ZERO;
        }else if(CONFIG_C.isWithGarage&&hiveStatus== HIVE_STATUS.FLYING&&flyingState.getCode()< FLYING_STATE.FLYING_TAKEOFF.getCode()){
            distanceStart = MISSION_C.INT_ZERO;
        }else{
            distanceStart = MyMapUtil.calculateLineDistance(currentGCJ02, uavFlightController.getLocateInfoLaunchGCJ02());
        }
    }

    public boolean isOnline() {
        UAVApplication app = UAVApplication.getInstance();
        AircraftUtil aircraftUtil = app.getAircraftUtil();
        if (aircraftUtil == null){return false;}
        boolean lastOnline = this.isUavOnline;
        boolean currentOnline = aircraftUtil.isAircraftOnline();
        if(!lastOnline&&currentOnline){reConnectedProcess();}
        this.isUavOnline = currentOnline;
        if(currentOnline)isFullBattery=false;
        return currentOnline;
    }

    *//**无人机重新连接上的处理*//*
    Timer timer_resetVideoSource = new Timer();
    public void reConnectedProcess(){
        //XLog.d("【reConnectedProcess】");
        if (hiveStatus == HIVE_STATUS.CHARGING){return;}
        if (timer_resetVideoSource != null){timer_resetVideoSource.cancel();timer_resetVideoSource.purge();timer_resetVideoSource = null;}
        timer_resetVideoSource = new Timer();
        //timer_resetVideoSource.schedule(new TaskResetVideoSource(), 0, 1000);
    }

    public int displayMode(){
        if (camera_type == CAMERA_TYPE.H20T || camera_type == CAMERA_TYPE.H20){
            return video_stream_source.getCode();
        }else {
            return display_mode.getCode();
        }
    }


    public SocketBean getSocketInfo(){
         SocketBean socketBean = new SocketBean();
*//*        List<ImageInfo> imageInfos = new ArrayList<>();
        for (int i = 0; i < 3; i++){
            ImageInfo imageInfo = new ImageInfo();
            imageInfo.setIndex(i);
            imageInfo.setImageData(imageData);
            imageInfo.setImageHeight(imageHeight);
            imageInfo.setImageWidth(imageWidth);
            imageInfo.setImageSource(i);
            imageInfos.add(imageInfo);

        }*//*
        socketBean.setImageList(imageInfos);
        socketBean.setCameraType(cameraType);

        if (laserTargetLocation != null && laserTargetLocation.size() >1){
            socketBean.setLaserTargetLocation(laserTargetLocation);
        }
        if (uavFlightController != null){
            socketBean.setLongitude(Double.toString(Longitude84));
            socketBean.setLatitude(Double.toString(Latitude84));
        }
        socketBean.setTheIT2AT(theIT2AT2);
        socketBean.setTheIDATRMax(theIDATRMax2);
        socketBean.setTheIDATRMin(theIDATRMin2);
        socketBean.setMissionBatch(missionBatch);
        socketBean.setSN(sn);
        socketBean.setUAVID(uavNameServer);
        socketBean.setType(1);
        socketBean.setTimestamp(timestamp);
        socketBean.setTheIDATLocation(theIDATLocation2);
        return socketBean;
    }

    //M300 预估飞行时间
    public double getflytime(){
        flytime = 0;
        if (!UAVApplication.getInstance().getAircraftUtil().isM300Product()){return MISSION_C.MISSION_COSTTIME_MAX;}
        if(uavBattery==null)return flytime;
        double f1 =flytime;
        for (int i = 0 ; i < uavBattery.batteryNumber; i ++){
            UAVBatterySingle single = uavBattery.batterys[i];
            BatteryState batteryState = single.batteryState;
            f1 = MISSION_C.dumpenergy*(MISSION_C.constant1 +
                    MISSION_C.constant2*batteryState.getChargeRemainingInPercent()+MISSION_C.constant3*batteryState.getNumberOfDischarges());
            flytime = f1 + flytime;
        }
        return flytime;
    }

    private int count ;
    public boolean isMediaupLoadComplete(){
        boolean isMediaupLoadComplete = false;
        if (isuping || isdowning){count = 0;}
            count ++;
        if (count > 10){isMediaupLoadComplete = true;}
        return isMediaupLoadComplete;
    }*/

}
