package dji.sampleV5.aircraft.net.bean;


public class MissionCheckResult {

    MissionCheckResult(){}

    public MissionCheckResult(MISSION_CHECK_CODE check_code, double distance, double seconds, LocateInfo lastPot){
        this.check_code = check_code;
        this.distance = distance;
        this.seconds = seconds;
        this.lastPot = lastPot;
    }

    private MISSION_CHECK_CODE check_code;
    private double distance;
    private double seconds;
    private LocateInfo lastPot;

    public double getDistance() {return distance;}
    public void setDistance(double distance) {this.distance = distance;}
    public double getSeconds() {return seconds;}
    public void setSeconds(double seconds) {this.seconds = seconds;}
    public MISSION_CHECK_CODE getCheck_code() {return check_code;}
    public void setCheck_code(MISSION_CHECK_CODE check_code) {this.check_code = check_code;}
    public LocateInfo getLastPot() {return lastPot;}
    public void setLastPot(LocateInfo lastPot) {this.lastPot = lastPot;}

}
