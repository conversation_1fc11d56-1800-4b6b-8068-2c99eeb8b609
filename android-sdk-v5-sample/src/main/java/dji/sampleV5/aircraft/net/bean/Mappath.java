package dji.sampleV5.aircraft.net.bean;

import java.util.ArrayList;
import java.util.List;

public class Mappath {

    private double longitude;
    private double latitude;

    public Mappath(){}

    public Mappath(double latitude_in, double longitude_in){
        this.latitude = latitude_in;
        this.longitude = longitude_in;
    }

    public double getLongitude() {
        return longitude;
    }
    public void setLongitude(double longitude) {
        this.longitude = longitude;
    }
    public double getLatitude() {
        return latitude;
    }
    public void setLatitude(double latitude) {
        this.latitude = latitude;
    }

    public static List<Mappath> createMappaths(List<LocateInfo> latLngs){
        List<Mappath> result = new ArrayList<Mappath>();
        int size = latLngs.size();
        for(int i = MISSION_C.INT_ZERO; i<size; i++){
            LocateInfo latLng = latLngs.get(i);
            result.add(new Mappath(latLng.getLatitude(), latLng.getLongitude()));
        }
        return result;
    }

}
