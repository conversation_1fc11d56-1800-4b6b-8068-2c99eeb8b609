package dji.sampleV5.aircraft;

import android.Manifest;
import android.app.AlertDialog;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.graphics.Color;
import android.graphics.drawable.Drawable;
import android.net.Uri;
import android.os.Bundle;
import android.provider.Settings;
import android.text.Html;
import android.text.Spanned;
import android.util.Log;
import android.view.MenuItem;
import android.view.View;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.app.AppCompatActivity;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;
import androidx.core.content.PermissionChecker;
import androidx.core.view.GravityCompat;
import androidx.databinding.DataBindingUtil;

import com.amap.api.maps.MapsInitializer;
import com.amap.api.maps.offlinemap.OfflineMapActivity;
import com.google.android.material.navigation.NavigationView;

import org.greenrobot.eventbus.Subscribe;

import java.util.ArrayList;
import java.util.concurrent.atomic.AtomicBoolean;

import dji.sampleV5.aircraft.databinding.HomeActivityBinding;
import dji.sampleV5.aircraft.databinding.ZkyhomeActivityBinding;
import dji.sampleV5.aircraft.event.Event;
import dji.sampleV5.aircraft.event.Events;
import dji.sampleV5.aircraft.lbs.LocationService;
import dji.sampleV5.aircraft.lbs.MapServiceFactory;
import dji.sampleV5.aircraft.mqtt.MQttManager;
import dji.sampleV5.aircraft.util.ToastUtil;
import dji.sdk.keyvalue.key.FlightControllerKey;
import dji.sdk.keyvalue.key.KeyTools;
import dji.sdk.keyvalue.key.ProductKey;
import dji.sdk.keyvalue.value.product.ProductType;
import dji.v5.common.callback.CommonCallbacks;
import dji.v5.common.error.IDJIError;
import dji.v5.common.register.DJISDKInitEvent;
import dji.v5.manager.KeyManager;
import dji.v5.manager.SDKManager;
import dji.v5.manager.interfaces.SDKManagerCallback;
import me.jessyan.autosize.internal.CancelAdapt;

public class ZKYHomeActivity extends AppCompatActivity implements View.OnClickListener, CancelAdapt {
    private ZkyhomeActivityBinding binding;
    private AtomicBoolean isInited = new AtomicBoolean(false);
    private static final int REQUEST_CODE = 0x0010;
    //private FlyStatusController flyStatusController;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        getWindow().setStatusBarColor(getResources().getColor(android.R.color.transparent));
        super.onCreate(savedInstanceState);
        binding = DataBindingUtil.setContentView(this, R.layout.zkyhome_activity);
        //ImmerseUtil.fullScreen(this);
        Event.register(this);
        //DJIHelper.getInstance().register(getApplicationContext());
        //flyStatusController = new FlyStatusController();


        binding.fly.setOnClickListener(this);


        checkPermissions();


    }

    private void checkPermissions() {
        ArrayList<String> deniedPermissionList = new ArrayList<>();
        if (PermissionChecker.checkSelfPermission(this, Manifest.permission.ACCESS_COARSE_LOCATION) != PermissionChecker.PERMISSION_GRANTED) {
            deniedPermissionList.add(Manifest.permission.ACCESS_COARSE_LOCATION);
        }

        if (PermissionChecker.checkSelfPermission(this, Manifest.permission.ACCESS_FINE_LOCATION) != PermissionChecker.PERMISSION_GRANTED) {
            deniedPermissionList.add(Manifest.permission.ACCESS_FINE_LOCATION);
        }

        if (ContextCompat.checkSelfPermission(this, Manifest.permission.READ_EXTERNAL_STORAGE) != PermissionChecker.PERMISSION_GRANTED) {
            deniedPermissionList.add(Manifest.permission.READ_EXTERNAL_STORAGE);
        }

        if (ContextCompat.checkSelfPermission(this, Manifest.permission.WRITE_EXTERNAL_STORAGE) != PermissionChecker.PERMISSION_GRANTED) {
            deniedPermissionList.add(Manifest.permission.WRITE_EXTERNAL_STORAGE);
        }

        if (ContextCompat.checkSelfPermission(this, Manifest.permission.READ_PHONE_STATE) != PermissionChecker.PERMISSION_GRANTED) {
            deniedPermissionList.add(Manifest.permission.READ_PHONE_STATE);
        }

        if (ContextCompat.checkSelfPermission(this, Manifest.permission.RECORD_AUDIO) != PermissionChecker.PERMISSION_GRANTED) {
            deniedPermissionList.add(Manifest.permission.RECORD_AUDIO);
        }

        if (deniedPermissionList.size() > 0) {
            String[] permissionArray = new String[deniedPermissionList.size()];
            deniedPermissionList.toArray(permissionArray);
            ActivityCompat.requestPermissions(this, permissionArray, REQUEST_CODE);
        } else {
            MapsInitializer.updatePrivacyShow(ZKYHomeActivity.this, true, true);
            MapsInitializer.updatePrivacyAgree(ZKYHomeActivity.this, true);
            register();
            LocationService locationService = MapServiceFactory.getLocationServiceInstance();
            locationService.connectService();
            locationService.startOnceLocation();
        }
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        boolean isAllGrant = false;
        for (int grantResult : grantResults) {
            isAllGrant = (grantResult == PackageManager.PERMISSION_GRANTED);
            if (!isAllGrant) {
                break;
            }
        }

        if (isAllGrant) {
            MapsInitializer.updatePrivacyShow(ZKYHomeActivity.this, true, true);
            MapsInitializer.updatePrivacyAgree(ZKYHomeActivity.this, true);
            register();
            LocationService locationService = MapServiceFactory.getLocationServiceInstance();
            locationService.connectService();
            locationService.startOnceLocation();
        } else {
            showPermissionDialog();
        }
    }

    private void showPermissionDialog() {
        AlertDialog.Builder builder = new AlertDialog.Builder(this, R.style.AppTheme_Dialog);
        builder.setTitle("权限申请");
        builder.setMessage("如果没有允许权限应用可能无法正常工作，请到应用设置的权限管理中修改权限。");
        builder.setNegativeButton("取消", (dialog, which) -> {
            dialog.dismiss();
            ContextUtil.removeAllActivity();
        });
        builder.setPositiveButton("确定", (dialog, which) -> {
            dialog.dismiss();
            ContextUtil.removeAllActivity();
            start2SettingPage();
        });
        builder.setCancelable(false);
        builder.show();
    }

    private void start2SettingPage() {
        Uri packageURI = Uri.parse("package:" + getPackageName());
        Intent intent = new Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS, packageURI);
        startActivity(intent);
    }

    private void register() {
        SDKManager.getInstance().init(getApplicationContext(), new SDKManagerCallback() {
            @Override
            public void onRegisterSuccess() {
                //ToastUtils.INSTANCE.showToast("Register Success");
                if (!SDKManager.getInstance().isRegistered()) {
                    return;
                }
                if (isInited.getAndSet(true)) {
                    return;
                }

                KeyManager.getInstance().listen(KeyTools.createKey(FlightControllerKey.KeyConnection), this, new CommonCallbacks.KeyListener<Boolean>() {

                    @Override
                    public void onValueChange(@Nullable Boolean oldValue, @Nullable Boolean newValue) {
                        if (newValue == null) {
                            return;
                        }
                        DJIAircraftApplication.getInstance().setAircraftConnected(newValue);
                        if (newValue) {
                            //ToastUtils.INSTANCE.showToast("无人机连接成功");
                            KeyManager.getInstance().getValue(KeyTools.createKey(ProductKey.KeyProductType), new CommonCallbacks.CompletionCallbackWithParam<ProductType>() {
                                @Override
                                public void onSuccess(ProductType productType) {
                                    if (productType == ProductType.M30_SERIES) {
                                        Drawable img = getResources().getDrawable(R.drawable.green_icon);
                                        img.setBounds(0, 0, img.getMinimumWidth(), img.getMinimumHeight());
                                        binding.tvName.setCompoundDrawables(img, null, null, null);
                                        binding.imgAircraft.setImageResource(R.drawable.m30);

                                        binding.tvName.setText("M30");
                                        binding.tvStatus.setText("已连接");

                                    }
                                }

                                @Override
                                public void onFailure(@NonNull IDJIError error) {

                                }
                            });

                            KeyManager.getInstance().getValue(KeyTools.createKey(FlightControllerKey.KeySerialNumber), new CommonCallbacks.CompletionCallbackWithParam<String>() {
                                @Override
                                public void onSuccess(String sn) {
                                    binding.tvSn.setText(sn);
                                    Log.e("KeySerialNumber", "onSuccess KeySerialNumber:" + sn);
                                    //MQttManager.getInstance().connectMQtt(sn);
                                }

                                @Override
                                public void onFailure(@NonNull IDJIError error) {

                                }
                            });

                        } else {
                            ToastUtil.show("无人机断开连接");
                        }
                    }
                });
                //KeyManager.getInstance().getValue()
            }

            @Override
            public void onRegisterFailure(IDJIError error) {
                ToastUtil.show("Register failure " + error.description());
            }

            @Override
            public void onProductDisconnect(int productId) {

            }

            @Override
            public void onProductConnect(int productId) {

            }

            @Override
            public void onProductChanged(int productId) {

            }

            @Override
            public void onInitProcess(DJISDKInitEvent event, int totalProcess) {
                if (event == DJISDKInitEvent.INITIALIZE_COMPLETE) {
                    //ToastUtils.INSTANCE.showToast("registerApp");
                    SDKManager.getInstance().registerApp();
                }
            }

            @Override
            public void onDatabaseDownloadProgress(long current, long total) {

            }
        });
    }

    @Override
    protected void onResume() {
        super.onResume();
        /*LoginCache loginCache = SpUtil.getLoginCache();
        if(loginCache == null){
            startActivity(new Intent(this, LoginActivity.class));
        }else {
            View view = binding.navView.getHeaderView(0);
            AlphabetCircleImageView ivAvatar = view.findViewById(R.id.iv_nav_avatar);
            ivAvatar.setAlphabet(loginCache.getId());
            TextView tvUserName = view.findViewById(R.id.tv_nav_name);
            tvUserName.setText(loginCache.getId());
            TextView tvOrgName = view.findViewById(R.id.tv_org_name);
            tvOrgName.setVisibility(View.INVISIBLE);
        }*/
    }

    @Override
    public void onBackPressed() {
        super.onBackPressed();
        //MQttManager.getInstance().connectMQtt("1ZNBJ8D00C001F");
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        Event.unregister(this);
        //flyStatusController.onDestroy();
        //MQttManager.getInstance().notifyAppClosed();
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.combo_layout:
               /* LoginCache loginCache = SpUtil.getLoginCache();
                if(loginCache == null){
                    startActivity(new Intent(this, LoginActivity.class));
                }*/
                break;
            case R.id.fly:
                startActivity(new Intent(this, DefaultLayoutActivity.class));
                //startActivity(new Intent(this, TestActivity.class));
                break;
            case R.id.web:
                //startActivity(new Intent(this, WebViewActivity.class));
                break;
            case R.id.plan:
                /*ImmerseUtil.showDialog(new AddPlanDialog(this, new AddPlanDialog.MyClickListener() {
                    @Override
                    public void onCreatePolygon() {
                        Intent intent = new Intent(HomeActivity.this, WebViewActivity.class);
                        intent.putExtra("FromLogin", false);
                        intent.putExtra("type", 1);
                        startActivity(intent);
                    }

                    @Override
                    public void onCreatePoint() {
                        Intent intent = new Intent(HomeActivity.this, WebViewActivity.class);
                        intent.putExtra("FromLogin", false);
                        intent.putExtra("type", 2);
                        startActivity(intent);
                    }

                    @Override
                    public void onCreateOblique() {
                    }

                    @Override
                    public void onCreateCircle() {
                    }
                }));*/

                break;
            case R.id.media:
               /* if (DJIHelper.getInstance().getProduct() == null) {
                    ToastUtil.show("请先连接上飞机");
                    return;
                }
                startActivity(new Intent(this, PictureActivity.class));*/
                break;
        }
    }

    private Spanned proName(String raw) {
        int index = raw.lastIndexOf(" ");
        if (index != -1) {
            return Html.fromHtml(raw.substring(0, index) + " <font color=\"red\">" + raw.substring(index, raw.length()) + "</font>");
        }
        return Html.fromHtml(raw);
    }

    @Subscribe
    public void onGetSNEvent(Events.GetSNEvent event) {
        String sn = event.getSn();
        runOnUiThread(() -> {
            if (sn != null) {
                binding.tvSn.setText(sn);
                //MQttManager.getInstance().connectMQtt(sn);
                //getUavNameInfo(sn);
            } else {
                binding.tvSn.setText("序列号");
            }
        });
    }

}
