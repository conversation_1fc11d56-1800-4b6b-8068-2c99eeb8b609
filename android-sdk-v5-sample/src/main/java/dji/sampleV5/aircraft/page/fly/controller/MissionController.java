package dji.sampleV5.aircraft.page.fly.controller;


import static dji.sampleV5.aircraft.common.drone.mission.BaseMissionHelper.EXECUTION_PAUSED;
import static dji.sampleV5.aircraft.common.drone.mission.BaseMissionHelper.MISSION_FINISH;
import static dji.sampleV5.aircraft.common.drone.mission.BaseMissionHelper.MISSION_PAUSE;
import static dji.sampleV5.aircraft.common.drone.mission.BaseMissionHelper.MISSION_RESUME;
import static dji.sampleV5.aircraft.common.drone.mission.BaseMissionHelper.MISSION_START;
import static dji.sampleV5.aircraft.common.drone.mission.BaseMissionHelper.MISSION_STOP;
import static dji.sampleV5.aircraft.common.drone.mission.BaseMissionHelper.MISSION_UPLOAD;
import static dji.sampleV5.aircraft.common.drone.mission.BaseMissionHelper.READY_TO_EXECUTE;
import static dji.sampleV5.aircraft.common.drone.mission.BaseMissionHelper.UNKNOWN;
import static dji.sampleV5.aircraft.common.drone.mission.BaseMissionHelper.VALUE_FAILED;
import static dji.sampleV5.aircraft.common.drone.mission.BaseMissionHelper.VALUE_FINISHED;
import static dji.sampleV5.aircraft.common.drone.mission.BaseMissionHelper.VALUE_SUCCEED;

import android.annotation.SuppressLint;
import android.app.AlertDialog;
import android.app.Dialog;
import android.text.TextUtils;
import android.util.Log;
import android.view.View;
import android.widget.Button;
import android.widget.EditText;
import android.widget.ImageButton;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.graphics.drawable.DrawableCompat;

import com.google.gson.Gson;

import org.json.JSONArray;
import org.json.JSONException;

import dji.sampleV5.aircraft.ContextUtil;
import dji.sampleV5.aircraft.DJIAircraftApplication;
import dji.sampleV5.aircraft.DefaultLayoutActivity;
import dji.sampleV5.aircraft.R;


import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

import dji.sampleV5.aircraft.common.drone.mission.BaseMissionHelper;
import dji.sampleV5.aircraft.common.drone.mission.WaypointMissionHelper;
import dji.sampleV5.aircraft.common.json.JsonUtil;
import dji.sampleV5.aircraft.data.mission.LaserUploadBean;
import dji.sampleV5.aircraft.data.mission.MissionDetail;
import dji.sampleV5.aircraft.data.mission.MissionStatus;
import dji.sampleV5.aircraft.data.preference.SpUtil;
import dji.sampleV5.aircraft.data.task.MissionInfo;
import dji.sampleV5.aircraft.databinding.ActivityDefaultLayoutBinding;
import dji.sampleV5.aircraft.lbs.MapController;
import dji.sampleV5.aircraft.lbs.MissionMapPainter;
import dji.sampleV5.aircraft.lbs.bean.AppLatLng;
import dji.sampleV5.aircraft.mvvm.event.LiveDataEvent;
import dji.sampleV5.aircraft.mvvm.ext.CommExtKt;
import dji.sampleV5.aircraft.mvvm.net.repository.UserRepository;
import dji.sampleV5.aircraft.mvvm.net.response.MissionInfoBean;
import dji.sampleV5.aircraft.mvvm.util.XLogUtil;
import dji.sampleV5.aircraft.net.bean.ActionTypeEnum;
import dji.sampleV5.aircraft.net.bean.Flightpath;
import dji.sampleV5.aircraft.net.bean.FlightpathAction;
import dji.sampleV5.aircraft.net.bean.LocateInfo;
import dji.sampleV5.aircraft.net.bean.Mission;
import dji.sampleV5.aircraft.net.bean.MissionJson;
import dji.sampleV5.aircraft.page.plan.BaseMission;
import dji.sampleV5.aircraft.page.plan.WaypointMission;
import dji.sampleV5.aircraft.util.BreakpointManager;
import dji.sampleV5.aircraft.util.CompressUtil;
import dji.sampleV5.aircraft.util.CoroutineUtils;
import dji.sampleV5.aircraft.util.GCJ02_WGS84;
import dji.sampleV5.aircraft.util.ToastUtil;
import dji.sampleV5.aircraft.util.coordinate.GeoSysConversion;
import dji.sampleV5.aircraft.util.phone.DensityUtil;
import dji.sampleV5.aircraft.view.AutoTakeOffDialog;
import dji.sdk.keyvalue.key.BatteryKey;
import dji.sdk.keyvalue.key.CameraKey;
import dji.sdk.keyvalue.key.FlightControllerKey;
import dji.sdk.keyvalue.key.GimbalKey;
import dji.sdk.keyvalue.key.KeyTools;
import dji.sdk.keyvalue.value.camera.CameraRotateAngle;
import dji.sdk.keyvalue.value.camera.GeneratedMediaFileInfo;
import dji.sdk.keyvalue.value.camera.LaserMeasureInformation;
import dji.sdk.keyvalue.value.camera.LaserMeasureState;
import dji.sdk.keyvalue.value.common.EmptyMsg;
import dji.sdk.keyvalue.value.flightcontroller.GoHomeState;
import dji.sdk.keyvalue.value.gimbal.GimbalAngleRotation;
import dji.sdk.keyvalue.value.gimbal.GimbalMode;
import dji.sdk.keyvalue.value.product.ProductType;
import dji.v5.common.callback.CommonCallbacks;
import dji.v5.common.error.IDJIError;
import dji.v5.manager.KeyManager;
import dji.v5.manager.aircraft.waypoint3.model.WaypointMissionExecuteState;
import kotlin.coroutines.Continuation;
import okhttp3.Call;
import okhttp3.Callback;
import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;


public class MissionController implements BaseMissionHelper.IOperationResultListener, View.OnClickListener {
    private final String TAG = this.getClass().getName();
    private static final int ENABLE_NONE = 0x0000;
    private static final int ENABLE_START_BUTTON = 0x0001;
    private static final int ENABLE_PAUSE_BUTTON = 0x0002;
    private static final int ENABLE_STOP_BUTTON = 0x0004;

    private int lastShowToastID;
    private long lastUploadTime;
    private long lastShowToastTime;

    private boolean isFlying;
    private boolean lastFly;
    private boolean isClickStartMission;

    private DefaultLayoutActivity activity;
    private ActivityDefaultLayoutBinding binding;

    private MissionDetail missionDetail = new MissionDetail();
    private MissionStatus missionStatus = new MissionStatus();
    private WaypointMission mChosenMission;
    private MissionMapPainter mMissionMapPainter;
    private BaseMissionHelper missionHelper;

    private boolean autoFly;
    private boolean isH20;
    private boolean is2D;//是否是光伏2d扫图
    private ArrayList<LaserUploadBean> laserUploadBeanArrayList = new ArrayList<>();
    private MissionJson missionJson;
    private int currentWaypointIndex = -1;
    private float targetDistance = -1;
    private CommonCallbacks.KeyListener isFlyingListener;
    private CommonCallbacks.KeyListener generatedMediaFileListener;
    private CommonCallbacks.KeyListener laserListener;
    private boolean isResumeMission = false;
    private boolean isCustomMission = false;
    private boolean isMissionFinish = false;
    private RtmpController rtmpController;
    private MissionDetail resumeMissionDetail;
    private OfflineController offlineController;
    private boolean isOffLineMission = false;
    private ProductType productType;


    public boolean hasExecutingTask() {
        return mChosenMission != null;
    }

    /**
     * 判断当前任务是否为断点续飞任务
     * @return true-断点续飞任务, false-正常任务
     */
    public boolean isBreakpointResume() {
        // 在线任务断点续飞
        boolean onlineBreakpoint = isResumeMission || isCustomMission;

        // 离线任务断点续飞
        boolean offlineBreakpoint = false;
        if (isOffLineMission && offlineController != null) {
            offlineBreakpoint = offlineController.isBreakpointResume();
        }

        return onlineBreakpoint || offlineBreakpoint;
    }

    /**
     * 获取断点续飞的原始批次号
     * @return 原始批次号，如果不是断点续飞则返回null
     */
    public String getOriginalBatch() {
        // 在线任务逻辑
        if ((isResumeMission || isCustomMission) && resumeMissionDetail != null) {
            WaypointMission resumeWaypointMission = resumeMissionDetail.getWaypointMission();
            if (resumeWaypointMission != null) {
                return resumeWaypointMission.getMissionBatch();
            }
        }

        // 离线任务逻辑
        if (isOffLineMission && offlineController != null) {
            return offlineController.getOriginalBatch();
        }

        return null;
    }

    public MissionController(DefaultLayoutActivity activity, RtmpController rtmpController) {
        this.activity = activity;
        this.binding = activity.getBinding();
        this.rtmpController = rtmpController;
        productType = DJIAircraftApplication.getInstance().getProductType();

        // 设置BreakpointManager的MissionController实例
        BreakpointManager.getInstance().setMissionController(this);
    }

    public void onConnect(boolean connect) {
        if (connect) {
            isFlyingListener = new CommonCallbacks.KeyListener<Boolean>() {
                @Override
                public void onValueChange(@Nullable Boolean oldValue, @Nullable Boolean now) {
                    if (now != null) {
                        isFlying = now;
                        if (!lastFly && now) {
                            //ToastUtil.show("起飞");
                            DJIAircraftApplication.getInstance().setMotorOn(true);
                            DJIAircraftApplication.getInstance().setSubState(204);
                            ContextUtil.getHandler().postDelayed(new Runnable() {
                                @Override
                                public void run() {
                                    DJIAircraftApplication.getInstance().setSubState(209);
                                }
                            }, 3000);
                        }
                        if (!isFlying && lastFly && mChosenMission != null) {
                            //测试发现御2行业进阶最后一个点currentWaypointIndex不更新.
                            DJIAircraftApplication.getInstance().setMotorOn(false);
                            DJIAircraftApplication.getInstance().setSubState(102);
                            stopLaser();
                            missionStatus.setMissionBatch("");
                            DJIAircraftApplication.getInstance().setMissionStatus(missionStatus);
                            if (offlineController != null) {
                                offlineController.onDestroy();
                            }
                            if (isMissionFinish && (currentWaypointIndex == mChosenMission.getWaypointList().size() || currentWaypointIndex == (mChosenMission.getWaypointList().size() - 1))) {
                                if (missionDetail.getEndTime() == null) { //再加一层保护，如果飞机落地我回去判断有没有存结束时间，如果没有我就把落地时间作为结束时间
                                    missionDetail.setEndTime(System.currentTimeMillis());
                                }
                                missionDetail.setComplete(true);
                                SpUtil.setLaserList(laserUploadBeanArrayList);
                                ToastUtil.show("任务已完成，可以到飞行历史中上传数据");
                                /*new AlertDialog.Builder(activity)
                                        .setMessage("任务已经完成，是否立即下载上传图片？")
                                        .setNegativeButton(R.string.dialog_cancel, (dialog, which) -> {
                                            ToastUtil.show("稍后可以到首页最近任务或者飞行历史中处理");
                                        })
                                        .setPositiveButton(R.string.dialog_ok, (dialog, which) -> {
                                            rtmpController.stopRtmp();
                                            MissionPhotoUploader.getInstance().onMissionComplete(mChosenMission.getMissionBatch(), binding);
                                        }).create().show();*/
                            } else {
                                if (currentWaypointIndex > 0) {//可能还没飞到第一个点就返回了，这个时候不做保存
                                    ToastUtil.show("任务还未完成，进行保存");
                                    missionDetail.setComplete(false);
                                }
                            }
                            SpUtil.setMissionData(mChosenMission.getMissionBatch(), missionDetail);
                            if (binding.ivShowNaviLine.getVisibility() == View.VISIBLE) {
                                binding.ivShowNaviLine.setVisibility(View.GONE);
                            }
                            mMissionMapPainter.hideRoute();
                        }
                        lastFly = now;
                    }
                }
            };
            KeyManager.getInstance().listen(KeyTools.createKey(FlightControllerKey.KeyIsFlying), this, isFlyingListener);

            addPhotoListener();
            //isH20 = DJIHelper.getInstance().isH20();

            KeyManager.getInstance().listen(KeyTools.createKey(FlightControllerKey.KeyGoHomeStatus), this, new CommonCallbacks.KeyListener<GoHomeState>() {
                @Override
                public void onValueChange(@Nullable GoHomeState oldValue, @Nullable GoHomeState newValue) {
                    if (newValue != null) {
                        switch (newValue) {
                            case RETURNING_TO_HOME:
                                binding.viewMissionPanel.main.setVisibility(View.GONE);
                                DJIAircraftApplication.getInstance().setSubState(302);
                                break;
                            case LANDING:
                                DJIAircraftApplication.getInstance().setSubState(306);
                                break;
                        }
                    }
                }
            });

            //实时监听无人机电池电量
            KeyManager.getInstance().listen(KeyTools.createKey(BatteryKey.KeyChargeRemainingInPercent), this, (integer, curValue) -> {
                if (curValue == null) return;
                if (missionHelper == null) return;
                int hoverValue = dji.v5.ux.core.util.SpUtil.getLowBatteryHover();
                XLogUtil.INSTANCE.d(TAG, " --当前无人机状态" + missionHelper.getCurrentState() + "----DJI状态" +  WaypointMissionExecuteState.EXECUTING.value() + "----当前电池电量：" + curValue + " ---悬停电池电量：" + hoverValue);
                //17表示任务执行中，当低电量达到阈值并且在执行任务时则暂停任务
                if ((hoverValue >= curValue) && (missionHelper.getCurrentState() == 17)) {
                    missionHelper.pauseMission();
                }
            });

            /*if (!TextUtils.isEmpty(SpUtil.getMissionBatch())) {
                showResumeMissionDialog();
            }*/
        } else {
            if (isFlyingListener != null) {
                KeyManager.getInstance().cancelListen(isFlyingListener);
                setLayoutGroupEnable(ENABLE_START_BUTTON);
            }
            if (generatedMediaFileListener != null) {
                KeyManager.getInstance().cancelListen(generatedMediaFileListener);
            }
        }
    }

    private void addPhotoListener() {
        generatedMediaFileListener = new CommonCallbacks.KeyListener<GeneratedMediaFileInfo>() {
            @Override
            public void onValueChange(@Nullable GeneratedMediaFileInfo oldValue, @Nullable GeneratedMediaFileInfo newValue) {
                if (newValue != null) {
                    LaserUploadBean laserUploadBean = new LaserUploadBean();
                    laserUploadBean.setIndex(newValue.getIndex());
                    laserUploadBean.setTargetDistance(targetDistance);
                    laserUploadBeanArrayList.add(laserUploadBean);
                }

            }
        };
        KeyManager.getInstance().listen(KeyTools.createKey(CameraKey.KeyNewlyGeneratedMediaFile), this, generatedMediaFileListener);
    }

    public void onControlMission(MissionJson missionJson) {
        if (mChosenMission == null) {
            ToastUtil.show("没有执行的任务");
            return;
        }
        switch (missionJson.getCode()) {
            case "pause":
                ToastUtil.show("接收到暂停任务指令");
                missionHelper.pauseMission();
                break;
            case "estop":
            case "stop":
                ToastUtil.show("接收到停止任务指令");
                missionHelper.stopMission();
                break;
            case "resume":
                ToastUtil.show("接收到恢复任务指令");
                int state = missionHelper.getCurrentState();
                if (state == EXECUTION_PAUSED) {
                    missionHelper.resumeMission();
                }
                break;
            case "rths":
                ToastUtil.show("接收到返航指令");
                if (isFlying) {
                    KeyManager.getInstance().performAction(KeyTools.createKey(FlightControllerKey.KeyStartGoHome), new CommonCallbacks.CompletionCallbackWithParam<EmptyMsg>() {
                        @Override
                        public void onSuccess(EmptyMsg emptyMsg) {
                            XLogUtil.INSTANCE.i(TAG,"onSuccess: " + "开始返航");
                        }

                        @Override
                        public void onFailure(@NonNull IDJIError error) {

                        }
                    });
                }
                break;
        }
    }

    List<MissionInfoBean.FlightParams.FlightPath> offLineFlightPaths;

    public void TakeOffLineMission(MissionInfoBean missionListInfo) {
        isOffLineMission = true;
        if (missionListInfo.getFlightParams().getChildrenList() != null && !missionListInfo.getFlightParams().getChildrenList().isEmpty()) {
            offlineController = new OfflineController(missionListInfo.getFlightParams().getChildrenList().get(0).getIndex()); //金凯只会给我传一个任务
        } else {
            offlineController = new OfflineController(missionListInfo.getFlightParams().getFlightPath().get(0).getIndex());
        }

        missionStatus.setMissionID(missionListInfo.getMissionId());
        XLogUtil.INSTANCE.d(TAG, "TakeOffLineMission: " + missionListInfo.getMissionId());
        missionStatus.setMissionName(missionListInfo.getMissionName());
        DJIAircraftApplication.getInstance().setMissionStatus(missionStatus);
        DJIAircraftApplication.getInstance().setSubState(202);

        MissionInfoBean.FlightParams flightParams = missionListInfo.getFlightParams();
        if (flightParams.getChildrenList() == null || flightParams.getChildrenList().isEmpty()) {
            offLineFlightPaths = flightParams.getFlightPath();
        } else {
            offLineFlightPaths = flightParams.getChildrenList().get(0).getFlightPath();
        }
        autoFly = true;
        ToastUtil.show("接收到任务指令");
        WaypointMission waypointMission = new WaypointMission();
        waypointMission.setHeadingMode(WaypointMission.HeadingMode.UsingWaypointHeading);
        waypointMission.setFinishedAction(BaseMission.FinishedAction.GoHome);
        waypointMission.setFlySpeed((float) flightParams.getCruiseSpeed());
        waypointMission.setMissionID(missionListInfo.getMissionId());
        waypointMission.setMissionBatch("GF" + System.currentTimeMillis()); //离线任务造一个missionbatch是为了断点续飞用
        waypointMission.setName(missionListInfo.getMissionName() + "-扫图");
        waypointMission.setMyMissionType(missionListInfo.getMissionType());
        waypointMission.setMissionCheck(missionListInfo.getMissionCheck());
        waypointMission.setHeightType(missionListInfo.getHeightType());
        waypointMission.setMissionCheck(missionListInfo.getMissionCheck());
        waypointMission.setUavSTAltitude((int) missionListInfo.getInHeight());
        waypointMission.setUavRHAltitude((int) missionListInfo.getOutHeight());
        waypointMission.setGlobalWaypointTurnMode(missionListInfo.getFlightParams().getHoverPhoto() == 0 ? "toPointAndPassWithContinuityCurvature" : "toPointAndStopWithDiscontinuityCurvature");
        waypointMission.setOfflineMission(isOffLineMission);

        /*返航不需要加点，我直接设置返航高度就行了，也就是目前只需要加一个点*/
        int uavRHAltitude = (int) missionListInfo.getOutHeight();
        uavRHAltitude = uavRHAltitude > 0 ? uavRHAltitude : 100;
        setGoHomeHeight(uavRHAltitude);

        for (MissionInfoBean.FlightParams.FlightPath flightpath : offLineFlightPaths) {
            WaypointMission.Waypoint waypoint = new WaypointMission.Waypoint();
            waypoint.setLatLng(new AppLatLng(flightpath.getLatitude(), flightpath.getLongitude()));
            waypoint.setGimbalPitch((short) flightpath.getGimbalPitch());
            waypoint.setHeading((short) (flightpath.getHeading() >= -180 && flightpath.getHeading() <= 180 ? flightpath.getHeading() : 0));
            waypoint.setAltitude((float) flightpath.getAltitude());
            waypoint.setPointName(flightpath.getName());
            waypoint.setTurnMode(BaseMission.TurnMode.Clockwise);
            waypoint.setSpeed((float) flightpath.getSpeed());
            waypoint.setWaypointTurnMode((String) flightpath.getWaypointTurnMode());
            waypoint.addWaypointAction(new WaypointMission.Waypoint.Action(WaypointMission.Waypoint.ActionType.StartTakePhoto, 0));
            waypointMission.addWaypoint(waypoint);
        }

        judgeMission(waypointMission);
    }

    public void onRemoteMission(MissionJson missionJson) {
        isOffLineMission = false;
        laserUploadBeanArrayList.clear();
        this.missionJson = missionJson;
        if (missionJson.getIsGzipMission() == 1) {
            String missionString = CompressUtil.decompressEncode(missionJson.getGzipMission());
            List<Mission> list = com.alibaba.fastjson.JSONArray.parseArray(missionString, Mission.class);
            missionJson.setMission(list);
        }
        missionStatus.setMissionID(missionJson.getMissionID());
        missionStatus.setMissionBatch(missionJson.getMissionBatch());
        missionStatus.setMissionName(missionJson.getMissionName());
        DJIAircraftApplication.getInstance().setMissionStatus(missionStatus);
        DJIAircraftApplication.getInstance().setSubState(202);

        autoFly = true;
        Mission mission = missionJson.getMission().get(0);
        List<Flightpath> flightPaths = mission.getFlightpath();
        ToastUtil.show("接收到任务指令");
        WaypointMission waypointMission = new WaypointMission();
        waypointMission.setGlobalWaypointTurnMode(mission.getGlobalWaypointTurnMode());
        waypointMission.setHeadingMode(WaypointMission.HeadingMode.UsingWaypointHeading);
        waypointMission.setFinishedAction(BaseMission.FinishedAction.GoHome);
        XLogUtil.INSTANCE.d(TAG, "onRemoteMission: speed" + mission.getAutoflightSpeed());
        waypointMission.setFlySpeed(mission.getAutoflightSpeed());
        waypointMission.setMissionID(missionJson.getMissionID());
        waypointMission.setUavSTAltitude(missionJson.getUavSTAltitude());
        waypointMission.setUavRHAltitude(missionJson.getUavRHAltitude());
        waypointMission.setMissionBatch(missionJson.getMissionBatch());
        waypointMission.setName(missionJson.getMissionName());

        String params = missionJson.getParams();
        try {
            org.json.JSONObject jsonObject = new org.json.JSONObject(params);
            int missionCheck = jsonObject.getInt("missionCheck");//驭光的施工检测任务类型判断
            waypointMission.setMissionCheck(missionCheck);
        } catch (JSONException e) {
            e.printStackTrace();
        }

        /*返航不需要加点，我直接设置返航高度就行了，也就是目前只需要加一个点*/
        int uavRHAltitude = missionJson.getUavRHAltitude();
        uavRHAltitude = uavRHAltitude > 0 ? uavRHAltitude : 100;
        setGoHomeHeight(uavRHAltitude);

        for (Flightpath flightpath : flightPaths) {
            WaypointMission.Waypoint waypoint = new WaypointMission.Waypoint();
            //waypoint.setLatLng(new AppLatLng(flightpath.getLatitude(), flightpath.getLongitude()));
            if (mission.getGCSType() == 2) {
                LocateInfo locateInfo = GCJ02_WGS84.gcj02_To_Wgs84(flightpath.getLatitude(), flightpath.getLongitude());
                waypoint.setLatLng(new AppLatLng(locateInfo.getLatitude(), locateInfo.getLongitude()));
            } else {
                waypoint.setLatLng(new AppLatLng(flightpath.getLatitude(), flightpath.getLongitude()));
            }

            waypoint.setGimbalPitch((short) flightpath.getGimbal());
            waypoint.setHeading((short) (flightpath.getHeading() >= -180 && flightpath.getHeading() <= 180 ? flightpath.getHeading() : 0));
            waypoint.setAltitude(flightpath.getAltitude());
            waypoint.setPointName(flightpath.getName());
            waypoint.setTurnMode(BaseMission.TurnMode.Clockwise);
            waypoint.setSpeed(flightpath.getSpeed());
            waypoint.setWaypointTurnMode(flightpath.getWaypointTurnMode());
            waypoint.addWaypointAction(new WaypointMission.Waypoint.Action(WaypointMission.Waypoint.ActionType.StartTakePhoto, 0));
            waypointMission.addWaypoint(waypoint);
        }

        judgeMission(waypointMission);
    }

    private void judgeMission(WaypointMission waypointMission) {
        //在这清除上一次的任务
        if (mChosenMission != null && mMissionMapPainter != null) {
            mMissionMapPainter.remove(mChosenMission);
            activity.getmMapPainter().clearTrajectory();
        }

        //判断这个任务是不是上次未执行完的
        String missionBatch = SpUtil.getMissionBatch();
        resumeMissionDetail = SpUtil.getMissionData(missionBatch);

        if (resumeMissionDetail == null) {
            isResumeMission = false;
            isCustomMission = false;
            dealMission(waypointMission);
        } else {
            WaypointMission resumeWaypointMission = resumeMissionDetail.getWaypointMission();
            String resumeMissionID = resumeWaypointMission != null ? resumeWaypointMission.getMissionID() : null;

            // 修复断点续飞弹窗逻辑：
            // 1. 任务已完成 - 不显示弹窗
            // 2. 当前任务ID与保存的任务ID不匹配（且保存的任务ID不为空）- 不显示弹窗
            // 3. 保存的任务ID为空但任务未完成 - 显示弹窗（这是断点续飞后的情况）
            boolean shouldShowDialog = !resumeMissionDetail.isComplete() &&
                    (TextUtils.isEmpty(resumeMissionID) || TextUtils.equals(resumeMissionID, waypointMission.getMissionID()));

            if (!shouldShowDialog) {
                isResumeMission = false;
                isCustomMission = false;
                dealMission(waypointMission);
            } else {
                int position = resumeMissionDetail.getCurrentPosition();
                AlertDialog.Builder builder = new AlertDialog.Builder(ContextUtil.getCurrentActivity());
                AlertDialog dialog = builder.create();
                dialog.setCanceledOnTouchOutside(true);
                dialog.setCancelable(false);

                View view = View.inflate(ContextUtil.getCurrentActivity(), R.layout.resume_dialog, null);
                Button btnReStart = view.findViewById(R.id.btn_restart);
                Button btnContinue = view.findViewById(R.id.btn_continue);
                Button btnCustomePoint = view.findViewById(R.id.btn_custom_point);
                btnReStart.setOnClickListener(v -> {
                    isResumeMission = false;
                    isCustomMission = false;
                    dealMission(waypointMission);
                    dialog.dismiss();
                });
                btnContinue.setOnClickListener(v -> {
                    isResumeMission = true;
                    isCustomMission = false;

                    // 用户确认断点续飞后立即通知服务端
                    notifyBreakpointResumeStatus();

                    if (productType == ProductType.M300_RTK || productType == ProductType.M350_RTK) {
                        List<WaypointMission.Waypoint> resumeWaypointList = new ArrayList<>();
                        WaypointMission resumeMission;
                        for (int i = position + 1; i < waypointMission.getWaypointList().size(); i++) {
                            resumeWaypointList.add(waypointMission.getWaypointList().get(i));
                        }
                        resumeMission = waypointMission;
                        resumeMission.setWaypointList(resumeWaypointList);
                        // 保留原始missionID，以便后续断点续飞判断

                        this.mChosenMission = resumeMission;
                        activity.runOnUiThread(new Runnable() {
                            @Override
                            public void run() {
                                missionDetail = resumeMissionDetail.clone();
                                initUI(activity.getMapController());
                                initMissionHelper();
                                if (dji.v5.ux.core.util.SpUtil.getIsAutoFly()) {
                                    ToastUtil.show("三秒后自动执行任务");
                                    ContextUtil.getHandler().postDelayed(() -> binding.viewMissionPanel.ivMissionStartLay.callOnClick(), 3000);
                                }
                                //ContextUtil.getHandler().postDelayed(() -> binding.viewMissionPanel.ivMissionStartLay.callOnClick(), 10);
                            }
                        });
                    } else {
                        dealMission(waypointMission);
                    }
                    dialog.dismiss();
                });
                btnCustomePoint.setOnClickListener(v -> {
                    dialog.dismiss();
                    showCustomPointDialog(waypointMission);
                });
                //设置背景透明,去四个角
                dialog.getWindow().setBackgroundDrawableResource(android.R.color.transparent);
                dialog.show();
                dialog.getWindow().setLayout(DensityUtil.dip2px(ContextUtil.getCurrentActivity(), 290), LinearLayout.LayoutParams.WRAP_CONTENT);
//        dialog.getWindow().setWindowAnimations(R.style.AnimMM);
                dialog.setContentView(view);
            }
        }
    }

    private void showCustomPointDialog(WaypointMission waypointMission) {
        Dialog dialog = new Dialog(ContextUtil.getCurrentActivity());
        dialog.setCanceledOnTouchOutside(true);
        dialog.setCancelable(false);

        View view = View.inflate(ContextUtil.getCurrentActivity(), R.layout.custom_point_dialog, null);
        Button buttonCancel = view.findViewById(R.id.btn_alert_cancel);
        Button buttonOk = view.findViewById(R.id.btn_alert_ok);
        EditText editPoint = view.findViewById(R.id.edit_point);
        buttonCancel.setOnClickListener(v -> {
            dialog.dismiss();
        });
        buttonOk.setOnClickListener(v -> {
            int position = Integer.parseInt(editPoint.getText().toString());
            resumeMissionDetail.setCurrentPosition(position);//下面上传任务动作要helper里面会用到
            if (position < 1 || position > waypointMission.getWaypointList().size() - 1) {
                ToastUtil.show("航点不在区间内，请重新填写");
                return;
            }
            List<WaypointMission.Waypoint> resumeWaypointList = new ArrayList<>();
            WaypointMission resumeMission;
            for (int i = position - 1; i < waypointMission.getWaypointList().size(); i++) {
                resumeWaypointList.add(waypointMission.getWaypointList().get(i));
            }
            resumeMission = waypointMission;
            resumeMission.setWaypointList(resumeWaypointList);
            // 保留原始missionID，以便后续断点续飞判断

            this.mChosenMission = resumeMission;
            activity.runOnUiThread(new Runnable() {
                @Override
                public void run() {
                    isResumeMission = false;
                    isCustomMission = true;
                    missionDetail = resumeMissionDetail.clone();
                    initUI(activity.getMapController());
                    initMissionHelper();
                    if (dji.v5.ux.core.util.SpUtil.getIsAutoFly()) {
                        ToastUtil.show("三秒后自动执行任务");
                        ContextUtil.getHandler().postDelayed(() -> binding.viewMissionPanel.ivMissionStartLay.callOnClick(), 3000);
                    }
                }
            });
            dialog.dismiss();
        });
        //设置背景透明,去四个角
        dialog.getWindow().setBackgroundDrawableResource(android.R.color.transparent);
        dialog.show();
        dialog.getWindow().setLayout(DensityUtil.dip2px(ContextUtil.getCurrentActivity(), 290), LinearLayout.LayoutParams.WRAP_CONTENT);
        dialog.setContentView(view);
    }

    private void dealMission(WaypointMission waypointMission) {
        // 销毁旧 Helper
        if (missionHelper != null) {
            missionHelper.removeListener();
            missionHelper = null;
        }
        this.mChosenMission = waypointMission;
        if (autoFly && mChosenMission != null) {
            activity.runOnUiThread(new Runnable() {
                @Override
                public void run() {
                    initUI(activity.getMapController());
                    initMissionHelper();
                    if (dji.v5.ux.core.util.SpUtil.getIsAutoFly()) {
                        ToastUtil.show("三秒后自动执行任务");
                        ContextUtil.getHandler().postDelayed(() -> binding.viewMissionPanel.ivMissionStartLay.callOnClick(), 3000);
                    }
                }
            });
        }
    }

    private void setGoHomeHeight(int GomeHeight) {
        KeyManager.getInstance().setValue(KeyTools.createKey(FlightControllerKey.KeyGoHomeHeight), GomeHeight, new CommonCallbacks.CompletionCallback() {
            @Override
            public void onSuccess() {
                ToastUtil.show("返航高度已设置成" + GomeHeight + "米");
            }

            @Override
            public void onFailure(@NonNull IDJIError error) {
                ToastUtil.show("返航高度设置失败:" + error.description());
            }
        });
    }

    private void initMission(String missionString) {
        if (missionString != null) {
            this.mChosenMission = JsonUtil.fromJson(missionString, WaypointMission.class);
        }
    }

    @SuppressLint("SetTextI18n")
    private void initMissionHelper() {
        if (mChosenMission != null) {
            if (missionHelper != null) {
                Log.e(TAG, "initMissionHelper: 再次接收任务，初始化helper");
                missionHelper.removeListener();
                missionHelper = null;
            }
            int waypointLength = mChosenMission.getWaypointList().size();
            if (waypointLength > 1) {
                binding.missionProgress.setVisibility(View.VISIBLE);
                binding.missionProgress.setText("1/" + waypointLength);
                boolean isOpenCheckList = dji.v5.ux.core.util.SpUtil.getOpenCheckList();
                if (isOpenCheckList && binding.btnCheckList.getVisibility() == View.GONE) {
                    binding.btnCheckList.setVisibility(View.VISIBLE);
                }
                if (mChosenMission.getWaypointList().get(0).getPointName() != null && !mChosenMission.getWaypointList().get(0).getPointName().isEmpty()) {
                    binding.missionName.setVisibility(View.VISIBLE);
                    binding.missionName.setText("航点名：" + mChosenMission.getWaypointList().get(0).getPointName());
                }
            }
            missionHelper = new WaypointMissionHelper();
            missionHelper.setResultListener(this);
            missionHelper.setMissionProcessCallback(index -> {
                if (index >= 0 && index <= mChosenMission.getWaypointList().size()) { // 添加索引检查
                    binding.missionProgress.setText((index + 1) + "/" + mChosenMission.getWaypointList().size());
                    if (mChosenMission.getWaypointList().get(index).getPointName() != null && !mChosenMission.getWaypointList().get(index).getPointName().isEmpty()) {
                        binding.missionName.setText("航点名：" + mChosenMission.getWaypointList().get(index).getPointName());
                    } else {
                        binding.missionName.setVisibility(View.GONE);
                    }
                    if (index > 0 && index > currentWaypointIndex) {
                        currentWaypointIndex = index;
                        //ToastUtil.show("当前第几个点" + currentWaypointIndex);
                        //这是为了mqtt上报
                        missionStatus.setCurrentIndex(currentWaypointIndex);
                        DJIAircraftApplication.getInstance().setMissionStatus(missionStatus);
                        //这是为了断点续飞
                        missionDetail.setCurrentPosition(currentWaypointIndex);
                        SpUtil.setMissionData(mChosenMission.getMissionBatch(), missionDetail);
                    }
                } else {
                    XLogUtil.INSTANCE.d(TAG, "Invalid index: " + index + ", size: " + waypointLength); // 添加日志记录
                }
            });
        }
    }

    private void initUI(MapController mapController) {
        if (mChosenMission != null) {

            binding.viewMissionPanel.main.setVisibility(View.VISIBLE);
            mMissionMapPainter = new MissionMapPainter(activity, mapController);

            mMissionMapPainter.draw(mChosenMission, true);
            LiveDataEvent.INSTANCE.isShowMissionNavi().observe(activity, isShow -> {
                if (isShow) {
                    mMissionMapPainter.showRoute();
                } else {
                    mMissionMapPainter.hideRoute();
                }
            });
            binding.viewMissionPanel.ivMissionStartLay.setOnClickListener(this);
            binding.viewMissionPanel.ivMissionPauseLay.setOnClickListener(this);
            binding.viewMissionPanel.ivMissionStopLay.setOnClickListener(this);

            setLayoutGroupEnable(ENABLE_START_BUTTON);
        }
    }

    public void onDestroy() {
        if (mMissionMapPainter != null) {
            mMissionMapPainter.removeAll();
            mMissionMapPainter = null;
        }

        if (missionHelper != null) {
            missionHelper.removeListener();
            missionHelper = null;
        }

        if (offlineController != null) {
            offlineController.onDestroy();
            offlineController = null;
        }

        KeyManager.getInstance().cancelListen(this);
        /*if (isFlyingListener != null) {
            KeyManager.getInstance().cancelListen(isFlyingListener);
        }
        if (generatedMediaFileListener != null) {
            KeyManager.getInstance().cancelListen(generatedMediaFileListener);
        }*/

    }

    private void uploadMission() {
        long currentTime = System.currentTimeMillis();
        if (currentTime - lastUploadTime < 2000) {
            return;
        } else {
            lastUploadTime = currentTime;
        }

        if (mChosenMission != null) {
            updateRemindText(R.string.fly_mission_upload_started);
            ContextUtil.getHandler().post(() -> binding.viewMissionPanel.ivMissionStart.updateProgress(0.01f));
            if (isOffLineMission) {
                //离线任务,这里是金凯返回的，跟我之前定义的还不一样
                List<Flightpath> originFlightPaths = new ArrayList<>();
                for (MissionInfoBean.FlightParams.FlightPath offLineFlightPath : offLineFlightPaths) {
                    Flightpath flightpath = new Flightpath();
                    List<FlightpathAction> actions = new ArrayList<>();
                    List<MissionInfoBean.FlightParams.FlightPath.Action> actionInfos = offLineFlightPath.getActions();
                    // 如果动作为空，则添加一个默认的空动作
                    if (actionInfos == null || actionInfos.isEmpty()) {
                        FlightpathAction defaultAction = new FlightpathAction();
                        defaultAction.setName("未知");
                        defaultAction.setParam(0);
                        actions.add(defaultAction);
                    } else {
                        // 遍历并转换每个 ActionInfo
                        for (MissionInfoBean.FlightParams.FlightPath.Action actionInfo : actionInfos) {
                            FlightpathAction flightpathAction = new FlightpathAction();
                            String actionName = ActionTypeEnum.getActionByType(actionInfo.getAction());
                            flightpathAction.setName(actionName);
                            float param = (float) actionInfo.getActionParams();
                            flightpathAction.setParam(param);
                            XLogUtil.INSTANCE.d(TAG, "uploadMission: 原来的参数" + actionInfo.getActionParams() +
                                    "  后面的参数：" + param);
                            actions.add(flightpathAction);
                        }
                        // 如果动作存在，则设置航点名称
                        flightpath.setName(offLineFlightPath.getName());
                    }
                    flightpath.setHeading((float) offLineFlightPath.getHeading());
                    flightpath.setAltitude((float) offLineFlightPath.getAltitude());
                    flightpath.setGimbal((float) offLineFlightPath.getGimbalPitch());
                    flightpath.setLatitude(offLineFlightPath.getLatitude());
                    flightpath.setLongitude(offLineFlightPath.getLongitude());
                    flightpath.setName(offLineFlightPath.getName()); // 航点的别名
                    flightpath.setActions(actions);
                    flightpath.setName(offLineFlightPath.getName()); //航点的别名
                    originFlightPaths.add(flightpath);
                }
                if (isCustomMission) {
                    int position = resumeMissionDetail.getCurrentPosition();
                    List<Flightpath> resumeFlightPaths = new ArrayList<>();
                    for (int i = position - 1; i < originFlightPaths.size(); i++) {
                        resumeFlightPaths.add(originFlightPaths.get(i));
                    }
                    missionHelper.setFlyPath(resumeFlightPaths, missionJson);
                } else if (isResumeMission){
                    int position = resumeMissionDetail.getCurrentPosition();
                    if (productType == ProductType.M300_RTK || productType == ProductType.M350_RTK) {
                        List<Flightpath> resumeFlightPaths = new ArrayList<>();
                        for (int i = position + 1; i < originFlightPaths.size(); i++) {
                            resumeFlightPaths.add(originFlightPaths.get(i));
                        }
                        missionHelper.setFlyPath(resumeFlightPaths, missionJson);
                    } else {
                        missionHelper.setFlyPath(originFlightPaths, missionJson);
                    }
                } else {
                    missionHelper.setFlyPath(originFlightPaths, missionJson);
                }
            }//断点续飞的任务missionJson，这里处理一下
            else if (missionJson != null) {
                List<Flightpath> originFlightPaths = missionJson.getMission().get(0).getFlightpath();
                if (isCustomMission) {
                    int position = resumeMissionDetail.getCurrentPosition();
                    List<Flightpath> resumeFlightPaths = new ArrayList<>();
                    for (int i = position - 1; i < originFlightPaths.size(); i++) {
                        resumeFlightPaths.add(originFlightPaths.get(i));
                    }
                    missionHelper.setFlyPath(resumeFlightPaths, missionJson);
                } else if (isResumeMission) {
                    int position = resumeMissionDetail.getCurrentPosition();
                    if (productType == ProductType.M300_RTK || productType == ProductType.M350_RTK) {
                        List<Flightpath> resumeFlightPaths = new ArrayList<>();
                        for (int i = position + 1; i < originFlightPaths.size(); i++) {
                            resumeFlightPaths.add(originFlightPaths.get(i));
                        }
                        missionHelper.setFlyPath(resumeFlightPaths, missionJson);
                    } else {
                        missionHelper.setFlyPath(originFlightPaths, missionJson);
                    }
                } else {
                    missionHelper.setFlyPath(originFlightPaths, missionJson);
                }
            }

            missionHelper.setWayPointMission(mChosenMission);
            ContextUtil.getHandler().postDelayed(new Runnable() {
                @Override
                public void run() {
                    prepareForUpload();
                }
            }, 1000);

        }
    }

    /**
     * 通知服务端断点续飞状态
     */
    private void notifyBreakpointResumeStatus() {
        if (mChosenMission == null) {
            XLogUtil.INSTANCE.w(TAG, "任务为空，无法通知断点续飞状态");
            return;
        }

        String missionId = mChosenMission.getMissionID();
        if (TextUtils.isEmpty(missionId)) {
            XLogUtil.INSTANCE.w(TAG, "任务ID为空，无法通知断点续飞状态");
            return;
        }

        boolean isBreakpoint = BreakpointManager.getInstance().isBreakpointResume();

        XLogUtil.INSTANCE.d(TAG, "开始通知断点续飞状态 - 任务ID: " + missionId + ", 断点续飞: " + isBreakpoint);

        // 使用工具类调用Kotlin挂起函数
        CoroutineUtils.callIsBreakpointResume(missionId, isBreakpoint, new CoroutineUtils.ResultCallback<String>() {
            @Override
            public void onSuccess(String result) {
                activity.runOnUiThread(() -> {
                    ToastUtil.show("断点续飞状态通知成功");
                    XLogUtil.INSTANCE.i(TAG, "断点续飞通知成功 - 返回结果: " + result);
                });
            }

            @Override
            public void onError(Exception e) {
                activity.runOnUiThread(() -> {
                    String errorMsg = e.getMessage();
                    ToastUtil.show("断点续飞状态通知失败: " + errorMsg);
                    XLogUtil.INSTANCE.e(TAG, "断点续飞通知失败" + e);
                });
            }
        });
    }

    private void startMission() {
        XLogUtil.INSTANCE.d(TAG, "startMission: ");
        if (readyForMission()) {
            int state = missionHelper.getCurrentState();
            if (state == EXECUTION_PAUSED) {
                missionHelper.resumeMission();
            } else if (state == READY_TO_EXECUTE) {
                if (isResumeMission && productType != ProductType.M300_RTK && productType != ProductType.M350_RTK) {
                    missionHelper.startBPMission();
                } else {
                    missionHelper.startMission();
                }
            }
        }
    }


    /*现在把起飞，上传任务和开始任务合并到一个按钮操作了，但是前提是飞机必须先起飞才能上传任务*/
    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.iv_mission_start_lay:

                if (!readyForStart()) {
                    return;
                }


               /* if (!isFlying) {
                    isClickStartMission = true;
                    if (mChosenMission != null) {
                        new AutoTakeOffDialog(autoFly);
                    }
                } else {*/
                isMissionFinish = false;
                //*判断是否是暂停状态*//*
                int state = missionHelper.getCurrentState();
                if (state == EXECUTION_PAUSED) {
                    missionHelper.resumeMission();
                } else {
                    setGimbalFollowMode();
                    uploadMission();
                }
                // }
                break;
            case R.id.iv_mission_pause_lay:
                missionHelper.pauseMission();
                break;
            case R.id.iv_mission_stop_lay:
                missionHelper.stopMission();
                break;
        }
    }

    private void setGimbalFollowMode() {
        KeyManager.getInstance().setValue(KeyTools.createKey(GimbalKey.KeyGimbalMode), GimbalMode.YAW_FOLLOW, new CommonCallbacks.CompletionCallback() {
            @Override
            public void onSuccess() {
                XLogUtil.INSTANCE.d(TAG, "setGimbalFollowMode: 设置云台跟随成功");
                rotateGimbal();
            }

            @Override
            public void onFailure(@NonNull IDJIError error) {
                XLogUtil.INSTANCE.d(TAG, "setGimbalFollowMode: 设置云台跟随失败" + error.description());
                ToastUtil.show("设置云台跟随失败" + error.description());
            }
        });
    }

    private void rotateGimbal() {
        float firstPointPitch = mChosenMission.getWaypointList().get(0).getGimbalPitch();
        GimbalAngleRotation gimbalAngleRotation = new GimbalAngleRotation();
        gimbalAngleRotation.setPitch((double) firstPointPitch);
        gimbalAngleRotation.setRollIgnored(true);
        gimbalAngleRotation.setYawIgnored(true);
        KeyManager.getInstance().performAction(KeyTools.createKey(GimbalKey.KeyRotateByAngle), gimbalAngleRotation, new CommonCallbacks.CompletionCallbackWithParam<EmptyMsg>() {
            @Override
            public void onSuccess(EmptyMsg emptyMsg) {
                XLogUtil.INSTANCE.d(TAG, "rotateGimbal: 设置云台俯仰角度成功" + CommExtKt.toJsonStr(gimbalAngleRotation));
                //ToastUtil.show("设置云台俯仰角度成功");
            }

            @Override
            public void onFailure(@NonNull IDJIError error) {
                XLogUtil.INSTANCE.d(TAG, "rotateGimbal: 设置云台俯仰角度失败" + error.description());
                ToastUtil.show("设置云台俯仰角度失败:" + error.description());
            }
        });
    }

    private boolean readyForMission() {
        // 设置返航点
        /*Boolean isHomeSet = (Boolean) KeyManager.getInstance().getValue(FlightControllerKey.create(FlightControllerKey.IS_HOME_LOCATION_SET));
        if (isHomeSet == null || !isHomeSet) {
            PopupWindowController.getInstance().getPopWindowFromCenter().addRemindingView(R.string.set_home_first, false);
            return false;
        }
        // 需起飞无人机
        Boolean isFlying = (Boolean) KeyManager.getInstance().getValue(FlightControllerKey.create(FlightControllerKey.IS_FLYING));
        if (isFlying == null || !isFlying) {
            PopupWindowController.getInstance().getPopWindowFromCenter().addRemindingView(R.string.fly_first_route, false);
            return false;
        }
*/
        return true;
    }

    private boolean readyForStart() {
        /*if (!DJIHelper.getInstance().isAircraftConnected()) {
            PopupWindowController.getInstance().getPopWindowFromCenter().addRemindingView(R.string.please_check_connection, true);
            return false;
        }

        Boolean isLanding = (Boolean) KeyManager.getInstance().getValue(FlightControllerKey.create(FlightControllerKey.IS_LANDING));
        if (isLanding == null || isLanding) {
            PopupWindowController.getInstance().getPopWindowFromCenter().addRemindingView(R.string.fly_auto_home, false);
            return false;
        }

        Boolean isGoingHome = (Boolean) KeyManager.getInstance().getValue(FlightControllerKey.create(FlightControllerKey.IS_GOING_HOME));
        if (isGoingHome == null || isGoingHome) {
            PopupWindowController.getInstance().getPopWindowFromCenter().addRemindingView(R.string.fly_auto_home, false);
            return false;
        }*/

        return true;
    }

    private void prepareForUpload() {
        ContextUtil.getHandler().post(() -> setLayoutGroupEnable(ENABLE_NONE));
        missionHelper.stopLastAndUploadMission();
    }

    @Override
    public void onResult(int state, float value, String err) {
        switch (state) {
            case MISSION_UPLOAD:
                setLayoutGroupEnable(ENABLE_START_BUTTON);
                onResultUpload(value, err);
                break;
            case MISSION_START:
                currentWaypointIndex = -1;
                onResultStart(value, err);
                missionDetail.setComplete(false);
                break;
            case MISSION_PAUSE:
                onResultPause(value, err);
                break;
            case MISSION_RESUME:
                onResultResume(value, err);
                break;
            case MISSION_STOP:
                //currentWaypointIndex = -1;
                onResultStop(value, err);
                break;
            case MISSION_FINISH:
                //currentWaypointIndex = -1;
                onResultMissionFinish(value, err);
                break;
            case UNKNOWN:
                setLayoutGroupEnable(ENABLE_START_BUTTON);
                onResultUnknown(value, err);
                break;
        }
    }

    private void onResultUpload(float value, String err) {
        if (value > 0 && value < 1) {
            XLogUtil.INSTANCE.d(TAG, "onResultUpload: " + value);
            binding.viewMissionPanel.ivMissionStart.updateProgress(value);
        } else if (value == VALUE_FINISHED) {
            setLayoutGroupEnable(ENABLE_NONE);
            binding.viewMissionPanel.ivMissionStart.backToNormal();
            updateRemindText(R.string.fly_mission_upload_finished);
            updateRemindText(R.string.fly_mission_prepare);
            /*上传任务后直接开始任务*/
            ContextUtil.getHandler().postDelayed(this::startMission, 3000);

        } else if (value == VALUE_FAILED) {
            updateRemindText("任务上传失败:" + err, true);
            binding.viewMissionPanel.ivMissionStart.backToNormal();
        }
    }


    private void onResultStart(float value, String err) {
        if (value == VALUE_SUCCEED) {
            if (!isResumeMission && !isCustomMission) {
                missionDetail.setStartTime(System.currentTimeMillis());
            }
            //这里断点续飞只支持一次，如果第二次没飞完，任务只能作废
            missionDetail.setWaypointMission(mChosenMission);
            SpUtil.setMissionBatch(mChosenMission.getMissionBatch());
            SpUtil.setMissionData(mChosenMission.getMissionBatch(), missionDetail);

            if (offlineController != null) {//开启离线数据的存储
                offlineController.saveOfflineData();
            }

            updateRemindText(R.string.fly_mission_start_succeed);
            if (isFlying) {   //这么判断的原因是，当虚拟摇杆关闭后，重新开始任务，还是显示暂停
                DJIAircraftApplication.getInstance().setSubState(209);
            }
            setLayoutGroupEnable(ENABLE_PAUSE_BUTTON | ENABLE_STOP_BUTTON);
            //MissionPhotoUploader.getInstance().setUploading(false);//每次开始前初始化上传状态，可能上一次上传失败了，不能影响下次任务执行
            DJIAircraftApplication.getInstance().setLastWaypoint(mChosenMission.getWaypointList().get(mChosenMission.getWaypointList().size() - 1).getLatLng());
            //startLaser();
            LiveDataEvent.INSTANCE.isMissionFinished().postValue(false);
        } else if (value == VALUE_FAILED) {
            updateRemindText("任务开始失败:" + err, true);
            setLayoutGroupEnable(ENABLE_START_BUTTON);
            binding.viewMissionPanel.ivMissionStart.backToNormal();
        }
    }

    private void onResultStop(float value, String err) {
        if (value == VALUE_SUCCEED) {
            setLayoutGroupEnable(ENABLE_START_BUTTON);
            binding.missionProgress.setVisibility(View.GONE);
            binding.missionName.setVisibility(View.GONE);
            binding.btnCheckList.setVisibility(View.GONE);
            binding.viewMissionPanel.main.setVisibility(View.GONE);
            updateRemindText(R.string.fly_mission_stop_succeed);
            //DJIAircraftApplication.getInstance().setLastWaypoint(null);
            //ServerResponse.getInstance().postSucceed(RemoteCmdMgr.TYPE_ROUTE_STOP);
        } else if (value == VALUE_FAILED) {
            updateRemindText("任务停止失败:" + err, true);
            //ServerResponse.getInstance().post(RemoteCmdMgr.TYPE_ROUTE_STOP, "任务结束失败：" + err, "", false);
        }
    }

    private void onResultPause(float value, String err) {
        if (value == VALUE_SUCCEED) {
            setLayoutGroupEnable(ENABLE_START_BUTTON | ENABLE_STOP_BUTTON);
            updateRemindText(R.string.fly_mission_pause_succeed);
            DJIAircraftApplication.getInstance().setSubState(221);
            //ServerResponse.getInstance().postSucceed(RemoteCmdMgr.TYPE_ROUTE_PAUSE);

        } else if (value == VALUE_FAILED) {
            updateRemindText("任务暂停失败:" + err, true);
            //ServerResponse.getInstance().post(RemoteCmdMgr.TYPE_ROUTE_PAUSE, "任务暂停失败：" + err, "", false);

        }
    }

    private void onResultResume(float value, String err) {
        if (value == VALUE_SUCCEED) {
            setLayoutGroupEnable(ENABLE_PAUSE_BUTTON | ENABLE_STOP_BUTTON);
            updateRemindText(R.string.fly_mission_resume_succeed);
            DJIAircraftApplication.getInstance().setSubState(209);
            //ServerResponse.getInstance().postSucceed(RemoteCmdMgr.TYPE_ROUTE_RESUME);
        } else if (value == VALUE_FAILED) {
            updateRemindText("任务恢复失败:" + err, true);
            //ServerResponse.getInstance().post(RemoteCmdMgr.TYPE_ROUTE_RESUME, "任务恢复失败：" + err, "", false);
        }
    }

    private void onResultUnknown(float value, String err) {
        if (value == VALUE_FAILED) {
            updateRemindText("未知错误:" + err, true);
        }
    }

    private void onResultMissionFinish(float value, String err) {
        if (value == VALUE_SUCCEED) {
            //MissionDetail missionDetail = SpUtil.getMissionData(mChosenMission.getId());
            missionDetail.setEndTime(System.currentTimeMillis());
            SpUtil.setMissionData(mChosenMission.getMissionBatch(), missionDetail);
            isMissionFinish = true;
            binding.missionProgress.setVisibility(View.GONE);
            binding.missionName.setVisibility(View.GONE);
            binding.viewMissionPanel.main.setVisibility(View.GONE);
            binding.btnCheckList.setVisibility(View.GONE);
            ToastUtil.show(R.string.mission_complete);
            updateRemindText(R.string.mission_complete);
            LiveDataEvent.INSTANCE.isMissionFinished().postValue(true);
        } else if (value == VALUE_FAILED) {
            updateRemindText("任务恢复失败:" + err, true);
        }

        setLayoutGroupEnable(ENABLE_START_BUTTON);
    }

    private void startLaser() {
        if (DJIAircraftApplication.getInstance().getProductType() != ProductType.M30_SERIES
                && DJIAircraftApplication.getInstance().getProductType() != ProductType.M300_RTK
                && DJIAircraftApplication.getInstance().getProductType() != ProductType.M350_RTK) {
            return;
        }
        if (!mChosenMission.getName().contains("路点")) {
            is2D = true;
            XLogUtil.INSTANCE.d(TAG, "startLaser: ");
            laserListener = new CommonCallbacks.KeyListener<LaserMeasureInformation>() {
                @Override
                public void onValueChange(@Nullable LaserMeasureInformation oldValue, @Nullable LaserMeasureInformation laserMeasureInformation) {
                    if (laserMeasureInformation.getLaserMeasureState() == LaserMeasureState.NORMAL) {
                        targetDistance = laserMeasureInformation.getDistance().floatValue();
                        XLogUtil.INSTANCE.d(TAG, "startLaser onUpdate:" + targetDistance + "/" + laserMeasureInformation.getLaserMeasureState());
                    } else {
                        //XLogUtil.INSTANCE.e("TAG", "startLaser onUpdate: 参数不准");
                    }
                }
            };
            KeyManager.getInstance().listen(KeyTools.createKey(CameraKey.KeyLaserMeasureInformation), this, new CommonCallbacks.KeyListener<LaserMeasureInformation>() {
                @Override
                public void onValueChange(@Nullable LaserMeasureInformation oldValue, @Nullable LaserMeasureInformation newValue) {

                }
            });
            KeyManager.getInstance().setValue(KeyTools.createKey(CameraKey.KeyLaserMeasureEnabled), true, new CommonCallbacks.CompletionCallback() {
                @Override
                public void onSuccess() {
                    ToastUtil.show("激光雷达开启");
                }

                @Override
                public void onFailure(@NonNull IDJIError error) {
                    ToastUtil.show("激光雷达失败" + error.description());
                }
            });
        } else {
            is2D = false;
        }
    }

    private void stopLaser() {
        if (DJIAircraftApplication.getInstance().getProductType() != ProductType.M30_SERIES
                && DJIAircraftApplication.getInstance().getProductType() != ProductType.M300_RTK
                && DJIAircraftApplication.getInstance().getProductType() != ProductType.M350_RTK) {
            return;
        }
        if (is2D && laserListener != null) {
            XLogUtil.INSTANCE.d(TAG, "stopLaser: ");
            KeyManager.getInstance().cancelListen(laserListener);
            KeyManager.getInstance().setValue(KeyTools.createKey(CameraKey.KeyLaserMeasureEnabled), false, new CommonCallbacks.CompletionCallback() {
                @Override
                public void onSuccess() {
                    ToastUtil.show("激光雷达关闭");
                }

                @Override
                public void onFailure(@NonNull IDJIError error) {
                    ToastUtil.show("激光雷达关闭失败" + error.description());
                }
            });
        }
    }

    private void setLayoutGroupEnable(int state) {
        boolean isStart = false, isPause = false, isStop = false;
        switch (state) {
            case ENABLE_START_BUTTON:
                isStart = true;
                break;
            case ENABLE_START_BUTTON | ENABLE_STOP_BUTTON:
                isStart = true;
                isStop = true;
                break;
            case ENABLE_PAUSE_BUTTON | ENABLE_STOP_BUTTON:
                isPause = true;
                isStop = true;
                break;
            default:
                break;
        }

        setLayoutEnable(binding.viewMissionPanel.ivMissionStartLay, isStart);
        setLayoutEnable(binding.viewMissionPanel.ivMissionPauseLay, isPause);
        setLayoutEnable(binding.viewMissionPanel.ivMissionStopLay, isStop);

        setImageButtonState(binding.viewMissionPanel.ivMissionStart, isStart);
        setImageButtonState(binding.viewMissionPanel.ivMissionPause, isPause);
        setImageButtonState(binding.viewMissionPanel.ivMissionStop, isStop);
    }

    private void setLayoutEnable(RelativeLayout layout, boolean enable) {
        layout.setClickable(enable);
    }

    private void setImageButtonState(ImageButton imageButton, boolean enable) {
        if (enable) {
            DrawableCompat.setTint(imageButton.getDrawable(), ContextUtil.getApplicationContext().getResources().getColor(R.color.icon_light_1));
        } else {
            DrawableCompat.setTint(imageButton.getDrawable(), ContextUtil.getApplicationContext().getResources().getColor(R.color.black_gray));
        }
    }

    private void updateRemindText(int id) {
        if (id == lastShowToastID && (System.currentTimeMillis() - lastShowToastTime) < 1500) {
            return;//解决同一条信息回调多次，显示多次的问题
        }

        lastShowToastID = id;
        lastShowToastTime = System.currentTimeMillis();
        updateRemindText(ContextUtil.getString(id), false);
    }

    private void updateRemindText(final String text, final boolean isWarning) {
        ContextUtil.getHandler().post(() -> PopupWindowController.getInstance().getPopWindowFromCenter().addRemindingView(text, isWarning));
    }
}
