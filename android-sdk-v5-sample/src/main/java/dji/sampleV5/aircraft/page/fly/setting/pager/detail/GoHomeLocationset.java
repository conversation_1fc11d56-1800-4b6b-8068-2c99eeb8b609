package dji.sampleV5.aircraft.page.fly.setting.pager.detail;

import android.app.AlertDialog;
import android.content.Context;
import android.view.View;
import android.view.inputmethod.EditorInfo;
import android.view.inputmethod.InputMethodManager;
import android.widget.EditText;
import android.widget.ImageView;

import dji.sampleV5.aircraft.ContextUtil;
import dji.sampleV5.aircraft.DefaultLayoutActivity;
import dji.sampleV5.aircraft.R;
import dji.sampleV5.aircraft.databinding.MenuAicraftSetPagerBinding;
import dji.sampleV5.aircraft.util.ToastUtil;
import dji.sampleV5.aircraft.util.phone.ImmerseUtil;


public class GoHomeLocationset {
    private final String GPS_signal_weak;
    private final String gohomeLocationDialog_RCGPS;
    private int mI = 0;
    private AlertDialog alertDialog;
    private Double LONGITUDE;
    private Double LATITUDE;
    private final EditText tvASetGoHoneHigh;
    private final ImageView AircaftLocationSetGoHome;
    private double latitude = 0;
    private double longitude = 0;
    private final ImageView ivRCGPSGoHome;
    private final String gohomeLocationDialog1;
    private final String gohomeLocationDialog3;
    //public GPSData mgpsData;

    public GoHomeLocationset(MenuAicraftSetPagerBinding aicraftSetPagerBinding, DefaultLayoutActivity activity) {
        tvASetGoHoneHigh = aicraftSetPagerBinding.menuASetGohomeHigh.tvASetGoHoneHigh;
        AircaftLocationSetGoHome = aicraftSetPagerBinding.menuASetGohomeLocation.ivASetGoHome;
        ivRCGPSGoHome = aicraftSetPagerBinding.menuASetGohomeLocation.ivRCGPSGoHome;

        gohomeLocationDialog1 = ContextUtil.getString(R.string.go_home_location_dialog1);
        gohomeLocationDialog3 = ContextUtil.getString(R.string.go_home_location_dialog3);
        gohomeLocationDialog_RCGPS = ContextUtil.getString(R.string.go_home_location_dialog_rc_gps);
        GPS_signal_weak = ContextUtil.getString(R.string.gps_signal_weak);
    }

    public void setGoHomeLocationset() {
       /* FlightController flightController = DJIHelper.getInstance().getFlightController();
        if (flightController == null) {
            tvASetGoHoneHigh.setVisibility(View.GONE);
            AircaftLocationSetGoHome.setVisibility(View.GONE);
            ivRCGPSGoHome.setVisibility(View.INVISIBLE);
            return;
        }

        RemoteController rc = DJIHelper.getInstance().getRemoteController();
        if (rc != null) {
            rc.setGPSDataCallback(gpsData -> {
                mgpsData = gpsData;
                if (gpsData != null) {
                    ivRCGPSGoHome.setVisibility(View.VISIBLE);
                    ivRCGPSGoHome.setClickable(true);
                }
            });
        }*/

        //设置返航点位置
        setGoHomeLocation();
        //获得输入框的数值,并设置
        tvASetGoHoneHigh.setOnEditorActionListener((v, actionId, event) -> {
            if (actionId == EditorInfo.IME_ACTION_DONE) {//点击软键盘完成控件时触发的行为
                //关闭光标并且关闭软键盘
//                    mtvASetGoHoneHigh.setCursorVisible(false);
                InputMethodManager im = (InputMethodManager) ContextUtil.getApplicationContext().getSystemService(Context.INPUT_METHOD_SERVICE);
                im.hideSoftInputFromWindow(ContextUtil.getCurrentActivity().getCurrentFocus().getApplicationWindowToken(), InputMethodManager.HIDE_NOT_ALWAYS);
                getGOHomeHigh();
                setGOHomeHigh(mI);
            }
            return true;//消费掉该行为
        });
       /* KeyManager.getInstance().getValue(FlightControllerKey.create(FlightControllerKey.GO_HOME_HEIGHT_IN_METERS), new YNListener1<Integer>() {
            @Override
            public void onSuccess(final Integer value) {
                ContextUtil.getHandler().post(() -> tvASetGoHoneHigh.setText(Util.getDot(value, 0) + ""));
            }

            @Override
            public void onException(Throwable e) {

            }
        });*/
    }

    /**
     * 返航位置设置
     */
    private void setGoHomeLocation() {
        ivRCGPSGoHome.setOnClickListener(v -> selectRcGPSlocation());
        AircaftLocationSetGoHome.setOnClickListener(v -> GoHomePoint());
    }

    /**
     * 选择RCGPS为返航点
     */
    private void selectRcGPSlocation() {
        /*if (mgpsData != null) {

            GPSData.GPSLocation location = mgpsData.getLocation();
            latitude = location.getLatitude();
            longitude = location.getLongitude();
            if (latitude == 0 && longitude == 0) {
                showDialog(-1);
            } else {
                showDialog(-2);

            }
        } else {

            ContextUtil.getHandler().post(() -> ivRCGPSGoHome.setVisibility(View.INVISIBLE));
        }*/
    }

    private void setRCGPSGohomeocation() {
       /* LocationCoordinate2D locationCoordinate2D = new LocationCoordinate2D(latitude, longitude);
        if (Util.checkGpsCoordinate(locationCoordinate2D.getLatitude(), locationCoordinate2D.getLongitude())) {
            KeyManager.getInstance().setValue(FlightControllerKey.create(FlightControllerKey.HOME_LOCATION), locationCoordinate2D, new YNListener0() {
                @Override
                public void onSuccess() {
                    ToastUtil.show(ContextUtil.getString(R.string.set_success));
                }

                @Override
                public void onException(Throwable e) {
                    setToast(e.getLocalizedMessage());
                }
            });
        } else {
            setToast(GPS_signal_weak);
        }*/
    }

    public void setToast(final String message) {
        ContextUtil.getHandler().post(() -> ToastUtil.show(message));
    }

    public void GoHomePoint() {

        getGOHomeHigh();
        showDialog(mI);
    }

    private void showDialog(final int mI) {

        AlertDialog.Builder builder = new AlertDialog.Builder(ContextUtil.getCurrentActivity(), AlertDialog.THEME_HOLO_DARK);

        if (mI == -1) {
            builder.setMessage(GPS_signal_weak);
        } else if (mI == -2) {
            builder.setMessage(gohomeLocationDialog_RCGPS);
        } else {
            String format = String.format(gohomeLocationDialog1, mI);
            builder.setMessage(format);
            builder.setTitle(gohomeLocationDialog3);
        }
        builder.setPositiveButton(ContextUtil.getString(R.string.dialog_ok), (dialog, which) -> {
            if (mI == -2) {
                setRCGPSGohomeocation();
            } else if (mI == -1) {

            } else {
                setCurrentLocastoinGOhome();
            }
            alertDialog.dismiss();
        });
        builder.setNegativeButton(ContextUtil.getString(R.string.dialog_cancel), (dialog, which) -> alertDialog.dismiss());
        alertDialog = builder.create();
        ImmerseUtil.showDialog(alertDialog);
    }

    /**
     * 设置当前位置为返航点
     */
    private void setCurrentLocastoinGOhome() {
       /* KeyManager.getInstance().getValue(FlightControllerKey.create(FlightControllerKey.AIRCRAFT_LOCATION), new YNListener1<LocationCoordinate3D>() {
            @Override
            public void onSuccess(LocationCoordinate3D value) {
                LocationCoordinate2D homeLocation = new LocationCoordinate2D(value.getLatitude(), value.getLongitude());
                if (Util.checkGpsCoordinate(homeLocation.getLatitude(), homeLocation.getLongitude())) {
                    KeyManager.getInstance().setValue(FlightControllerKey.create(FlightControllerKey.HOME_LOCATION), homeLocation, new YNListener0() {
                        @Override
                        public void onSuccess() {
                            setToast(ContextUtil.getString(R.string.set_success));
                        }

                        @Override
                        public void onException(Throwable e) {
                            setToast(e.getLocalizedMessage());
                        }
                    });
                } else {
                    setToast(GPS_signal_weak);
                }
            }

            @Override
            public void onException(Throwable e) {
                setToast(e.getLocalizedMessage());
            }
        });*/
    }

    public void setGOHomeHigh(final int i) {
       /* KeyManager.getInstance().setValue(FlightControllerKey.create(FlightControllerKey.GO_HOME_HEIGHT_IN_METERS), i, new YNListener0() {
            @Override
            public void onSuccess() {
                ContextUtil.getHandler().post(() -> {
                    ToastUtil.show(ContextUtil.getString(R.string.set_success));
                    tvASetGoHoneHigh.setText(i + "");
                });
            }

            @Override
            public void onException(Throwable e) {
            }
        });*/
    }

    private void getGOHomeHigh() {
        final String s = tvASetGoHoneHigh.getText().toString().trim();
        if (!s.isEmpty()) {
            mI = Integer.valueOf(s);
            if (s != null) {
                if (500 < mI) {
                    mI = 500;
                } else if (mI < 20) {
                    mI = 20;
                }
            }
        }
    }

    public void unConnected() {
        tvASetGoHoneHigh.setVisibility(View.GONE);
        ivRCGPSGoHome.setVisibility(View.INVISIBLE);
        AircaftLocationSetGoHome.setVisibility(View.GONE);
    }

    public void Connected() {
       /* if (mgpsData != null) {
            ivRCGPSGoHome.setVisibility(View.VISIBLE);
        }*/
        tvASetGoHoneHigh.setVisibility(View.VISIBLE);
        AircaftLocationSetGoHome.setVisibility(View.VISIBLE);
    }
}
