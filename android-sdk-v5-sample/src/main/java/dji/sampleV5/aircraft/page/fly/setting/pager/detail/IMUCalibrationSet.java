package dji.sampleV5.aircraft.page.fly.setting.pager.detail;

import android.widget.RelativeLayout;


import dji.sampleV5.aircraft.ContextUtil;
import dji.sampleV5.aircraft.databinding.MenuAicraftSetPagerBinding;
import dji.sampleV5.aircraft.util.ToastUtil;
import dji.sampleV5.aircraft.util.phone.ImmerseUtil;

public class IMUCalibrationSet {
    private boolean isConnect;
    //private IMUCalibrationDialog imuCalibrationDialog;
    public IMUCalibrationSet(MenuAicraftSetPagerBinding aircraftSetPagerBinding) {
       /* RelativeLayout rl = aircraftSetPagerBinding.menuImuSet.rlImuSet;
        rl.setOnClickListener((v) -> {
            if(isConnect){
                if (imuCalibrationDialog == null){
                    imuCalibrationDialog = new IMUCalibrationDialog(ContextUtil.getCurrentActivity());
                }
                ImmerseUtil.showDialog(imuCalibrationDialog);
            }else {
                ToastUtil.show("请连接飞机");
            }
        });*/
    }

    public void connect(Boolean connect) {
        isConnect = connect;
    }
}
