package dji.sampleV5.aircraft.page.fly.controller;

import android.text.TextUtils;
import android.util.Log;


import dji.sampleV5.aircraft.ContextUtil;
import dji.sampleV5.aircraft.DJIAircraftApplication;
import dji.sampleV5.aircraft.DefaultLayoutActivity;
import dji.sampleV5.aircraft.R;
import dji.sampleV5.aircraft.databinding.ActivityDefaultLayoutBinding;
import dji.sampleV5.aircraft.util.ToastUtil;
import dji.sampleV5.aircraft.view.LiveControlView;

public class RtmpController implements RtmpStatusCallback, LiveControlView.OnClickCallback {


    private ActivityDefaultLayoutBinding binding;

    public RtmpController(DefaultLayoutActivity activity) {
        this.binding = activity.getBinding();
        binding.viewRtmpPanel.setRtmpClickListener(this);
    }

    public void onDestroy() {
        if (RTMPManager.getInstance().isPublishing()) {
            RTMPManager.getInstance().stopPublish();
        }
    }

    public void onConnected(boolean isConnected) {
        if (!isConnected) {
            if (RTMPManager.getInstance().isPublishing()) {
                RTMPManager.getInstance().stopPublish();
            }
        }
    }

    @Override
    public void onRTMPConnecting() {
        ContextUtil.getHandler().post(() -> {
            binding.viewRtmpPanel.changeVideoUI(true);
            binding.viewRtmpPanel.updateTextStatus(ContextUtil.getString(R.string.live_connecting));
        });
    }

    @Override
    public void onRTMPConnected() {
        updateTextStatus(ContextUtil.getString(R.string.live_connected));
    }

    @Override
    public void onRTMPDisconnected(boolean manually) {
        ContextUtil.getHandler().post(() -> {
            binding.viewRtmpPanel.changeVideoUI(false);
            binding.viewRtmpPanel.updateTextStatus(ContextUtil.getString(R.string.live_disconnected));
        });
    }

    @Override
    public void onRTMPInfo(final float fps, final float bitrate) {
        ContextUtil.getHandler().post(() -> binding.viewRtmpPanel.updateVideoFPSAndVideoBitrate(fps, bitrate));
    }

    private void updateTextStatus(final String status) {
        ContextUtil.getHandler().post(() -> binding.viewRtmpPanel.updateTextStatus(status));
    }


    private void prepareForPublish(String rtmpUrl) {
       RTMPManager.getInstance().startPublish(rtmpUrl);
    }

    @Override
    public void onClickRtmp() {
        Log.e("onClickRtmp", "onClickRtmp: "+RTMPManager.getInstance().isPublishing());
        if (RTMPManager.getInstance().isPublishing()) {
            stopRtmp();
        } else {
            startRtmp();
        }
    }

    public void stopRtmp() {
        if (RTMPManager.getInstance().isPublishing()) {
            RTMPManager.getInstance().stopPublish();
            RTMPManager.getInstance().setPublishEventHandler(null);
            ContextUtil.getHandler().post(() -> binding.viewRtmpPanel.changeVideoUI(false));
        }
    }

    public void startRtmp() {
        if (!binding.viewRtmpPanel.isReadyForLive()) {
            return;
        }
        RTMPManager.getInstance().setPublishEventHandler(this);
        if(DJIAircraftApplication.getInstance().getUavInfoSN() == null){
            ToastUtil.show("没有获取到站点信息");
            return;
        }
        String rtmpUrl = DJIAircraftApplication.getInstance().getUavInfoSN().getData().getUAVPushURL();
        if (!TextUtils.isEmpty(rtmpUrl)) {
            prepareForPublish(rtmpUrl);
        } else {
            ToastUtil.show("没有获取到推流地址");
        }
    }

}
