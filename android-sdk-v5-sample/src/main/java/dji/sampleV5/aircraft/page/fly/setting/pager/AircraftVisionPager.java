package dji.sampleV5.aircraft.page.fly.setting.pager;




import static dji.sampleV5.aircraft.page.fly.setting.AircraftSettingFragment.PAGER_VISION_ADVANCE;

import android.view.LayoutInflater;

import androidx.databinding.DataBindingUtil;

import dji.sampleV5.aircraft.ContextUtil;
import dji.sampleV5.aircraft.R;
import dji.sampleV5.aircraft.databinding.AircaftRadarPagerBinding;
import dji.sampleV5.aircraft.page.fly.setting.AircraftSettingFragment;
import dji.sampleV5.aircraft.page.fly.setting.pager.detail.AircaftRadarSenser;

public class AircraftVisionPager extends BasePager {

    private AircaftRadarSenser aircaftRadarSenser;
    private AircaftRadarPagerBinding dataBinding;

    public AircraftVisionPager(AircraftSettingFragment fragment) {
        super(fragment);
    }

    @Override
    public void initData() {
        isLoading = true;
        tvTitle.setText(ContextUtil.getString(R.string.aircraft_obstacle));
        dataBinding = DataBindingUtil.inflate(LayoutInflater.from(fragment.getActivity()), R.layout.aircaft_radar_pager, null, false);
        dataBinding.rlVisionAdvanceSet.setOnClickListener(v -> fragment.getViewPager().setCurrentItem(PAGER_VISION_ADVANCE, false));

        aircaftRadarSenser = new AircaftRadarSenser(dataBinding);
        flContainer.addView(dataBinding.getRoot());
    }

    @Override
    public void removeListener() {
        if (aircaftRadarSenser != null) {
            aircaftRadarSenser.remove();
        }
    }

    @Override
    public void isConnect(boolean connect) {
        if (isLoading) {
            aircaftRadarSenser.setConnect(connect);
            /*if(connect){
                if (DJIHelper.getInstance().getModel() != null) {
                    if (DJIHelper.getInstance().getModel().getDisplayName().toUpperCase().contains("MAVIC 2")) {
                        dataBinding.auxiliaryLight.setVisibility(View.VISIBLE);
                    }
                }
            }*/
        }
    }

}
