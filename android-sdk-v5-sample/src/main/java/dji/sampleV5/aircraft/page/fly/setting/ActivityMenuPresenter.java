package dji.sampleV5.aircraft.page.fly.setting;

import androidx.databinding.BaseObservable;
import androidx.databinding.Bindable;

import dji.sampleV5.aircraft.BR;


public class ActivityMenuPresenter extends BaseObservable {
    @Bindable
    public int gimbalIndex;
    @Bindable
    public boolean Previous;
    @Bindable
    public boolean isAuto;
    @Bindable
    public boolean isStatusNormal;
    @Bindable
    public Boolean isCheckRemoteA;
    @Bindable
    public Boolean IsClickableOfBattery;
    @Bindable
    public boolean ISHDSelect;
    @Bindable
    public boolean Englishsystem;
    @Bindable
    public boolean Kmsystem;
    @Bindable
    public boolean Msystem;
    @Bindable
    public boolean NoviceMode;
    @Bindable
    public boolean RTK;
    @Bindable
    public boolean RootVision;
    @Bindable
    public boolean RootRc;
    @Bindable
    public boolean RootHD;
    @Bindable
    public boolean RootBattery;
    @Bindable
    public boolean RootGimbal;
    @Bindable
    public boolean RootOther;
    @Bindable
    public boolean HDOSDISOPEN;
    @Bindable
    public boolean bandWidth;
    @Bindable
    public boolean HDMISupportEXT = false;
    @Bindable
    public boolean HDMISupportSecondary = false;
    @Bindable
    public boolean HDMISDIOutTure;
    @Bindable
    public boolean ISHDSelectLeft;
    private AircraftSettingFragment activity;
    @Bindable
    private String PreviousName;
    private String RemoteShow;
   //private HDSignalChannel signalChannel;
    //private MavicSignalChannel mavicSignalChannel;
    private String SeekbarTextView;
    private String FlyTime;
    private String currentEnergy;
    private String FullEnergy;
    private String CyclikTime;
    private String BatteryPressure;
    private String BatteryTemperature;
    private String Batteryserialnumber;
    private String BatteryReleaseTime;
    private String TvSeekbalSpeed;
    private String TvSeekbalSpeed2;
    private String TvSeekbalSpeed3;
    private String TvSeekbalSpeed4;

    public ActivityMenuPresenter(AircraftSettingFragment activity) {
        this.activity = activity;
    }

    public void setEnglishsystem(boolean Englishsystem) {
        this.Englishsystem = Englishsystem;
        notifyPropertyChanged(BR.Englishsystem);
    }

    public void setKmsystem(boolean Kmsystem) {
        this.Kmsystem = Kmsystem;
        notifyPropertyChanged(BR.Kmsystem);
    }

    public void setMsystem(boolean Msystem) {
        this.Msystem = Msystem;
        notifyPropertyChanged(BR.Msystem);
    }

    public void setNoviceMode(boolean NoviceMode) {
        this.NoviceMode = NoviceMode;
        notifyPropertyChanged(BR.NoviceMode);
    }

    public void setRTKMode(boolean RTK) {
        this.RTK = RTK;
        notifyPropertyChanged(BR.RTK);
    }

    public void setRootVision(boolean RootVision) {
        this.RootVision = RootVision;
        notifyPropertyChanged(BR.RootVision);
    }

    public void setRootRc(boolean RootRc) {
        this.RootRc = RootRc;
        notifyPropertyChanged(BR.RootRc);
    }

    public void setRootHD(boolean RootHD) {
        this.RootHD = RootHD;
        notifyPropertyChanged(BR.RootHD);
    }

    public void setRootBattery(boolean RootBattery) {
        this.RootBattery = RootBattery;
        notifyPropertyChanged(BR.RootBattery);
    }

    public void setRootGimbal(boolean RootGimbal) {
        this.RootGimbal = RootGimbal;
        notifyPropertyChanged(BR.RootGimbal);
    }

    public void setRootOther(boolean RootOther) {
        this.RootOther = RootOther;
        notifyPropertyChanged(BR.RootOther);
    }

    public void setISHDSelect(boolean ISHDSelect) {
        this.ISHDSelect = ISHDSelect;
        notifyPropertyChanged(BR.ISHDSelect);
    }

    public void setHDOSDISOPEN(boolean HDOSDISOPEN) {
        this.HDOSDISOPEN = HDOSDISOPEN;
        notifyPropertyChanged(BR.HDOSDISOPEN);
    }

    public void setbandWidth(boolean bandWidth) {
        this.bandWidth = bandWidth;
        notifyPropertyChanged(BR.bandWidth);
    }

    public void setHDMISupportEXT(boolean HDMISupportEXT) {
        this.HDMISupportEXT = HDMISupportEXT;
        notifyPropertyChanged(BR.HDMISupportEXT);
    }

    public void setHDMISupportSecondary(boolean HDMISupportSecondary) {
        this.HDMISupportSecondary = HDMISupportSecondary;
        notifyPropertyChanged(BR.HDMISupportSecondary);
    }

    public void setHDMISDIOutTure(boolean HDMISDIOutTure) {
        this.HDMISDIOutTure = HDMISDIOutTure;
        notifyPropertyChanged(BR.HDMISDIOutTure);
    }

    public void setISHDSelectLeft(boolean ISHDSelectLeft) {
        this.ISHDSelectLeft = ISHDSelectLeft;
        notifyPropertyChanged(BR.ISHDSelectLeft);
    }

    //显示遥控手模式类型
    @Bindable
    public String getRemoteShow() {
        return RemoteShow;
    }

    public void setRemoteShow(String RemoteShow) {
        this.RemoteShow = RemoteShow;
        notifyPropertyChanged(BR.remoteShow);
    }

    public void setIsClickableOfBattery(Boolean IsClickableOfBattery) {
        this.IsClickableOfBattery = IsClickableOfBattery;
        notifyPropertyChanged(BR.IsClickableOfBattery);
    }

    public void isCheckRemoteA(Boolean isCheckRemoteA) {
        this.isCheckRemoteA = isCheckRemoteA;
        notifyPropertyChanged(BR.isCheckRemoteA);
    }

    //遥控手模式点击事件
    public void onClickRemote() {
        activity.getViewPager().setCurrentItem(AircraftSettingFragment.PAGER_REMOTE_FREQUENCY, false);
    }

    //遥控器校准点击事件
    public void onClickRemoteCalibration() {
        activity.getViewPager().setCurrentItem(AircraftSettingFragment.PAGER_REMOTE_CALIBRATE, false);
    }

    //点击前一菜单的事件
    public void onClickPrevious() {
        if (PreviousName != null) {
            switch (PreviousName) {
                case "aircraftPager":
                    activity.getViewPager().setCurrentItem(AircraftSettingFragment.TAB_AIRCRAFT, false);
                    break;
                case "RadarPager":
                    activity.getViewPager().setCurrentItem(AircraftSettingFragment.TAB_VISION, false);
                    break;
                case "RemotePager":
                    activity.getViewPager().setCurrentItem(AircraftSettingFragment.TAB_REMOTE, false);
                    break;
                case "BatteryPager":
                    activity.getViewPager().setCurrentItem(AircraftSettingFragment.TAB_BATTERY, false);
                    break;
                case "GimbalPager":
                    activity.getViewPager().setCurrentItem(AircraftSettingFragment.TAB_GIMBAL, false);
                    break;
                case "RemoteCalibrationPager":
                    activity.getViewPager().setCurrentItem(AircraftSettingFragment.PAGER_REMOTE_FREQUENCY, false);
                    break;
            }
        }
    }

    public void setPreviousName(String PreviousName) {
        this.PreviousName = PreviousName;
        notifyPropertyChanged(BR.PreviousName);
    }

    //是否显示上一菜单按钮
    public void setIsPrevious(boolean Previous) {
        this.Previous = Previous;

        notifyPropertyChanged(BR.Previous);
    }

    //hd选择通道模式 自动/手动 是否是自动
    public void setHdSignalSelectorChannel(boolean isAuto) {
        this.isAuto = isAuto;
        notifyPropertyChanged(BR.isAuto);
    }

    //mavic 选择带宽是10Mhz
    public void onClickBandWithTen() {
        //mavicSignalChannel.setCurrentBandWidth(OcuSyncBandwidth.Bandwidth10MHz);
    }

    //mavic 选择带宽是20Mhz
    public void onClickBandWithTwenty() {
        //mavicSignalChannel.setCurrentBandWidth(OcuSyncBandwidth.Bandwidth20MHz);
    }


    //自动选择信号
    public void onClickHDAuto() {
        //signalChannel.setCurrentChannelmode(ChannelSelectionMode.AUTO);
    }

    //手动选择信道
    public void onClickHDOneSelf() {
        //signalChannel.setCurrentChannelmode(ChannelSelectionMode.MANUAL);
    }

    //自动选择Mavic信道
    public void onClickMavicAuto() {
       // mavicSignalChannel.setCurrentChannelMode(ChannelSelectionMode.AUTO);
    }

    //手动选择Mavic信道
    public void onClickMavicOneSelf() {
        //mavicSignalChannel.setCurrentChannelMode(ChannelSelectionMode.MANUAL);
    }

   /* //当前的信号道选择方式
    public void currentHDSignalChannel(HDSignalChannel signalChannel) {
        this.signalChannel = signalChannel;
    }*/

    //当前Mavic的信号道选择方式
   /* public void currentMavicSignalChannel(MavicSignalChannel mavicSignalChannel) {
        this.mavicSignalChannel = mavicSignalChannel;
    }*/

    //跳转到电池高级设置
    public void onClickBatteryAdvancedSet() {
        activity.getViewPager().setCurrentItem(AircraftSettingFragment.PAGER_BATTERY_ADVANCE, false);
    }

    //电池详细信息
    //当前状态
    public void setBatteryStatus(boolean isStatusNormal) {
        this.isStatusNormal = isStatusNormal;
        notifyPropertyChanged(BR.isStatusNormal);
    }

    //云台高级设置
    public void GimbalSetAdvanced() {
        activity.getViewPager().setCurrentItem(AircraftSettingFragment.PAGER_GIMBAL_ADVANCE, false);
    }

    //云台微调roll
    public void onClickRoll(int gimbalIndex) {
       /* AircraftActivity activity = ((AircraftActivity) ContextUtil.getCurrentActivity());
        activity.getAircraftFragmentManager().removeFragment(AircraftFragmentManager.SETTING);
        activity.showGimbalRollSetDialog(gimbalIndex);*/
    }

    //云台回中/朝下
    public void onClickReset() {
       /* Gimbal gimbal = DJIHelper.getInstance().getGimbal();
        if (gimbal != null) {
            gimbal.reset(null);
        }*/
    }

    //云台高级设置
    //重置云台
    public void onClicksetResetGimbal() {
        //gimbalSetAdvanced.restSpeed();
    }

    /*public void setGimbalSetAdvanced(GimbalSetAdvanced gimbalSetAdvanced) {
        this.gimbalSetAdvanced = gimbalSetAdvanced;
    }*/

    @Bindable
    public String getHDSeekbarTextView() {
        return SeekbarTextView;
    }

    //设置图传码率
    public void setHDSeekbarTextView(String SeekbarTextView) {
        this.SeekbarTextView = SeekbarTextView;
        notifyPropertyChanged(BR.hDSeekbarTextView);
    }

    //飞行时间tv_flyTime
    @Bindable
    public String getFlyTime() {
        return FlyTime;
    }

    public void setFlyTime(String FlyTime) {
        this.FlyTime = FlyTime;
        notifyPropertyChanged(BR.flyTime);
    }

    //当前电量tv_currentEnergy
    @Bindable
    public String getCurrentEnergy() {
        return currentEnergy;
    }

    public void setCurrentEnergy(String currentEnergy) {
        this.currentEnergy = currentEnergy;
        notifyPropertyChanged(BR.currentEnergy);
    }

    //当前电量tv_fullEnergy
    @Bindable
    public String getFullEnergy() {
        return FullEnergy;
    }

    public void setFullEnergy(String FullEnergy) {
        this.FullEnergy = FullEnergy;
        notifyPropertyChanged(BR.fullEnergy);
    }

    //当前循环tv_cyclikTime
    @Bindable
    public String getCyclikTime() {
        return CyclikTime;
    }

    public void setCyclikTime(String CyclikTime) {
        this.CyclikTime = CyclikTime;
        notifyPropertyChanged(BR.cyclikTime);
    }

    //当前电压battery_pressure
    @Bindable
    public String getBatteryPressure() {
        return BatteryPressure;
    }

    public void setBatteryPressure(String BatteryPressure) {
        this.BatteryPressure = BatteryPressure;
        notifyPropertyChanged(BR.batteryPressure);
    }

    //当前电池温度battery_temperature
    @Bindable
    public String getBatteryTemperature() {
        return BatteryTemperature;
    }

    public void setBatteryTemperature(String BatteryTemperature) {
        this.BatteryTemperature = BatteryTemperature;
        notifyPropertyChanged(BR.batteryTemperature);
    }

    //当前电池序列号tv_serialnumber
    @Bindable
    public String getBatteryserialnumber() {
        return Batteryserialnumber;
    }

    public void setBatteryserialnumber(String Batteryserialnumber) {
        this.Batteryserialnumber = Batteryserialnumber;
        notifyPropertyChanged(BR.batteryserialnumber);
    }

    //当前电池tv_release_time
    @Bindable
    public String getBatteryReleaseTime() {
        return BatteryReleaseTime;
    }

    public void setBatteryReleaseTime(String BatteryReleaseTime) {
        this.BatteryReleaseTime = BatteryReleaseTime;
        notifyPropertyChanged(BR.batteryReleaseTime);
    }

    @Bindable
    public String getTvSeekbalSpeed1() {
        return TvSeekbalSpeed;
    }

    //云台速度tv_seekbar_gimbal_speed
    public void setTvSeekbalSpeed1(String TvSeekbalSpeed) {
        this.TvSeekbalSpeed = TvSeekbalSpeed;
        notifyPropertyChanged(BR.tvSeekbalSpeed1);
    }

    @Bindable
    public String getTvSeekbalSpeed2() {
        return TvSeekbalSpeed2;
    }

    //云台速度tv_seekbar_gimbal_speed
    public void setTvSeekbalSpeed2(String TvSeekbalSpeed2) {
        this.TvSeekbalSpeed2 = TvSeekbalSpeed2;
        notifyPropertyChanged(BR.tvSeekbalSpeed2);
    }

    @Bindable
    public String getTvSeekbalSpeed3() {
        return TvSeekbalSpeed3;
    }

    //云台速度tv_seekbar_gimbal_speed
    public void setTvSeekbalSpeed3(String TvSeekbalSpeed3) {
        this.TvSeekbalSpeed3 = TvSeekbalSpeed3;
        notifyPropertyChanged(BR.tvSeekbalSpeed3);
    }

    @Bindable
    public String getTvSeekbalSpeed4() {
        return TvSeekbalSpeed4;
    }

    //云台速度tv_seekbar_gimbal_speed
    public void setTvSeekbalSpeed4(String TvSeekbalSpeed4) {
        this.TvSeekbalSpeed4 = TvSeekbalSpeed4;
        notifyPropertyChanged(BR.tvSeekbalSpeed4);
    }
}
