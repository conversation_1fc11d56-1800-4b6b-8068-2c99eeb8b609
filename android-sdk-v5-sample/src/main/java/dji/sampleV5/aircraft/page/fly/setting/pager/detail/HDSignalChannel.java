package dji.sampleV5.aircraft.page.fly.setting.pager.detail;/*
package dji.sampleV5.aircraft.page.fly.setting.pager.detail;

import static com.skysys.fly.R.string.channel;

import android.util.Log;
import android.view.View;
import android.widget.ListView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.Nullable;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import dji.sampleV5.aircraft.ContextUtil;
import dji.sampleV5.aircraft.DefaultLayoutActivity;
import dji.sampleV5.aircraft.R;
import dji.sampleV5.aircraft.page.fly.setting.ActivityMenuPresenter;
import dji.sampleV5.aircraft.view.adapter.CommonAdapter;
import dji.sampleV5.aircraft.view.adapter.EasyAdapter;
import dji.sdk.keyvalue.value.airlink.ChannelSelectionMode;

import java.util.ArrayList;


public class HDSignalChannel implements View.OnClickListener {
    private static final String TAG = "HDSignalChannel";
    private final DefaultLayoutActivity mActivity;
    private TextView mtvHdManual;
    private RelativeLayout rlHdManual;
    private ListView mListView;

    private EasyAdapter easyAdapter;
    private int currentPosition;
    private MenuHdPagerBinding binding;
    private RecyclerView rvHDSignalView;
    private LinearLayoutManager mLayoutManager;
    private ArrayList<HDChannel> listChannel_long = new ArrayList<>();
    private ArrayList<HDChannel> listChannel_short = new ArrayList<>();
    private ArrayList<Integer> listChannel = new ArrayList<>();

    private AirLink airLink;
    private CommonAdapter<HDChannel> commonAdapter;
    private int ChannelNumber = -1;

    private int currentLength;
    private int spinnerListLength = 0;
    private long lastJudgeTime;
    private boolean isUpdate = true;
    private boolean isUpdate1 = true;
    private HDChannel hdChannel;
    private final ActivityMenuPresenter menuPresenter;
    private boolean flg = true;
    private int lastPosition;
    private KeyListener<ChannelSelectionMode> channel_selection_modeListener;
    private boolean currentLengthFlg = true;
    private int lastLength;

    public HDSignalChannel(MenuHdPagerBinding binding, AircraftActivity mActivity) {
        this.binding = binding;
        this.mActivity = mActivity;
        menuPresenter = binding.getActivityMenuPresenter();
        rvHDSignalView = binding.menuHdSignalHistogram.rvHDSignalView;
        mtvHdManual = binding.menuHdManual.tvHdManual;
        rlHdManual = binding.menuHdManual.rlHdManual;
        rlHdManual.setOnClickListener(this);
        mListView = (ListView) View.inflate(ContextUtil.getApplicationContext(), R.layout.item_listview, null);
    }

    public void setSignalChannel() {
        //获得所有的信号通道
        getAllChannel();
        //获得当前模式
        getCurrentMode();

    }

    //获得所有的信号通道
    public void getAllChannel() {
        listChannel_long.clear();
        listChannel_short.clear();

        airLink = DJIHelper.getInstance().getAirLink();
        if (airLink == null) {
            return;
        }
        for (int i = 0; i <= 33; i++) {
            HDChannel hdChannel = new HDChannel();
            listChannel_long.add(hdChannel);
        }

        for (int i = 0; i <= 8; i++) {
            HDChannel hdChannel = new HDChannel();
            listChannel_short.add(hdChannel);
        }

        getCurrentChannel();
    }

    //获得当前通道
    public void getCurrentChannel() {
        if (airLink.getLightbridgeLink() == null) {
            return;
        }

        airLink.getLightbridgeLink().setChannelInterferenceCallback(channelInterferences -> judgeChannel(channelInterferences));
    }


    private void judgeChannel(ChannelInterference[] channelInterferences) {
        if ((System.currentTimeMillis() - lastJudgeTime) > 500) {
            lastJudgeTime = System.currentTimeMillis();

            airLink.getLightbridgeLink().getChannelNumber(new CommonCallbacks.CompletionCallbackWith<Integer>() {
                @Override
                public void onSuccess(Integer integer) {
                    Log.e(TAG, "getChannelNumber = " + integer);
                    ChannelNumber = integer;
                }

                @Override
                public void onFailure(DJIError djiError) {

                }
            });

            if (channelInterferences.length > 0) {
                currentLength = channelInterferences.length;
                if (currentLengthFlg) {
                    lastLength = currentLength;
                    currentLengthFlg = false;
                }

                if (currentLength != lastLength) {
                    currentLengthFlg = true;
                }

                //选择信号道
                selectChannel(channelInterferences);

                for (int i = 0; i < channelInterferences.length; i++) {
                    final int power = channelInterferences[i].getPower();
                    final int channelValue = channelInterferences[i].getChannel();

                    if (currentLength <= 9) {
                        hdChannel = listChannel_short.get(i);

                    } else if (currentLength > 10) {
                        if (i < 34) {
                            hdChannel = listChannel_long.get(i);
                        }
                    }
                    hdChannel.setPower(power);
                    hdChannel.setChannel(channelValue + "");
                    final String channel = hdChannel.getChannel();

                    */
/*要注意的是channelNumber和channel不是等值的，可能有8个channel，当时当前channelNumber是19*//*

                    if (channel.equals(ChannelNumber + "")) {
                        hdChannel.setIsPower(true);
                    } else {
                        hdChannel.setIsPower(false);
                    }
                    final int finalI = i;
                    ContextUtil.getHandler().post(() -> {
                        if (!currentLengthFlg) {
                            if (commonAdapter != null) {
                                commonAdapter.notifyItemChanged(finalI);
                            } else {
                                //显示数据recycleView
                                showRecycleView();
                            }
                        } else {
                            //显示数据recycleView
                            showRecycleView();
                        }

                    });
                }

            }
        }
    }
    //设置listview里面的模式
    public void setListIntoMode(final int channelNumber) {
        mtvHdManual.setText(ContextUtil.getString(channel) + " " + channelNumber);
        int listChannelFirstId = listChannel.get(0);

        for (int i = 0; i < listChannel.size(); i++) {
            int channelId = listChannel.get(i);
            if (channelNumber == channelId) {
                currentPosition = i;
                mtvHdManual.setText(ContextUtil.getString(channel) + " " + currentPosition);
            } */
/*else if (channelNumber < listChannelFirstId) {
                currentPosition = 0;
                setChannelMode(listChannelFirstId);
            }*//*

        }

        if (easyAdapter == null) {
            easyAdapter = new EasyAdapter(ContextUtil.getApplicationContext(), listChannel) {
                @Override
                public EasyHolder getHolder(int type) {
                    return new EasyHolder() {
                        private TextView tv_text_item;

                        @Override
                        public int getLayout() {
                            return R.layout.one_center_item;
                        }

                        @Override
                        public View createView(int position) {
                            tv_text_item = view.findViewById(R.id.tv_center_item);
                            return view;
                        }

                        @Override
                        public void refreshView(int position, Object item) {

                            if (position == currentPosition) {
                                tv_text_item.setTextColor(ContextUtil.getColor(R.color.colorBright));
                            } else {
                                tv_text_item.setTextColor(ContextUtil.getColor(R.color.white));
                            }
                            int ite = (int) item;
                            tv_text_item.setText(ContextUtil.getString(channel) + " " + ite);
                        }
                    };
                }
            };

            mListView.setAdapter(easyAdapter);
            mListView.setSelection(currentPosition);
            mListView.setVerticalScrollBarEnabled(false);
        } else {
            mListView.setSelection(currentPosition);
            easyAdapter.notifyDataSetChanged();
        }

        mListView.setOnItemClickListener((parent, view, position, id) -> {
            currentPosition = position;
            int integer = listChannel.get(position);
            //设置摇杆模式
            setChannelMode(integer);
            easyAdapter.notifyDataSetChanged();
        });
    }

    private void setChannelMode(final int position) {
        if (airLink.getLightbridgeLink() == null) {
            return;
        }

        airLink.getLightbridgeLink().setChannelNumber(position, djiError -> {
            if (djiError == null) {
                ChannelNumber = position;
                ContextUtil.getHandler().post(() -> {
                    mtvHdManual.setText(ContextUtil.getString(channel) + " " + position);
                    ((AircraftActivity) ContextUtil.getCurrentActivity()).dismissPopupWindow();
                });
            } else {

            }
        });
    }

    private void initdate(ArrayList<HDChannel> listDate, int item_line_view) {
        //创建RecyclerView
        mLayoutManager = new LinearLayoutManager(ContextUtil.getApplicationContext());
        mLayoutManager.setOrientation(LinearLayoutManager.HORIZONTAL);

        ContextUtil.getHandler().post(() -> rvHDSignalView.setLayoutManager(mLayoutManager));

        commonAdapter = new CommonAdapter<HDChannel>(ContextUtil.getApplicationContext(), item_line_view, listDate) {

            @Override
            public void convert(final ViewHolder holder, final HDChannel user, final int position) {
                ContextUtil.getHandler().post(() -> {
                    holder.setText(R.id.item_lineView_tv, user.getChannel(), user.IsPower());
                    holder.setPower(R.id.item_lineView, user.getPower(), user.IsPower());
                });
            }
        };

        ContextUtil.getHandler().post(() -> {
            rvHDSignalView.setAdapter(commonAdapter);
            rvHDSignalView.setItemAnimator(null);

        });
        scrollListener();
    }

    private void selectChannel(ChannelInterference[] channelInterferences) {
        if (currentLength != spinnerListLength) {
            if (channelInterferences.length > 10) {
                menuPresenter.setISHDSelect(true);
                menuPresenter.setISHDSelectLeft(true);
            } else {
                menuPresenter.setISHDSelect(false);
            }
            spinnerListLength = currentLength;

            listChannel.clear();
            for (ChannelInterference channelInterference : channelInterferences) {
                //选择信道
                listChannel.add(channelInterference.getChannel());
            }

            ContextUtil.getHandler().post(() -> setListIntoMode(ChannelNumber));

        }
    }

    private void showRecycleView() {
        if (currentLength <= 9) {
            if (isUpdate) {
                isUpdate = false;
                isUpdate1 = true;
                commonAdapter = null;
                initdate(listChannel_short, R.layout.item_line_view);
            }
        }
        if (currentLength > 10) {
            if (isUpdate1) {
                isUpdate1 = false;
                isUpdate = true;
                commonAdapter = null;
                //显示数据recycleView
                initdate(listChannel_long, R.layout.item_line_view_type2);
            }
        }
    }

    public void setCurrentChannelmode(ChannelSelectionMode Mode) {
        KeyManager.getInstance().setValue(AirLinkKey.createLightbridgeLinkKey(AirLinkKey.CHANNEL_SELECTION_MODE), Mode, new YNListener0() {
            @Override
            public void onSuccess() {

            }

            @Override
            public void onException(Throwable e) {

            }
        });
    }


    //获得当前模式
    private void getCurrentMode() {
        //自动
        //手动
        channel_selection_modeListener = new KeyListener<ChannelSelectionMode>() {
            @Override
            protected void onValueChanged(@Nullable ChannelSelectionMode old, @Nullable ChannelSelectionMode channelSelectionMode) {
                if (channelSelectionMode != null) {
                    final int value = channelSelectionMode.value();
                    ContextUtil.getHandler().post(() -> {
                        switch (value) {
                            case 0:
                                //自动
                                binding.getActivityMenuPresenter().setHdSignalSelectorChannel(true);
                                break;
                            case 1:
                                //手动
                                binding.getActivityMenuPresenter().setHdSignalSelectorChannel(false);

                                break;
                        }
                    });
                }
            }
        };
        KeyManager.getInstance().addListenerWithInitialValue(AirLinkKey.createLightbridgeLinkKey(AirLinkKey.CHANNEL_SELECTION_MODE), channel_selection_modeListener);
    }

    private void scrollListener() {
        rvHDSignalView.setOnScrollListener(new RecyclerView.OnScrollListener() {
            //用来标记是否正在向最后一个滑动
            boolean isSlidingToLast = false;

            @Override
            public void onScrollStateChanged(RecyclerView recyclerView, int newState) {
                super.onScrollStateChanged(recyclerView, newState);
            }

            @Override
            public void onScrolled(RecyclerView recyclerView, int dx, int dy) {
                super.onScrolled(recyclerView, dx, dy);
                LinearLayoutManager manager = (LinearLayoutManager) recyclerView.getLayoutManager();
                //dx用来判断横向滑动方向，dy用来判断纵向滑动方向
                if (flg) {
                    lastPosition = manager.findLastCompletelyVisibleItemPosition();
                    flg = false;
                }
                if (dx > 0) {

                    rvHDSignalView.scrollToPosition(lastPosition);
                    menuPresenter.setISHDSelectLeft(false);
                    //大于0表示正在向右滚动
                    isSlidingToLast = true;
                } else if (dx < 0) {
                    menuPresenter.setISHDSelectLeft(true);
                    rvHDSignalView.scrollToPosition(0);
                    //小于等于0表示停止或向左滚动
                    isSlidingToLast = false;
                }
            }
        });
    }

    @Override
    public void onClick(View v) {
        // 指定PopupWindow显示到et_number的下方
        View anchor = mtvHdManual;
        mActivity.showPopup(anchor, mListView, listChannel_long.size());
    }

    public void remove() {
        if (airLink != null && airLink.getLightbridgeLink() != null) {
            airLink.getLightbridgeLink().setChannelInterferenceCallback(null);
        }
        KeyManager.getInstance().removeListener(channel_selection_modeListener);
    }

    public void connect(Boolean connect) {
        if (connect) {
            rvHDSignalView.setVisibility(View.VISIBLE);
            rlHdManual.setVisibility(View.VISIBLE);
        } else {
            rvHDSignalView.setVisibility(View.GONE);
            rlHdManual.setVisibility(View.GONE);
        }
    }
}



*/
