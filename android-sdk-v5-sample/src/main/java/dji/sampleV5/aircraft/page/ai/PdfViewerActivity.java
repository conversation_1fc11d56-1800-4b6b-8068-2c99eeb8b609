package dji.sampleV5.aircraft.page.ai;

import android.app.Activity;
import android.content.Intent;
import android.net.Uri;
import android.os.Bundle;
import android.os.Environment;
import android.util.Log;
import android.view.View;

import androidx.annotation.Nullable;
import androidx.appcompat.app.AppCompatActivity;
import androidx.databinding.DataBindingUtil;
import androidx.fragment.app.Fragment;

import com.gyf.immersionbar.BarHide;
import com.gyf.immersionbar.ImmersionBar;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.URL;

import dji.sampleV5.aircraft.R;
import dji.sampleV5.aircraft.databinding.ActivityPdfBinding;
import dji.sampleV5.aircraft.mvvm.net.response.DefectReportInfoBean;
import dji.sampleV5.aircraft.mvvm.net.response.TaskReportInfoBean;
import dji.sampleV5.aircraft.net.DownloadUtil;
import dji.sampleV5.aircraft.util.ToastUtil;
import dji.v5.utils.common.LogUtils;
import me.jessyan.autosize.internal.CancelAdapt;
import okhttp3.Call;

/**
 * 用来显示 pdf
 */
public class PdfViewerActivity extends AppCompatActivity implements CancelAdapt {

    private static final String TASK_NAME = "taskName";
    private static final String PDF_URI = "pdfUri";
    private final String TAG = LogUtils.getTag(this);

    /**
     * 界面渲染 layout.xml
     */
    private ActivityPdfBinding binding;
    // 用于保存下载任务的Call对象
    private Call downloadCall;
    /**
     * 下载
     *
     * @param target  本地文件
     * @param fileUrl 文件路径
     */
    private void downloadFile(File target, String fileUrl) {
        try {
            URL url = new URL(fileUrl);
            InputStream inputStream = url.openStream();
            FileOutputStream fileOutputStream = new FileOutputStream(target);
            int length;
            byte[] buffer = new byte[1024 * 512];
            while ((length = inputStream.read(buffer)) > -1) {
                Log.i(TAG, String.format("downloadFile size:%d", length));
                fileOutputStream.write(buffer, 0, length);
            }
            fileOutputStream.close();
            inputStream.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
//        getWindow().setStatusBarColor(getResources().getColor(android.R.color.transparent));
        super.onCreate(savedInstanceState);

        // ui binding
        binding = DataBindingUtil.setContentView(this, R.layout.activity_pdf);

        Intent intent = getIntent();
        String pdfUrl = intent.getStringExtra(PDF_URI);
        String taskName = intent.getStringExtra(TASK_NAME);
        Log.e(TAG, String.format("onCreate task:%s url:%s", taskName, pdfUrl));
        // 后台下载文件
       new Thread(() -> {
           //downloadFile(file, pdfUrl);
           DownloadUtil.get(this).download(pdfUrl, taskName + ".pdf", new DownloadUtil.OnDownloadListener() {
               @Override
               public void onDownloadSuccess(Uri uri) {
                   //成功
                   Log.e("注意", "下载成功");
                   runOnUiThread(() -> {
                       ToastUtil.show("文件已下载到Download/Skysys/download/ai_report文件夹下");
                       binding.downPercent.setVisibility(View.INVISIBLE);
                       binding.downloadProgressBar.setVisibility(View.INVISIBLE);
                        binding.titleDownload.setVisibility(View.INVISIBLE);
                        binding.pdfView
                                .fromUri(uri)
                                .load();
                    });
                }

                @Override
                public void onDownloading(int progress) {
                    //进度
                    runOnUiThread(() -> {
                        binding.downloadProgressBar.setProgress(progress);
                        binding.downPercent.setText(progress + "%");
                    });
                    Log.e("注意", progress + "%");
                }

               @Override
               public void onDownloadFailed(String error) {
                   //失败
                   ToastUtil.show(error);
                   Log.e("注意", error);
               }
           });

       }).start();
    }

    @Override
    protected void onResume() {
        super.onResume();
        ImmersionBar.with(this).hideBar(BarHide.FLAG_HIDE_STATUS_BAR).navigationBarColor(R.color.transparent).init();
    }

    @Override
    public void onBackPressed() {
        DownloadUtil.get(this).cancelDownload();
        finish();
    }

    /**
     * 显示 pdf
     *
     * @param activity 发起请求的 activity
     */
    public static void showPdf(Activity activity, ReportItemVO item) {
        Intent intent = new Intent(activity, PdfViewerActivity.class)
                .putExtra(PDF_URI, item.pdfUrl)
                .putExtra(TASK_NAME, item.taskName);
        activity.startActivity(intent);
    }

    public static void showPdf(Activity activity, TaskReportInfoBean reportInfo) {
        Intent intent = new Intent(activity, PdfViewerActivity.class)
                .putExtra(PDF_URI, reportInfo.getPdfDownloadUrl())
                .putExtra(TASK_NAME, reportInfo.getTaskName());
        activity.startActivity(intent);
    }

    public static void showPdf(Activity activity, DefectReportInfoBean reportInfo) {
        Intent intent = new Intent(activity, PdfViewerActivity.class)
                .putExtra(PDF_URI, reportInfo.getLocationReportDownloadUrl())
                .putExtra(TASK_NAME, reportInfo.getReportName());
        activity.startActivity(intent);
    }
}
