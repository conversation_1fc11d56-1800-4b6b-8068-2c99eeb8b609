package dji.sampleV5.aircraft.page.fly.setting.pager.detail;

import android.view.LayoutInflater;

import androidx.databinding.DataBindingUtil;

import dji.sampleV5.aircraft.ContextUtil;
import dji.sampleV5.aircraft.R;

import dji.sampleV5.aircraft.databinding.MenuGimbalAdvancedSetBinding;
import dji.sampleV5.aircraft.page.fly.setting.AircraftSettingFragment;
import dji.sampleV5.aircraft.page.fly.setting.pager.BasePager;


public class GimbalAdvancedPager extends BasePager {

    //private GimbalSetAdvanced gimbalSetAdvanced;

    public GimbalAdvancedPager(AircraftSettingFragment activity) {
        super(activity);
    }

    @Override
    public void initData() {
        isLoading=true;
        activityMenuPresenter.setIsPrevious(true);
        tvTitle.setText(ContextUtil.getString(R.string.advanced_setting));
        activityMenuPresenter.setPreviousName("GimbalPager");
        MenuGimbalAdvancedSetBinding Binding = DataBindingUtil.inflate(LayoutInflater.from(fragment.getActivity()), R.layout.menu_gimbal_advanced_set, null, false);
        Binding.setActivityMenuPresenter(activityMenuPresenter);
        /*gimbalSetAdvanced = new GimbalSetAdvanced(Binding);
        activityMenuPresenter.setGimbalSetAdvanced(gimbalSetAdvanced);
        gimbalSetAdvanced.setGimbalSetAdvanced();*/
        //添加到帧布局
        flContainer.addView(Binding.getRoot());
    }

    @Override
    public void removeListener() {
        /*if (gimbalSetAdvanced!=null){

            gimbalSetAdvanced.remove();
        }*/
    }

    @Override
    public void isConnect(boolean connect) {
    }

}
