package dji.sampleV5.aircraft.page.fly.setting.pager.detail;/*
package dji.sampleV5.aircraft.page.fly.setting.pager.detail;

import static dji.common.airlink.LightbridgeFrequencyBand.FREQUENCY_BAND_2_DOT_4_GHZ;
import static dji.common.airlink.LightbridgeFrequencyBand.FREQUENCY_BAND_5_DOT_7_GHZ;
import static dji.common.airlink.LightbridgeFrequencyBand.FREQUENCY_BAND_5_DOT_8_GHZ;

import android.view.View;
import android.widget.ListView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.Nullable;

import com.skysys.fly.R;
import com.skysys.fly.common.ContextUtil;
import com.skysys.fly.common.drone.DJIHelper;
import com.skysys.fly.common.drone.key.AirLinkKey;
import com.skysys.fly.common.drone.key.KeyListener;
import com.skysys.fly.common.drone.key.KeyManager;
import com.skysys.fly.databinding.MenuHdPagerBinding;
import com.skysys.fly.page.fly.AircraftActivity;
import com.skysys.fly.view.adapter.EasyAdapter;
import com.skysys.fly.view.adapter.EasyHolder;

import java.util.ArrayList;

import dji.common.airlink.LightbridgeFrequencyBand;
import dji.common.error.DJIError;
import dji.common.util.CommonCallbacks;
import dji.sdk.airlink.AirLink;


public class HDFrequencySet implements View.OnClickListener {

    private static final String G_24 = "2.4G";
    private static final String G_57 = "5.7G";
    private static final String G_58 = "5.8G";
    private final AircraftActivity mActivity;
    private  RelativeLayout rlHdFrequency;
    private  TextView tvHdFrequency;
    private final ArrayList<String> listFrequency =new ArrayList<>();
    private final RelativeLayout rlHdFrequencyPop;
    private AirLink airLink;
    private String frequencyName;
    private int currentPosition;
    private EasyAdapter easyAdapter;
    private ListView mListView;
    private KeyListener<LightbridgeFrequencyBand[]> lightBridgeListener;

    public HDFrequencySet(MenuHdPagerBinding binding, AircraftActivity mActivity, HDSignalChannel signalChannel) {
        rlHdFrequency = binding.menuHdFrequency.rlHdFrequency;
        tvHdFrequency = binding.menuHdFrequency.tvHdPrequency;
        rlHdFrequencyPop = binding.menuHdFrequency.rlHdFrequencyPop;
        this.mActivity=mActivity;
        //获得频段范围
        getFrequencyRange();
        rlHdFrequencyPop.setOnClickListener(this);
    }
    private void getFrequencyRange() {
        airLink = DJIHelper.getInstance().getAirLink();
        if (airLink ==null){
            return;
        }

        mListView = (ListView) View.inflate(ContextUtil.getApplicationContext(), R.layout.item_listview, null);
        lightBridgeListener = new KeyListener<LightbridgeFrequencyBand[]>() {
            @Override
            protected void onValueChanged(@Nullable LightbridgeFrequencyBand[] old, @Nullable final LightbridgeFrequencyBand[] lightbridgeFrequencyBands) {

                assert lightbridgeFrequencyBands != null;
                ContextUtil.getHandler().post(() -> {
                    if (lightbridgeFrequencyBands.length <= 1) {
                        rlHdFrequency.setVisibility(View.GONE);
                    } else {
                        rlHdFrequency.setVisibility(View.VISIBLE);
                    }
                });
                listFrequency.clear();
                for (LightbridgeFrequencyBand lightbridgeFrequencyBand : lightbridgeFrequencyBands) {
                    int value = lightbridgeFrequencyBand.value();
                    switch (value) {
                        case 0:
                            listFrequency.add(G_24);
                            break;
                        case 1:
                            listFrequency.add(G_57);
                            break;
                        case 2:
                            listFrequency.add(G_58);
                            break;
                    }

                }
                getcurrentFrequency();
            }
        };
        KeyManager.getInstance().addListenerWithInitialValue(AirLinkKey.createLightbridgeLinkKey(AirLinkKey.SUPPORTED_FREQUENCY_BANDS), lightBridgeListener);

    }

    private void getcurrentFrequency() {
        if (airLink.getLightbridgeLink() == null) {
            return;
        }
        airLink.getLightbridgeLink().getFrequencyBand(new CommonCallbacks.CompletionCallbackWith<LightbridgeFrequencyBand>() {
            @Override
            public void onSuccess(LightbridgeFrequencyBand lightbridgeFrequencyBand) {
                int value = lightbridgeFrequencyBand.value();
                switch (value) {
                    case 0:
                        frequencyName=G_24;
                        break;
                    case 1:
                        frequencyName=G_57;
                        break;
                    case 2:
                        frequencyName=G_58;
                        break;
                }

                for (int i = 0; i < listFrequency.size() ; i++) {
                    String s = listFrequency.get(i);
                    if (s.equals(frequencyName)){
                        currentPosition=i;
                    }
                }
                ContextUtil.getHandler().post(() -> tvHdFrequency.setText(frequencyName));

                setListIntoMode(currentPosition);
            }

            @Override
            public void onFailure(DJIError djiError) {

            }
        });
    }

    private void setListIntoMode(int integer) {
        easyAdapter = new EasyAdapter(ContextUtil.getApplicationContext(), listFrequency) {
            @Override
            public EasyHolder getHolder(int type) {
                return new EasyHolder() {

                    private TextView tv_text_item;

                    @Override
                    public int getLayout() {
                        return R.layout.one_center_item;
                    }

                    @Override
                    public View createView(int position) {
                        tv_text_item = view.findViewById(R.id.tv_center_item);
                        return view;
                    }

                    @Override
                    public void refreshView(int position, Object item) {
                        if (position==currentPosition){
                            tv_text_item.setTextColor(ContextUtil.getColor(R.color.colorBright));
                        }else {
                            tv_text_item.setTextColor(ContextUtil.getColor(R.color.white));
                        }
                        String ite = (String) item;
                        tv_text_item.setText(ite);
                    }
                };
            }
        };

        mListView.setAdapter(easyAdapter);
        mListView.setSelection(integer);
        mListView.setVerticalScrollBarEnabled(false);

        mListView.setOnItemClickListener((parent, view, position, id) -> {
            currentPosition=position;
            String s = listFrequency.get(position);
            switch (s) {
                case G_24:
                    setcurrentFrequency(FREQUENCY_BAND_2_DOT_4_GHZ,G_24);
                    break;
                case G_57:
                    setcurrentFrequency(FREQUENCY_BAND_5_DOT_7_GHZ, G_57);
                    break;
                case G_58:
                    setcurrentFrequency(FREQUENCY_BAND_5_DOT_8_GHZ, G_58);
                    break;
            }
        });
    }

    private void setcurrentFrequency(LightbridgeFrequencyBand frequencyBand, final String g24) {
        if (airLink.getLightbridgeLink() == null) {
            return;
        }
        airLink.getLightbridgeLink().setFrequencyBand(frequencyBand, djiError -> {
            if (djiError==null){
                ContextUtil.getHandler().post(() -> {
                    easyAdapter.notifyDataSetChanged();
                    tvHdFrequency.setText(g24);
                    ((AircraftActivity) ContextUtil.getCurrentActivity()).dismissPopupWindow();
                });

            }
        });
    }

    public void remove() {
   KeyManager.getInstance().removeListener(lightBridgeListener);
    }

    @Override
    public void onClick(View v) {
        View anchor= tvHdFrequency;
        mActivity.showPopup(anchor,mListView, listFrequency.size());
    }

    public void setconnect(Boolean connect) {
        if (connect){
            rlHdFrequencyPop.setClickable(true);
        }else {
            rlHdFrequencyPop.setClickable(false);

        }
    }


}
*/
