package dji.sampleV5.aircraft.page.fly.setting.pager.detail;/*
package dji.sampleV5.aircraft.page.fly.setting.pager.detail;

import android.util.Log;
import android.view.View;
import android.widget.TextView;

import androidx.annotation.Nullable;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.skysys.fly.R;
import com.skysys.fly.common.ContextUtil;
import com.skysys.fly.common.drone.DJIHelper;
import com.skysys.fly.common.drone.key.AirLinkKey;
import com.skysys.fly.common.drone.key.KeyListener;
import com.skysys.fly.common.drone.key.KeyManager;
import com.skysys.fly.common.listener.YNListener0;
import com.skysys.fly.databinding.MenuHdMavicPagerBinding;
import com.skysys.fly.page.fly.AircraftActivity;
import com.skysys.fly.page.fly.setting.ActivityMenuPresenter;
import com.skysys.fly.view.adapter.CommonAdapter;
import com.skysys.fly.view.adapter.ViewHolder;

import java.util.ArrayList;

import dji.common.airlink.ChannelSelectionMode;
import dji.common.airlink.OcuSyncBandwidth;
import dji.common.error.DJIError;
import dji.common.util.CommonCallbacks;
import dji.sdk.airlink.AirLink;

public class MavicSignalChannel implements View.OnClickListener {
    private MenuHdMavicPagerBinding binding;
//    private AircraftActivity mActivity;
    private ActivityMenuPresenter menuPresenter;
    private KeyListener<ChannelSelectionMode> channel_selection_modeListener;
    private KeyListener<OcuSyncBandwidth> OcuBandWidthListener;
    //    private KeyListener<Boolean> imageTransModeListener;
//    private EasyAdapter easyAdapter;
//    private int currentPosition;
    private int currentLength;
    private boolean currentLengthFlg = true;
    private int lastLength;
    private CommonAdapter<HDChannel> commonAdapter;
    private RecyclerView rvHDSignalView;
    private LinearLayoutManager mLayoutManager;
    private HDChannel hdChannel;
    //    private ListView mListView;
//    private TextView tvHdMode;
    private TextView tvDownBandWidth;
    private ArrayList<HDChannel> listDate = new ArrayList<>();
    private ArrayList<HDChannel> listDate_short = new ArrayList<>();
    private boolean isUpdate = true;
    private boolean isUpdateShort = true;
    private boolean flg = true;
    private int lastPosition;

    //    private RelativeLayout rlHdModePop;
    private AirLink airLink;
//    private ArrayList<String> listMode = new ArrayList() {{
//        add("普通模式");
//        add("高清模式");
//    }};

    public MavicSignalChannel(MenuHdMavicPagerBinding binding, AircraftActivity mActivity) {
        this.binding = binding;
//        this.mActivity = mActivity;
        menuPresenter = binding.getActivityMenuPresenter();

//        mListView = (ListView) View.inflate(ContextUtil.getApplicationContext(), R.layout.item_listview, null);
        tvDownBandWidth = binding.menuMavicBandwidth.tvBandwith;
//        tvHdMode = binding.menuMavicMode.tvHdMode;
//        rlHdModePop = binding.menuMavicMode.rlHdModePop;
        rvHDSignalView = binding.menuHdSignalHistogram.rvHDSignalView;
//        rlHdModePop.setOnClickListener(this);

        airLink = DJIHelper.getInstance().getAirLink();
        getAllChannel();
        getCurrentMode();
        getDownLinkBandWidth();
//        setListIntoMode(0);
//        getImageTransMode();
    }

    //获得所有的信号通道
    public void getAllChannel() {
        listDate.clear();
        listDate_short.clear();

        if (airLink != null) {
            for (int i = 0; i <= 33; i++) {
                HDChannel hdChannel = new HDChannel();
                listDate.add(hdChannel);
            }

            for (int i = 0; i <= 8; i++) {
                HDChannel hdChannel = new HDChannel();
                listDate_short.add(hdChannel);
            }

            getCurrentChannel();
        }
    }

    */
/*获取当前通道*//*

    private void getCurrentChannel() {
        if (airLink != null && airLink.getOcuSyncLink() != null) {
            airLink.getOcuSyncLink().setChannelInterferenceCallback(frequencyInterferences -> {
                currentLength = frequencyInterferences.length;
                if (currentLengthFlg) {
                    lastLength = currentLength;
                    currentLengthFlg = false;
                }

                if (currentLength != lastLength) {
                    currentLengthFlg = true;
                }

                //Log.e("getCurrentChannel", "currentLength = " + currentLength);
                for (int i = 0; i < frequencyInterferences.length; i++) {
                    int power = (int) frequencyInterferences[i].rssi;
                    //int channelValue = (int) (frequencyInterferences[i].frequencyFrom + frequencyInterferences[i].frequencyTo) / 2;

                    if (currentLength <= 9 && currentLength > 0) {
                        hdChannel = listDate_short.get(i);

                    } else if (currentLength > 10) {
                        if (i < 34) {
                            hdChannel = listDate.get(i);
                        }
                    }
                    hdChannel.setPower(power);
                    hdChannel.setChannel(i + "");

                    final int finalI = i;
                    ContextUtil.getHandler().post(() -> {
                        if (!currentLengthFlg) {
                            if (commonAdapter != null) {
                                commonAdapter.notifyItemChanged(finalI);
                            } else {
                                //显示数据recycleView
                                showRecycleView();
                            }
                        } else {
                            //显示数据recycleView
                            showRecycleView();
                        }

                    });

                }
            });

            airLink.getOcuSyncLink().getChannelNumber(new CommonCallbacks.CompletionCallbackWith<Integer>() {
                @Override
                public void onSuccess(Integer integer) {
                    Log.e("getChannelNumber", "onSuccess: 当前channel = " + integer);
                }

                @Override
                public void onFailure(DJIError djiError) {

                }
            });
        }
    }

    private void showRecycleView() {
        if (currentLength <= 9 && currentLength > 0) {
            if (isUpdate) {
                isUpdate = false;
                isUpdateShort = true;
                commonAdapter = null;
                initdate(listDate_short, R.layout.item_line_view);
            }
        }
        if (currentLength > 10) {
            if (isUpdateShort) {
                isUpdateShort = false;
                isUpdate = true;
                commonAdapter = null;
                //显示数据recycleView
                initdate(listDate, R.layout.item_line_view_type2);
            }
        }
    }

    private void initdate(ArrayList<HDChannel> listDate, int item_line_view) {
        //创建RecyclerView
        mLayoutManager = new LinearLayoutManager(ContextUtil.getApplicationContext());
        mLayoutManager.setOrientation(LinearLayoutManager.HORIZONTAL);

        ContextUtil.getHandler().post(() -> rvHDSignalView.setLayoutManager(mLayoutManager));

        commonAdapter = new CommonAdapter<HDChannel>(ContextUtil.getApplicationContext(), item_line_view, listDate) {

            @Override
            public void convert(final ViewHolder holder, final HDChannel user, final int position) {
                ContextUtil.getHandler().post(() -> {
                    holder.setText(R.id.item_lineView_tv, user.getChannel(), user.IsPower());
                    holder.setPower(R.id.item_lineView, user.getPower(), user.IsPower());
                });
            }
        };

        ContextUtil.getHandler().post(() -> {
            rvHDSignalView.setAdapter(commonAdapter);
            rvHDSignalView.setItemAnimator(null);

        });
        scrollListener();
    }

    private void scrollListener() {
        rvHDSignalView.setOnScrollListener(new RecyclerView.OnScrollListener() {
            //用来标记是否正在向最后一个滑动
            boolean isSlidingToLast = false;

            @Override
            public void onScrollStateChanged(RecyclerView recyclerView, int newState) {
                super.onScrollStateChanged(recyclerView, newState);
            }

            @Override
            public void onScrolled(RecyclerView recyclerView, int dx, int dy) {
                super.onScrolled(recyclerView, dx, dy);
                LinearLayoutManager manager = (LinearLayoutManager) recyclerView.getLayoutManager();
                //dx用来判断横向滑动方向，dy用来判断纵向滑动方向
                if (flg) {
                    lastPosition = manager.findLastCompletelyVisibleItemPosition();
                    flg = false;
                }
                if (dx > 0) {
                    rvHDSignalView.scrollToPosition(lastPosition);
                    menuPresenter.setISHDSelectLeft(false);
                    //大于0表示正在向右滚动
                    isSlidingToLast = true;
                } else if (dx < 0) {
                    menuPresenter.setISHDSelectLeft(true);
                    rvHDSignalView.scrollToPosition(0);
                    //小于等于0表示停止或向左滚动
                    isSlidingToLast = false;
                }
            }
        });
    }

    //获得当前信道模式 手动或者自动
    private void getCurrentMode() {
        channel_selection_modeListener = new KeyListener<ChannelSelectionMode>() {
            @Override
            protected void onValueChanged(@Nullable ChannelSelectionMode old, @Nullable ChannelSelectionMode channelSelectionMode) {
                if (channelSelectionMode != null) {
                    final int value = channelSelectionMode.value();
                    ContextUtil.getHandler().post(() -> {
                        switch (value) {
                            case 0:
                                //自动
                                binding.getActivityMenuPresenter().setHdSignalSelectorChannel(true);
                                break;
                            case 1:
                                //手动
                                binding.getActivityMenuPresenter().setHdSignalSelectorChannel(false);
                                break;
                        }
                    });
                }
            }
        };
        KeyManager.getInstance().addListenerWithInitialValue(AirLinkKey.createOcuSyncLinkKey(AirLinkKey.CHANNEL_SELECTION_MODE), channel_selection_modeListener);
    }

    */
/*获取图传模式*//*

//    private void getImageTransMode() {
//        imageTransModeListener = new KeyListener<Boolean>() {
//            @Override
//            protected void onValueChanged(@Nullable Boolean old, @Nullable final Boolean now) {
//
//                Log.e("---", "onValueChanged:"+now);
//                if (now != null) {
//                    Log.e("---", "getImageTransMode:"+now);
//                    ContextUtil.getHandler().post(() -> {
//                        if (now) {
//                            tvHdMode.setText(listMode.get(1));
//                        } else {
//                            tvHdMode.setText(listMode.get(0));
//                        }
//                    });
//
//                }
//            }
//        };
//        KeyManager.getInstance().addListenerWithInitialValue(CameraKey.create(CameraKey.HD_LIVE_VIEW_ENABLED), imageTransModeListener);
//    }


    */
/*获取下行带宽*//*

    private void getDownLinkBandWidth() {
        OcuBandWidthListener = new KeyListener<OcuSyncBandwidth>() {
            @Override
            protected void onValueChanged(@Nullable OcuSyncBandwidth old, @Nullable final OcuSyncBandwidth now) {
                ContextUtil.getHandler().post(() -> {
                    switch (now.value()) {
                        case 0:
                            //20Mhz
                            tvDownBandWidth.setText(ContextUtil.getString(R.string.signal_bandwidth_20));
                            binding.menuMavicBandwidth.tvTenSelect.setBackground(ContextUtil.getDrawable(R.drawable.bg_check_normal));
                            binding.menuMavicBandwidth.tvTwentySelect.setBackground(ContextUtil.getDrawable(R.drawable.bg_check));
                            break;
                        case 1:
                            //10Mhz
                            tvDownBandWidth.setText(ContextUtil.getString(R.string.signal_bandwidth_10));
                            binding.menuMavicBandwidth.tvTenSelect.setBackground(ContextUtil.getDrawable(R.drawable.bg_check));
                            binding.menuMavicBandwidth.tvTwentySelect.setBackground(ContextUtil.getDrawable(R.drawable.bg_check_normal));
                            break;
                    }
                });
            }
        };
        KeyManager.getInstance().addListenerWithInitialValue(AirLinkKey.createOcuSyncLinkKey(AirLinkKey.BANDWIDTH), OcuBandWidthListener);
    }

    */
/*图传模式的list*//*

//    private void setListIntoMode(int integer) {
//        easyAdapter = new EasyAdapter(ContextUtil.getApplicationContext(), listMode) {
//            @Override
//            public EasyHolder getHolder(int type) {
//                return new EasyHolder() {
//                    private TextView tv_text_item;
//
//                    @Override
//                    public int getLayout() {
//                        return R.layout.one_center_item;
//                    }
//
//                    @Override
//                    public View createView(int position) {
//                        tv_text_item = view.findViewById(R.id.tv_center_item);
//                        return view;
//                    }
//
//                    @Override
//                    public void refreshView(int position, Object item) {
//                        if (position == currentPosition) {
//                            tv_text_item.setTextColor(ContextUtil.getColor(R.color.colorBright));
//                        } else {
//                            tv_text_item.setTextColor(ContextUtil.getColor(R.color.white));
//                        }
//                        String ite = (String) item;
//                        tv_text_item.setText(ite);
//                    }
//                };
//            }
//        };
//
//        mListView.setAdapter(easyAdapter);
//        mListView.setSelection(integer);
//        mListView.setVerticalScrollBarEnabled(false);
//        mListView.setOnItemClickListener((parent, view, position, id) -> {
//            currentPosition = position;
//            setImageTransMode(position == 0 ? false : true);
//        });
//    }


    */
/*设置当前信道模式*//*

    public void setCurrentChannelMode(ChannelSelectionMode Mode) {
        KeyManager.getInstance().setValue(AirLinkKey.createOcuSyncLinkKey(AirLinkKey.CHANNEL_SELECTION_MODE), Mode, new YNListener0() {
            @Override
            public void onSuccess() {

            }

            @Override
            public void onException(Throwable e) {

            }
        });
    }

    */
/*设置下行带宽*//*

    public void setCurrentBandWidth(OcuSyncBandwidth ocuSyncBandwidth) {
        KeyManager.getInstance().setValue(AirLinkKey.createOcuSyncLinkKey(AirLinkKey.BANDWIDTH), ocuSyncBandwidth, new YNListener0() {
            @Override
            public void onSuccess() {

            }

            @Override
            public void onException(Throwable e) {

            }
        });
    }

    */
/*设置图传模式*//*

//    public void setImageTransMode(boolean isHD) {
//        KeyManager.getInstance().setValue(CameraKey.create(CameraKey.HD_LIVE_VIEW_ENABLED), isHD, new YNListener0() {
//            @Override
//            public void onSuccess() {
//                ContextUtil.getHandler().post(() -> {
//                    easyAdapter.notifyDataSetChanged();
//                    mActivity.dismissPopupWindow();
//                });
//            }
//
//            @Override
//            public void onException(Throwable e) {
//
//            }
//        });
//    }

    public void remove() {
        KeyManager.getInstance().removeListener(channel_selection_modeListener);
        KeyManager.getInstance().removeListener(OcuBandWidthListener);
//        KeyManager.getInstance().removeListener(imageTransModeListener);
        if (airLink != null && airLink.getOcuSyncLink() != null) {
            airLink.getOcuSyncLink().setChannelInterferenceCallback(null);
        }
    }

    @Override
    public void onClick(View v) {
//        View anchor = tvHdMode;
//        mActivity.showPopup(anchor, mListView, listMode.size());
    }
}
*/
