package dji.sampleV5.aircraft.kmz.util;

import com.thoughtworks.xstream.XStream;
import com.thoughtworks.xstream.io.xml.DomDriver;

/**
 * @author: wang<PERSON>i
 * @date: 2022/8/1 16:54
 * @description:
 */
public class XmlUtils {

    /**
     * XML转对象  单一节点
     * @param clazz 对象类
     * @param xml   xml字符串
     * @return <T>  对象类
     */
    public static <T> T parseFromXml(Class<T> clazz, String xml) {
        XStream xStream = new XStream();
        //设置别名, 默认会输出全路径，这里设置为类名小写
        xStream.alias(clazz.getSimpleName().toLowerCase(), clazz);
        //解决xStream:Security framework of XStream not initialized, XStream is probably vulnerable
        XStream.setupDefaultSecurity(xStream);
        xStream.allowTypes(new Class[]{clazz});
        //将XML字符串转为bean对象
        try{
            return (T) xStream.fromXML(xml.trim());
        }catch (Exception e){
            return null;
        }
    }

    /**
     * 对象转XML 单一节点
     * @param obj 对象
     * @return xml xml字符串
     */
    public static <T> String toXml(T obj, Class<T> clazz) {
        //创建解析XML对象
        XStream xStream = new XStream();
        //设置别名, 默认会输出全路径，这里设置为类名小写
        xStream.alias(clazz.getSimpleName().toLowerCase(), clazz);
        try{
            return xStream.toXML(obj);
        }catch (Exception e){
            return null;
        }
    }


    /**
     * XML转对象(基于注解的形式) 单一节点
     * @param clazz 对象类
     * @param xml   xml字符串
     * @return <T>  对象类
     */
    public static <T> T parseFromXmlAnnotations(Class<T> clazz, String xml) {

        //创建解析XML对象
        XStream xStream = new XStream(new DomDriver());
        //处理注解
        xStream.processAnnotations(clazz);
        //解决xStream:Security framework of XStream not initialized, XStream is probably vulnerable
        XStream.setupDefaultSecurity(xStream);
        xStream.allowTypes(new Class[]{clazz});
        //将XML字符串转为bean对象
        try{
            return (T) xStream.fromXML(xml.trim());
        }catch (Exception e){
            return null;
        }
    }

    /**
     * 对象转XML(基于注解的形式) 单一节点
     * @param obj 对象
     * @return xml xml字符串
     */
    public static String toXmlAnnotations(Object obj) {
        XStream xStream = new XStream(new DomDriver());
        xStream.processAnnotations(obj.getClass());
        try{
            return xStream.toXML(obj);
        }catch (Exception e){
            return null;
        }
    }


}
