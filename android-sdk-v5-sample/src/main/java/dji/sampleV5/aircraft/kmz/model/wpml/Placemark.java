package dji.sampleV5.aircraft.kmz.model.wpml;

import com.thoughtworks.xstream.annotations.XStreamAlias;

import dji.sampleV5.aircraft.kmz.model.common.ActionGroup;
import dji.sampleV5.aircraft.kmz.model.kml.Point;

@XStreamAlias("Placemark")
public class Placemark {

    /**
     * 航点经纬度<经度,纬度>
     */
    @XStreamAlias("Point")
    private Point point;

    /**
     * 航线序号，在一条航线内该ID唯一。该序号必须从0开始单调连续递增
     */
    @XStreamAlias("wpml:index")
    private Integer index;

    /**
     * 航点执行高度，该元素仅在waylines.wpml中使用。
     */
    @XStreamAlias("wpml:executeHeight")
    private Float executeHeight;

    /**
     * 航点飞行速度
     */
    @XStreamAlias("wpml:waypointSpeed")
    private Float waypointSpeed;

    /**
     * 是否使用全局飞行速度
     */
    @XStreamAlias("wpml:useGlobalSpeed")
    private Boolean useGlobalSpeed;

    /**
     * 偏航角模式参数
     */
    @XStreamAlias("wpml:waypointHeadingParam")
    private WaypointHeadingParam waypointHeadingParam;

    /**
     * 航点类型（航点转弯模式）
     */
    @XStreamAlias("wpml:waypointTurnParam")
    private WaypointTurnParam waypointTurnParam;

    /**
     * 该航段是否贴合直线
     * <p>
     * 0：航段轨迹全程为曲线
     * 1：航段轨迹尽量贴合两点连线
     */
    @XStreamAlias("wpml:useStraightLine")
    private int useStraightLine;

    /**
     * 航点云台俯仰角
     */
    @XStreamAlias("wpml:gimbalPitchAngle")
    private Float gimbalPitchAngle;

    @XStreamAlias("wpml:actionGroup")
    private ActionGroup actionGroup;

    public Point getPoint() {
        return point;
    }

    public void setPoint(Point point) {
        this.point = point;
    }

    public Integer getIndex() {
        return index;
    }

    public void setIndex(Integer index) {
        this.index = index;
    }

    public Float getExecuteHeight() {
        return executeHeight;
    }

    public void setExecuteHeight(Float executeHeight) {
        this.executeHeight = executeHeight;
    }

    public Float getWaypointSpeed() {
        return waypointSpeed;
    }

    public void setWaypointSpeed(Float waypointSpeed) {
        this.waypointSpeed = waypointSpeed;
    }

    public WaypointHeadingParam getWaypointHeadingParam() {
        return waypointHeadingParam;
    }

    public void setWaypointHeadingParam(WaypointHeadingParam waypointHeadingParam) {
        this.waypointHeadingParam = waypointHeadingParam;
    }

    public WaypointTurnParam getWaypointTurnParam() {
        return waypointTurnParam;
    }

    public void setWaypointTurnParam(WaypointTurnParam waypointTurnParam) {
        this.waypointTurnParam = waypointTurnParam;
    }

    public int getUseStraightLine() {
        return useStraightLine;
    }

    public void setUseStraightLine(int useStraightLine) {
        this.useStraightLine = useStraightLine;
    }

    public Float getGimbalPitchAngle() {
        return gimbalPitchAngle;
    }

    public void setGimbalPitchAngle(Float gimbalPitchAngle) {
        this.gimbalPitchAngle = gimbalPitchAngle;
    }

    public ActionGroup getActionGroup() {
        return actionGroup;
    }

    public void setActionGroup(ActionGroup actionGroup) {
        this.actionGroup = actionGroup;
    }

    public Boolean getUseGlobalSpeed() {
        return useGlobalSpeed;
    }

    public void setUseGlobalSpeed(Boolean useGlobalSpeed) {
        this.useGlobalSpeed = useGlobalSpeed;
    }
}
