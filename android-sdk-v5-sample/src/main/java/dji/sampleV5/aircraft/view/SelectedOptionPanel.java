package dji.sampleV5.aircraft.view;

import dji.sampleV5.aircraft.R;
import android.content.Context;
import android.graphics.Color;
import android.util.AttributeSet;
import android.view.Gravity;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import java.util.List;

public class SelectedOptionPanel extends LinearLayoutWidget {


    private ICallback iCallback;
    private String currentText;

    public SelectedOptionPanel(Context context) {
        super(context);
    }

    public SelectedOptionPanel(Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
    }

    public SelectedOptionPanel(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }

    public void setCallback(ICallback callback) {
        this.iCallback = callback;
    }

    public void setCurrentText(String text) {
        this.currentText = text;
    }

    @Override
    protected void initView(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        setOrientation(VERTICAL);
        setLayoutParams(new ViewGroup.LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT));
        setBackgroundResource(R.drawable.ext_bg_blue_stroke_and_black_solid_rectangle);
    }

    public void setData(List<String> data) {
        for (int index = 0; index < data.size(); index++) {
            TextView textView = new TextView(getContext());
            textView.setText(data.get(index));
            if (data.get(index).equals(currentText)) {
                textView.setTextColor(Color.BLUE);
            } else {
                textView.setTextColor(Color.WHITE);
            }
            textView.setGravity(Gravity.CENTER);
            textView.setPadding(0, 10, 0, 10);
            final int finalIndex = index;
            textView.setOnClickListener(view -> {
                if (iCallback != null) {
                    iCallback.onClick(finalIndex, textView.getText().toString().trim());
                } else {
                    SelectedOptionPanel.this.setVisibility(GONE);
                }
            });
            addView(textView);
        }
    }

    @Override
    protected void reactToModelChanges() {

    }

    @NonNull
    @Override
    public String getIdealDimensionRatioString() {
        return null;
    }

    public interface ICallback {
        void onClick(int index, String result);
    }
}
