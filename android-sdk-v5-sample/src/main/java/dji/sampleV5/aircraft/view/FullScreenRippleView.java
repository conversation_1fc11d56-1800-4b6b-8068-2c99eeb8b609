package dji.sampleV5.aircraft.view;

import android.content.Context;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.util.AttributeSet;
import android.view.View;
import android.view.animation.AlphaAnimation;
import android.view.animation.Animation;
import android.view.animation.AnimationSet;
import android.view.animation.ScaleAnimation;

import androidx.annotation.Nullable;

public class FullScreenRippleView extends View {
    private static final int RIPPLE_COLOR = Color.WHITE;

    private Paint paint;

    private AnimationSet animationSet;

    public FullScreenRippleView(Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        paint = new Paint();
        paint.setStyle(Paint.Style.FILL);
        paint.setColor(RIPPLE_COLOR);

        setAlpha(0);
    }

    public void setParam(int smallWidth, int smallHeight, int bigWidth, int bigHeight) {
        float bigHeight1 = (float) Math.sqrt(Math.pow(bigWidth, 2) + Math.pow(bigHeight, 2));
        float scaleY = bigHeight1 / smallHeight;

        ScaleAnimation scaleAnimation = new ScaleAnimation(1, scaleY, 1, scaleY, Animation.RELATIVE_TO_SELF, 0, Animation.RELATIVE_TO_SELF, 1);
        AlphaAnimation alphaAnimation = new AlphaAnimation(0.5f, 0);
        animationSet = new AnimationSet(true);
        animationSet.addAnimation(scaleAnimation);
        animationSet.addAnimation(alphaAnimation);
        animationSet.setAnimationListener(new Animation.AnimationListener() {
            @Override
            public void onAnimationStart(Animation animation) {
                setAlpha(1);
            }

            @Override
            public void onAnimationEnd(Animation animation) {
                setAlpha(0);
            }

            @Override
            public void onAnimationRepeat(Animation animation) {

            }
        });
        animationSet.setDuration(500);
    }

    public void startAnimation() {
        if (animationSet != null && animationSet.hasStarted()) {
            animationSet.reset();
        }

        startAnimation(animationSet);
    }

    @Override
    protected void onDraw(Canvas canvas) {
        super.onDraw(canvas);
        float radius = Math.min(getWidth(), getHeight());
        canvas.drawCircle(0, getHeight(), radius, paint);
    }
}
