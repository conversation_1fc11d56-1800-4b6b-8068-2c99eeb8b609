package dji.sampleV5.aircraft.view.dialog;

import android.content.Context;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.Button;
import android.widget.TextView;

import androidx.appcompat.app.AlertDialog;
import dji.sampleV5.aircraft.R;

public class NoticeAlertDialog extends AlertDialog {
    private onDialogClickListener onDialogClick;
    private TextView tvContent;
    private Button positiveButton;

    // 构造函数，接收上下文作为参数
    public NoticeAlertDialog(Context context,onDialogClickListener onDialogClick) {
        super(context);
        this.onDialogClick = onDialogClick;

    }

    // 可选构造函数，可以根据需求添加更多参数
    public NoticeAlertDialog(Context context, int themeResId) {
        super(context, themeResId);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        // 设置布局
        View dialogView = LayoutInflater.from(getContext()).inflate(R.layout.alert_dialog, null);

        // 初始化视图组件
        positiveButton = dialogView.findViewById(R.id.btn_alert_ok);
        Button negativeButton = dialogView.findViewById(R.id.btn_alert_cancel);
        tvContent = dialogView.findViewById(R.id.tv_alert_content);

        // 设置按钮监听器
        positiveButton.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                // 正面操作的处理逻辑
                dismiss();
                onDialogClick.onPositive();
            }
        });

        negativeButton.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                // 负面操作的处理逻辑
                dismiss();
            }
        });

        // 设置对话框的内容视图
        setView(dialogView);

        // 可以在此处设置标题、消息等内容
        /*setTitle("标题");
        setMessage("消息内容");*/
    }

    public void setContent(String content){
        tvContent.setText(content);
    }

    public void setPositiveButtonName(String name) {
        positiveButton.setText(name);
    }

    public interface onDialogClickListener {
        void onPositive();

        void onNegative();
    }
}
