package dji.sampleV5.aircraft.view.adapter;

import android.content.Context;
import android.content.Intent;
import android.text.TextUtils;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.BaseAdapter;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.hjq.toast.Toaster;

import dji.sampleV5.aircraft.ContextUtil;
import dji.sampleV5.aircraft.DJIAircraftApplication;
import dji.sampleV5.aircraft.R;
import dji.sampleV5.aircraft.common.json.JsonUtil;
import dji.sampleV5.aircraft.data.task.TaskInfo;
import dji.sampleV5.aircraft.mvvm.ext.StorageExtKt;
import dji.sampleV5.aircraft.mvvm.key.ValueKey;
import dji.sampleV5.aircraft.page.RecentMissionDetailActivity;
import dji.sampleV5.aircraft.util.ToastUtil;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

/**
 * Describe
 */
public class TaskAdapter extends BaseAdapter {
    private List<TaskInfo> taskInfoList;
    private Context context;
    private SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    public TaskAdapter(Context context, List<TaskInfo> taskInfoList) {
        this.context = context;
        this.taskInfoList = taskInfoList;
    }

    @Override
    public int getCount() {
        return taskInfoList.size();
    }

    @Override
    public Object getItem(int i) {
        return taskInfoList.get(i);
    }

    @Override
    public long getItemId(int i) {
        return i;
    }

    @Override
    public View getView(int position, View convertView, ViewGroup viewGroup) {
        View view = null;
        ViewHolder viewHolder = null;
        if (convertView == null) {
            view = LayoutInflater.from(context).inflate(R.layout.item_task_list, null);

            viewHolder = new ViewHolder();
            //实例化ViewHolder
            viewHolder.tvMissionName = view.findViewById(R.id.tvMissionName);
            viewHolder.tvMissionIdAndTime = view.findViewById(R.id.tvMissionIdAndTime);
            viewHolder.tvFlyMileage = view.findViewById(R.id.tvFlyMileage);
            viewHolder.tvTaskState = view.findViewById(R.id.tvTaskState);
            viewHolder.tvAircraftNum = view.findViewById(R.id.tvAircraftNum);
            viewHolder.tvFinshAction = view.findViewById(R.id.tvFinishAction);
            viewHolder.tvHiveName = view.findViewById(R.id.tvHiveName);
            viewHolder.tvStartAndLandTime = view.findViewById(R.id.tvStartAndLandTime);
            viewHolder.ivUpload = view.findViewById(R.id.upload);

            //将viewHolder的对象存储到View中
            view.setTag(viewHolder);
        } else {
            view = convertView;
            //取出ViewHolder
            viewHolder = (ViewHolder) view.getTag();
        }
        TaskInfo taskInfo = taskInfoList.get(position);
        //给item中各控件赋值
        viewHolder.tvMissionName.setText(taskInfo.getMissionName());
        viewHolder.tvTaskState.setText(getState(taskInfo.getState()));
        if(TextUtils.equals("5",taskInfo.getState())){//降落完成
            viewHolder.tvTaskState.setBackgroundColor(ContextUtil.getColor(R.color.green));
            viewHolder.tvTaskState.setTextColor(ContextUtil.getColor(R.color.white));
        }else {
            viewHolder.tvTaskState.setBackgroundColor(ContextUtil.getColor(R.color.gray));
            viewHolder.tvTaskState.setTextColor(ContextUtil.getColor(R.color.white));
        }
        viewHolder.tvMissionIdAndTime.setText("批次号: " + taskInfo.getMissionBatch() + " / 创建时间: " + taskInfoList.get(position).getCreateTime());
        viewHolder.tvHiveName.setText(taskInfo.getSiteName());
        viewHolder.tvAircraftNum.setText(taskInfo.getUAVID());
        viewHolder.tvFlyMileage.setText(taskInfo.getUAVFlightMileage() + "m");
        viewHolder.tvFinshAction.setText(getFinishAction(taskInfo.getFinishAction()));
        viewHolder.ivUpload.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if(DJIAircraftApplication.getInstance().getProductType() == null){
                    ToastUtil.show("请先连接无人机！");
                    return;
                }
                boolean isSdCard = StorageExtKt.getMmkv().getBoolean(ValueKey.IS_SD_CARD, false);
                if (!isSdCard) {
                    Toaster.show("请先插入SD卡！");
                    return;
                }
               /* if(TextUtils.isEmpty(taskInfo.getUAVStartTime()) || TextUtils.isEmpty(taskInfo.getUAVEndTime()) ){
                    ToastUtil.show("飞行历史不完整，无法上传！");
                    return;
                }*/

                if(TextUtils.isEmpty(taskInfo.getUAVStartTime())){
                    try {
                        Date startDate = simpleDateFormat.parse(taskInfo.getCreateTime());
                        long startTime = startDate.getTime();
                        taskInfo.setUAVStartTime(simpleDateFormat.format(startTime));
                    } catch (ParseException e) {
                        e.printStackTrace();
                    }
                }

                if(TextUtils.isEmpty(taskInfo.getUAVEndTime())){
                    try {
                        Date startDate = simpleDateFormat.parse(taskInfo.getUAVStartTime());
                        long startTime = startDate.getTime();
                        taskInfo.setUAVEndTime(simpleDateFormat.format(startTime + 30 * 60 *1000));
                    } catch (ParseException e) {
                        e.printStackTrace();
                    }

                }

                Intent intent = new Intent(ContextUtil.getCurrentActivity(), RecentMissionDetailActivity.class);
                intent.putExtra("taskInfo", JsonUtil.toJson(taskInfo));
                ContextUtil.getCurrentActivity().startActivity(intent);
            }
        });
        if (TextUtils.isEmpty(taskInfo.getUAVStartTime())) {
            viewHolder.tvStartAndLandTime.setText("暂无时间");
        } else {
            String start = taskInfo.getUAVStartTime().substring(0, 19);
            String end = taskInfo.getUAVEndTime();
            if (end.isEmpty()) {
                viewHolder.tvStartAndLandTime.setText(start + "～暂无结束时间");
            } else {
                viewHolder.tvStartAndLandTime.setText(start + "～" + end.substring(0, 19));
            }


        }
        return view;

    }

    private String getState(String state) {
        switch (state) {
            case "1":
                return "待命";
            case "2":
                return "起飞准备中";
            case "3":
                return "执飞中";
            case "4":
                return "降落完成中";
            case "5":
                return "降落完成";
            default:
                return "未知";

        }
    }

    private String getFinishAction(String state) {
        if (state == "1") {
            return "自动返航";
        } else {
            return "终点站";
        }
    }

    static class ViewHolder {
        TextView tvMissionName, tvTaskState, tvMissionIdAndTime, tvHiveName, tvAircraftNum, tvFlyMileage, tvFinshAction, tvStartAndLandTime;
        RelativeLayout ivUpload;
    }
}
