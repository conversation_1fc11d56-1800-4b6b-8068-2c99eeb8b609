package dji.sampleV5.aircraft.view;

import android.content.Context;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.graphics.RectF;
import android.util.AttributeSet;
import android.view.View;

import androidx.annotation.Nullable;

import dji.sampleV5.aircraft.R;

import dji.sampleV5.aircraft.ContextUtil;


public class BatteryEnergyView extends View {
    private  Paint mPaint;
    private int mWidth;
    private int mHeight;
    private float mpercent=1;

    public BatteryEnergyView(Context context) {
        super(context);
    }

    public BatteryEnergyView(Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        mPaint = new Paint(Paint.ANTI_ALIAS_FLAG);

    }

    public BatteryEnergyView(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }
    @Override
    protected void onSizeChanged(int w, int h, int oldw, int oldh) {
        super.onSizeChanged(w, h, oldw, oldh);
        mWidth = getWidth();
        mHeight = getHeight();
    }
    @Override
    protected void onDraw(Canvas canvas) {
        float cx = (float) mWidth;
        float cy = (float) mHeight;
        this.setBackground(ContextUtil.getDrawable(R.drawable.ic_kuang_battery));
        // 绘制上轴
        if (mpercent!=1){
            mPaint.setStyle(Paint.Style.FILL);
            mPaint.setColor(Color.GREEN);
            mPaint.setAntiAlias(true);// 设置画笔的锯齿效果
            RectF ova = new RectF(10, cy*mpercent+10, cx-10, cy-10);// 设置个新的长方形

            canvas.drawRoundRect(ova, 1, 1, mPaint);//第二个参数是x半径，第三个参数是y半径
        }

    }
    //百分比
    public void getPercent(float percent){
        mpercent=(1-percent);
        invalidate();
    }
}
