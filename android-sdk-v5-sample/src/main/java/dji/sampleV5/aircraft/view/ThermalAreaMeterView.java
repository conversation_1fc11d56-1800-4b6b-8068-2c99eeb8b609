package dji.sampleV5.aircraft.view;


import android.content.Context;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.graphics.Path;
import android.graphics.Point;
import android.graphics.RectF;
import android.util.Log;
import android.view.MotionEvent;
import android.view.View;


import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import java.util.Locale;

import dji.sdk.keyvalue.key.CameraKey;
import dji.sdk.keyvalue.key.KeyTools;
import dji.sdk.keyvalue.value.camera.ThermalAreaMetersureTemperature;
import dji.sdk.keyvalue.value.camera.ThermalTemperatureMeasureMode;
import dji.sdk.keyvalue.value.common.CameraLensType;
import dji.sdk.keyvalue.value.common.ComponentIndexType;
import dji.sdk.keyvalue.value.common.DoublePoint2D;
import dji.sdk.keyvalue.value.common.DoubleRect;
import dji.v5.common.callback.CommonCallbacks;
import dji.v5.common.error.IDJIError;
import dji.v5.manager.KeyManager;

public class ThermalAreaMeterView extends View {


    public static final int NONE = 0;
    public static final int LEFT_TOP_VIEW = 1;
    public static final int RIGHT_BOTTOM_VIEW = 2;

    private final static int minWidth = 110;

    private final static int shadowLength = 90;

    private float currentX;
    private float currentY;

    private int availableMaxWidth;
    private int availableMaxHeight;

    private int viewWidth;
    private int viewHeight;

    private int fpvWidth;
    private int fpvHeight;

    private Path path;
    private Paint paint;
    private RectF oval;

    private int currentArea = NONE;
    private int paddingStart;
    private int paddingTop;

    //private Camera thermalCamera;

    //private Lens thermalLens = null;

    private boolean isH20T;

    private Point maxPoint;
    private Point minPoint;
    private long lastTime;

    private float ratio = 1.25f;

    public ThermalAreaMeterView(Context context) {
        super(context);

        maxPoint = new Point();
        minPoint = new Point();
    }

    public void startMeter(int fpvWidth, int fpvHeight) {
        Log.e("TAG", "fpvWidth: "+fpvWidth+"  fpvHeight:"+fpvHeight);
        this.fpvWidth = fpvWidth;
        this.fpvHeight = fpvHeight;

        ratio = (float) fpvWidth / fpvHeight;

        /*this.availableMaxHeight = fpvHeight - 100;
        this.availableMaxWidth = (int) (availableMaxHeight * ratio);*/
        this.availableMaxHeight = fpvHeight;
        this.availableMaxWidth = fpvWidth;


        if (oval == null) {
            oval = new RectF();
        }
        if (viewWidth != 0 && viewHeight != 0)
            oval.set((float) (viewWidth - availableMaxWidth / 2) / 2, (float) (viewHeight - availableMaxWidth / 2) / 2, (float) (viewWidth + availableMaxHeight / 2) / 2, (float) (viewHeight + availableMaxHeight / 2) / 2); // first ui

        //setThermalArea();
    }

    public void stopMeasureTem() {

    }

    public void startMeasureTem() {
        KeyManager.getInstance().setValue(KeyTools.createCameraKey(CameraKey.KeyThermalTemperatureMeasureMode, ComponentIndexType.LEFT_OR_MAIN, CameraLensType.CAMERA_LENS_THERMAL), ThermalTemperatureMeasureMode.REGION, new CommonCallbacks.CompletionCallback() {
            @Override
            public void onSuccess() {
                Log.e("TAG", "设置红外区域测温成功: ");
                //DoubleRect rectF = new DoubleRect((double)(oval.left - paddingStart) / fpvWidth, (double)(oval.top - paddingTop) / fpvHeight, (double)(oval.right - paddingStart) / fpvWidth, (double)(oval.bottom - paddingTop) / fpvHeight);
                DoubleRect doubleRect = new DoubleRect(0.0, 0.0, 1.0, 1.0);
                KeyManager.getInstance().setValue(KeyTools.createCameraKey(CameraKey.KeyThermalRegionMetersureArea, ComponentIndexType.LEFT_OR_MAIN, CameraLensType.CAMERA_LENS_THERMAL), doubleRect, new CommonCallbacks.CompletionCallback() {
                    @Override
                    public void onSuccess() {
                        Log.e("TAG", "设置红外区域成功: ");
                    }

                    @Override
                    public void onFailure(@NonNull IDJIError error) {
                        Log.e("TAG", "设置红外区域失败: "+error.description());
                    }
                });

                KeyManager.getInstance().listen(KeyTools.createCameraKey(CameraKey.KeyThermalRegionMetersureTemperature, ComponentIndexType.LEFT_OR_MAIN, CameraLensType.CAMERA_LENS_THERMAL), this, new CommonCallbacks.KeyListener<ThermalAreaMetersureTemperature>() {
                    @Override
                    public void onValueChange(@Nullable ThermalAreaMetersureTemperature oldValue, @Nullable ThermalAreaMetersureTemperature newValue) {
                        if(newValue != null){
                            Log.e("TAG", ": "+newValue.getMaxAreaTemperature() + " "+newValue.getMinAreaTemperature());
                            updateTemperature(newValue);
                        }
                    }
                });

            }

            @Override
            public void onFailure(@NonNull IDJIError error) {
                if (error != null) {
                    Log.e("设置红外区域测温失败", "onFailure: " + error.description());
                }

            }
        });
    }

    private void updateTemperature(ThermalAreaMetersureTemperature thermalAreaMetersureTemperature) {
        DoublePoint2D maxPoint = thermalAreaMetersureTemperature.getMaxTemperaturePoint();
        DoublePoint2D minPoint = thermalAreaMetersureTemperature.getMinTemperaturePoint();
        updatePoint(maxPoint, minPoint);

        //500ms 更新一次，解决背压问题
        //dji.thirdparty.rx.exceptions.MissingBackpressureException
        if (System.currentTimeMillis() - lastTime < 500) {
            return;
        }

        lastTime = System.currentTimeMillis();

       /* String temp = String.format(Locale.CHINA, "最高温：%.1f    最低温：%.1f    平均温：%.1f", thermalAreaTemperatureAggregations.getMaxAreaTemperature(), thermalAreaTemperatureAggregations.getMinAreaTemperature(), thermalAreaTemperatureAggregations.getAverageAreaTemperature());
        UXEEvents.MeterAreaText areaMeter = new UXEEvents.MeterAreaText(temp, true);
        UXSDKEventBus.getInstance().post(areaMeter);*/
    }

    /*public void stopMeter() {
        if (isH20T) {
            if (thermalLens != null) {
                thermalLens.setThermalAreaTemperatureAggregationsCallback(null);
            }
        } else {
            if (thermalCamera != null) {
                thermalCamera.setThermalAreaTemperatureAggregationsCallback(null);
            }
        }
    }*/

    @Override
    protected void onSizeChanged(int viewWidth, int viewHeight, int oldWidth, int oldHeight) {
        super.onSizeChanged(viewWidth, viewHeight, oldWidth, oldHeight);
        this.viewWidth = viewWidth;
        this.viewHeight = viewHeight;
        Log.e("onSizeChanged", "viewWidth: "+viewWidth+"  viewHeight:"+viewHeight);

        paddingStart = (viewWidth - this.availableMaxWidth) / 2;
        paddingTop = (viewHeight - this.availableMaxHeight) / 2;

        path = new Path();
        paint = new Paint();
        paint.setAntiAlias(true);

        oval = new RectF();
        oval.set((float) (viewWidth - availableMaxWidth / 2) / 2, (float) (viewHeight - availableMaxWidth / 2) / 2, (float) (viewWidth + availableMaxHeight / 2) / 2, (float) (viewHeight + availableMaxHeight / 2) / 2); // first ui

        maxPoint.set((int) oval.left, (int) oval.top);
        minPoint.set((int) oval.left, (int) oval.top);

        setThermalArea();
    }

    @Override
    protected void onDraw(Canvas canvas) {
        super.onDraw(canvas);

        paint.setStrokeWidth(3);
        paint.setColor(Color.WHITE);
        paint.setStyle(Paint.Style.STROKE);
        canvas.drawRect(oval, paint);

        paint.setStrokeWidth(1);
        paint.setColor(Color.BLACK);
        paint.setAlpha(128);
        paint.setStyle(Paint.Style.FILL_AND_STROKE);

        //左上角阴影
        path.reset();
        path.moveTo(oval.left + 2, oval.top + 2);
        path.lineTo(oval.left + 2, oval.top + shadowLength - 4);
        path.lineTo(oval.left + shadowLength - 4, oval.top + 2);
        path.close();
        canvas.drawPath(path, paint);

        //右下角阴影
        path.reset();
        path.moveTo(oval.right - 2, oval.bottom - 2);
        path.lineTo(oval.right - 2, oval.bottom - shadowLength + 4);
        path.lineTo(oval.right - shadowLength + 4, oval.bottom - 2);
        path.close();
        canvas.drawPath(path, paint);

        paint.setStrokeWidth(2);
        paint.setColor(Color.WHITE);
        paint.setStyle(Paint.Style.FILL_AND_STROKE);

        //左上角箭头
        canvas.drawLine(oval.left + 15, oval.top + 15, oval.left + 40, oval.top + 40, paint);
        canvas.drawLine(oval.left + 15, oval.top + 15, oval.left + 15, oval.top + 25, paint);
        canvas.drawLine(oval.left + 15, oval.top + 15, oval.left + 25, oval.top + 15, paint);
        canvas.drawLine(oval.left + 30, oval.top + 40, oval.left + 40, oval.top + 40, paint);
        canvas.drawLine(oval.left + 40, oval.top + 30, oval.left + 40, oval.top + 40, paint);

        //右下角箭头
        canvas.drawLine(oval.right - shadowLength + 55, oval.bottom - 15, oval.right - 15, oval.bottom - shadowLength + 55, paint);
        canvas.drawLine(oval.right - shadowLength + 65, oval.bottom - 10, oval.right - 10, oval.bottom - shadowLength + 65, paint);


        //画圆点
        paint.setStrokeWidth(1);
        paint.setColor(Color.WHITE);
        paint.setStyle(Paint.Style.FILL);
        canvas.drawCircle(minPoint.x, minPoint.y, 13, paint);

        paint.setStrokeWidth(1);
        paint.setStyle(Paint.Style.FILL);
        paint.setColor(Color.BLUE);
        canvas.drawCircle(minPoint.x, minPoint.y, 10, paint);

        paint.setStrokeWidth(1);
        paint.setColor(Color.WHITE);
        paint.setStyle(Paint.Style.FILL);
        canvas.drawCircle(maxPoint.x, maxPoint.y, 13, paint);

        paint.setStrokeWidth(1);
        paint.setStyle(Paint.Style.FILL);
        paint.setColor(Color.RED);
        canvas.drawCircle(maxPoint.x, maxPoint.y, 10, paint);
    }

    private void updatePoint(DoublePoint2D max, DoublePoint2D min) {
        if (oval == null)
            return;

        /*this.maxPoint.x = (int) (paddingStart + max.x.floatValue() / 10000.0 * fpvWidth);
        this.maxPoint.y = (int) (paddingTop + max.y.floatValue() / 10000.0 * fpvHeight);

        this.minPoint.x = (int) (paddingStart + min.x.floatValue() / 10000.0 * fpvWidth);
        this.minPoint.y = (int) (paddingTop + min.y.floatValue() / 10000.0 * fpvHeight);*/
        this.maxPoint.x = (int) (max.getX().floatValue() * fpvWidth);
        this.maxPoint.y = (int) (max.getY().floatValue() * fpvHeight);

        this.minPoint.x = (int) (min.getX().floatValue() * fpvWidth);
        this.minPoint.y = (int) (min.getY().floatValue() * fpvHeight);

        //Log.e("TAG", "max.x: "+max.x+ "  max.y: "+max.y); //max.x: 0.8859000205993652  max.y: 0.5019999742507935
        Log.e("TAG", "maxPoint.x: "+maxPoint.x+ "  maxPoint.y: "+maxPoint.y);

        postInvalidate();
    }


    @Override
    public boolean onTouchEvent(MotionEvent event) {

        this.currentX = event.getX();
        this.currentY = event.getY();
        switch (event.getAction()) {
            case MotionEvent.ACTION_DOWN:
                currentArea = getResponseAreaIndex();
                break;
            case MotionEvent.ACTION_MOVE:
                if (currentArea == NONE) {
                    float newLeft, newRight, newTop, newBottom;
                    float currentWidth = oval.right - oval.left;
                    float currentHeight = oval.bottom - oval.top;
                    if (currentX < paddingStart + currentWidth / 2) {
                        newLeft = paddingStart;
                        newRight = newLeft + currentWidth;
                    } else if (currentX > paddingStart + availableMaxWidth - currentWidth / 2) {
                        newRight = paddingStart + availableMaxWidth;
                        newLeft = newRight - currentWidth;
                    } else {
                        newLeft = currentX - currentWidth / 2;
                        newRight = newLeft + currentWidth;
                    }

                    if (currentY < paddingTop + currentHeight / 2) {
                        newTop = paddingTop;
                        newBottom = newTop + currentHeight;
                    } else if (currentY > paddingTop + availableMaxHeight - currentHeight / 2) {
                        newBottom = paddingTop + availableMaxHeight;
                        newTop = newBottom - currentHeight;
                    } else {
                        newTop = currentY - currentHeight / 2;
                        newBottom = newTop + currentHeight;
                    }
                    oval.set(newLeft, newTop, newRight, newBottom);
                } else {
                    switch (currentArea) {
                        case LEFT_TOP_VIEW:
                            oval.set(paddingStart, paddingTop, paddingStart + availableMaxWidth, paddingTop + availableMaxHeight);
                            break;
                        case RIGHT_BOTTOM_VIEW:
                            float newRight, newBottom;
                            if (currentX > paddingStart + availableMaxWidth) {
                                newRight = paddingStart + availableMaxWidth;
                            } else if (currentX - oval.left < minWidth) {
                                newRight = oval.left + minWidth;
                            } else {
                                newRight = currentX;
                            }

                            newBottom = oval.top + (newRight - oval.left) / ratio;
                            oval.set(oval.left, oval.top, newRight, newBottom);
                            break;
                    }
                }
                postInvalidate(); // update ui
                break;
            case MotionEvent.ACTION_UP:
                setThermalArea();
            case MotionEvent.ACTION_CANCEL:
                break;
        }
        return true;
    }

    private int getResponseAreaIndex() {
        if ((currentX - oval.left > 0 && currentX - oval.left < 100) &&
                (currentY - oval.top > 0 && currentY - oval.top < 100)) {
            return LEFT_TOP_VIEW;
        }

        if ((oval.right - currentX > -50 && oval.right - currentX < 100) &&
                (oval.bottom - currentY > -50 && oval.bottom - currentY < 100)) {
            return RIGHT_BOTTOM_VIEW;
        }

        return NONE;
    }


    // 在热图像场景内设置矩形的测光区域，
    // 允许摄像机通过onUpdate回调方法传输聚合温度计算。
    // 请参阅ThermalAreaTemperatureAggregations可用的统计值。
    // 此方法需要一个与热场景的矩形成比例的相对矩形，x，y，width和height值必须都在0到1.0之间。
    // 仅受XT2，Mavic 2 Enterprise Dual和 Zenmuse XT支持，包含高级辐射测量功能。
    private void setThermalArea() {
        if (fpvWidth == 0 || fpvHeight == 0)
            return;
        RectF rectF = new RectF((oval.left - paddingStart) / fpvWidth, (oval.top - paddingTop) / fpvHeight, (oval.right - paddingStart) / fpvWidth, (oval.bottom - paddingTop) / fpvHeight);

       /* if (isH20T) {
            if (thermalLens != null) {
                thermalLens.setThermalMeteringArea(rectF, null);
            }
        } else {
            if (thermalCamera != null) {
                thermalCamera.setThermalMeteringArea(rectF, null);
            }
        }*/
    }
}

