package dji.sampleV5.aircraft.view;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.CheckBox;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.PopupWindow;

import com.amap.api.maps.AMap;
import dji.sampleV5.aircraft.R;

public class MapTypeMenu {

    private PopupWindow mPopupWindow;
    private CheckBox checkBoxPlain, checkBoxSate;

    public MapTypeMenu(Context context, AMap map) {
        LayoutInflater inflater = (LayoutInflater) context.getSystemService(Context.LAYOUT_INFLATER_SERVICE);
        View view = inflater.inflate(R.layout.item_map_type, null);
        view.measure(View.MeasureSpec.UNSPECIFIED, View.MeasureSpec.UNSPECIFIED);

        ImageView imagePlain = view.findViewById(R.id.image_plain);
        imagePlain.setOnClickListener(v -> {
            showCheckbox(true);
            map.setMapType(AMap.MAP_TYPE_NORMAL);

            if (mPopupWindow != null && mPopupWindow.isShowing()) {
                mPopupWindow.dismiss();
            }
        });

        ImageView imageSate = view.findViewById(R.id.image_satellite);
        imageSate.setOnClickListener(v -> {
            showCheckbox(false);
            map.setMapType(AMap.MAP_TYPE_SATELLITE);

            if (mPopupWindow != null && mPopupWindow.isShowing()) {
                mPopupWindow.dismiss();
            }
        });

        checkBoxPlain = view.findViewById(R.id.check_plain);
        checkBoxSate = view.findViewById(R.id.check_satellite);

        if (map.getMapType() == AMap.MAP_TYPE_SATELLITE) {
            showCheckbox(false);
        } else {
            showCheckbox(true);
        }

        mPopupWindow = new PopupWindow(view, FrameLayout.LayoutParams.WRAP_CONTENT,
                FrameLayout.LayoutParams.WRAP_CONTENT);

        mPopupWindow.setOutsideTouchable(true);
    }

    public void show(View anchor) {
        if (mPopupWindow != null && !mPopupWindow.isShowing()) {
            mPopupWindow.showAsDropDown(anchor, -mPopupWindow.getContentView().getMeasuredWidth() / 2 + anchor.getWidth() / 2, 0);
        }
    }

    private void showCheckbox(boolean isPlain) {
        checkBoxPlain.setVisibility(isPlain ? View.VISIBLE : View.GONE);
        checkBoxSate.setVisibility(isPlain ? View.GONE : View.VISIBLE);
    }

}
