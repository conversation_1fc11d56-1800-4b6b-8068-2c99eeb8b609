package dji.sampleV5.aircraft.view.task;

import android.content.Context;
import android.util.AttributeSet;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.yc.video.player.OnVideoStateListener;
import com.yc.video.player.VideoPlayer;


public class TaskVideoPlayer extends VideoPlayer{
    private long pos=0;
    private Context mContext;
    public TaskVideoPlayer(@NonNull Context context) {
        super(context);

    }

    public TaskVideoPlayer(@NonNull Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        this.mContext = context;
    }

    public TaskVideoPlayer(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }

    @Override
    public void initPlayer() {
        super.initPlayer();
    }

    @Override
    public void seekTo(long pos) {
        super.seekTo(pos);
        mMediaPlayer.seekTo(pos);
        this.pos = pos;
    }


    @Override
    public void pause() {

        super.pause();
    }



    @Override
    public void addOnStateChangeListener(@NonNull OnVideoStateListener listener) {
        super.addOnStateChangeListener(listener);
    }

    @Override
    public void setPlayerState(int playerState) {
        super.setPlayerState(playerState);
    }
}
