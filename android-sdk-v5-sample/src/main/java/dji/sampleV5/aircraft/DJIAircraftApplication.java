package dji.sampleV5.aircraft;

import android.app.Activity;
import android.app.Application;
import android.content.Context;
import android.os.Bundle;

import androidx.multidex.MultiDex;

import com.cySdkyc.clx.Helper;
import com.otaliastudios.cameraview.PictureResult;
import com.tencent.bugly.crashreport.CrashReport;

import dji.sampleV5.aircraft.data.mission.MissionStatus;
import dji.sampleV5.aircraft.data.preference.SpUtil;
import dji.sampleV5.aircraft.lbs.bean.AppLatLng;
import dji.sampleV5.aircraft.mqtt.MQttManager;
import dji.sampleV5.aircraft.mvvm.base.MvvmHelper;
import dji.sampleV5.aircraft.mvvm.base.MvvmHelperKt;
import dji.sampleV5.aircraft.mvvm.event.LiveDataEvent;
import dji.sampleV5.aircraft.mvvm.util.CrashHandlerUtil;
import dji.sampleV5.aircraft.mvvm.util.XLogUtil;
import dji.sampleV5.aircraft.net.bean.UAVInfoSN;
import dji.sdk.keyvalue.value.camera.CameraThermalPalette;
import dji.sdk.keyvalue.value.product.ProductType;
import dji.v5.common.error.IDJIError;
import dji.v5.common.register.DJISDKInitEvent;
import dji.v5.manager.SDKManager;
import dji.v5.manager.interfaces.SDKManagerCallback;
import dji.v5.network.DJINetworkManager;

public class DJIAircraftApplication extends DJIApplication implements Application.ActivityLifecycleCallbacks{
    private static DJIAircraftApplication app = null;
    public static DJIAircraftApplication getInstance() {
        return app;
    }
    //public PictureResult pictureResult;
    public UAVInfoSN uavInfoSN;
    public MissionStatus missionStatus;
    public boolean isAircraftConnected = false;
    /**
     * 当前无人机状态
     */
    private int storageCapacity;
    private int leftStorageCapacity;
    public int subState = 102;
    public int displayMode = 1;
    public PictureResult pictureResult;
    public int zoom = 1;
    public ProductType productType;
    public boolean isVsEnable = false;
    public AppLatLng lastWaypoint; //用于计算距离终点的距离，返航把这个值置空
    private boolean isInit = false;

    private CameraThermalPalette cameraThermalPalette;

    public void setCameraThermalPalette(CameraThermalPalette cameraThermalPalette) {
        this.cameraThermalPalette = cameraThermalPalette;
    }

    public CameraThermalPalette getCameraThermalPalette() {
        return cameraThermalPalette;
    }

    public int getStorageCapacity() {
        return storageCapacity;
    }

    public void setStorageCapacity(int storageCapacity) {
        this.storageCapacity = storageCapacity;
    }

    public int getLeftStorageCapacity() {
        return leftStorageCapacity;
    }

    public void setLeftStorageCapacity(int leftStorageCapacity) {
        this.leftStorageCapacity = leftStorageCapacity;
    }

    public void setLastWaypoint(AppLatLng lastWaypoint) {
        this.lastWaypoint = lastWaypoint;
    }

    public AppLatLng getLastWaypoint() {
        return lastWaypoint;
    }

    public boolean isVsEnable() {
        return isVsEnable;
    }

    public void setVsEnable(boolean vsEnable) {
        isVsEnable = vsEnable;
    }

    public void setProductType(ProductType productType) {
        this.productType = productType;
    }

    public ProductType getProductType() {
        return productType;
    }

    public int getZoom() {
        return zoom;
    }

    public void setZoom(int zoom) {
        this.zoom = zoom;
    }

    public void setDisplayMode(int displayMode) {
        this.displayMode = displayMode;
    }

    public int getDisplayMode() {
        return displayMode;
    }

    public void setPictureResult(PictureResult pictureResult) {
        this.pictureResult = pictureResult;
    }

    public PictureResult getPictureResult() {
        return pictureResult;
    }

    public boolean isMotorOn() {
        return isMotorOn;
    }

    public boolean isMotorOn = false;

    public void setMotorOn(boolean motorOn) {
        isMotorOn = motorOn;
    }

    public void setSubState(int subState) {
        this.subState = subState;
    }

    public int getSubState() {
        return subState;
    }

    public boolean isAircraftConnected() {
        return isAircraftConnected;
    }

    public void setAircraftConnected(boolean aircraftConnected) {
        isAircraftConnected = aircraftConnected;
    }


    public MissionStatus getMissionStatus() {
        return missionStatus;
    }

    public void setMissionStatus(MissionStatus missionStatus) {
        this.missionStatus = missionStatus;
    }

    public UAVInfoSN getUavInfoSN(){
        return uavInfoSN;
    }

    public void setUAVInfoSN(UAVInfoSN uavInfoSN) {
        this.uavInfoSN = uavInfoSN;
    }

    /*public void setPictureResult(PictureResult pictureResult) {
        this.pictureResult = pictureResult;
    }

    public PictureResult getPictureResult() {
        return pictureResult;
    }*/

    @Override
    public void onCreate() {
        super.onCreate();
        registerActivityLifecycleCallbacks(this);
        ContextUtil.init(this);
        MvvmHelper.INSTANCE.init(this);
        CrashReport.initCrashReport(getApplicationContext(), "da0333e6bb", true);
        SpUtil.init(this);
        dji.v5.ux.core.util.SpUtil.init(this);
        MQttManager.getInstance().init(this);
        MQttManager.getInstance().notifyAppStarted();
    }

    @Override
    protected void attachBaseContext(Context base) {
        super.attachBaseContext(base);
        app = this;
        com.cySdkyc.clx.Helper.install(this);
        CrashHandlerUtil.init(this);
        MvvmHelper.INSTANCE.xLogInit();
    }

    @Override
    public void onActivityCreated(Activity activity, Bundle savedInstanceState) {
        ContextUtil.addActivity(activity);
    }

    @Override
    public void onActivityStarted(Activity activity) {
    }

    @Override
    public void onActivityResumed(Activity activity) {
        ContextUtil.setCurrentActivity(activity);
    }

    @Override
    public void onActivityPaused(Activity activity) {
    }

    @Override
    public void onActivityStopped(Activity activity) {
    }

    @Override
    public void onActivitySaveInstanceState(Activity activity, Bundle outState) {
    }

    @Override
    public void onActivityDestroyed(Activity activity) {
        ContextUtil.removeActivity(activity);
    }
}
