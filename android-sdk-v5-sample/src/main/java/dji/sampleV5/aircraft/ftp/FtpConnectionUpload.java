package dji.sampleV5.aircraft.ftp;

import android.media.ExifInterface;
import android.os.Environment;
import android.util.Log;

import com.hjq.toast.Toaster;

import org.apache.commons.net.ftp.FTP;
import org.apache.commons.net.ftp.FTPClient;
import org.apache.commons.net.ftp.FTPReply;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.io.BufferedInputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.UnknownHostException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Locale;
import java.util.concurrent.TimeUnit;

import dji.sampleV5.aircraft.ContextUtil;
import dji.sampleV5.aircraft.data.mission.LaserUploadBean;
import dji.sampleV5.aircraft.data.preference.SpUtil;
import dji.sampleV5.aircraft.mvvm.ext.CommExtKt;
import dji.sampleV5.aircraft.mvvm.util.XLogUtil;
import dji.sampleV5.aircraft.net.bean.NetConfig;
import dji.sampleV5.aircraft.util.ToastUtil;
import dji.v5.manager.datacenter.media.MediaFile;
import io.socket.client.IO;
import okhttp3.Call;
import okhttp3.Callback;
import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;
import okhttp3.ResponseBody;

public class FtpConnectionUpload {
    private OnThreadResultListener listener;
    private ArrayList<MediaFile> uploadMediaList;
    private JSONArray jsonArray = new JSONArray();
    private String ftpPath;
    private String missionId;
    private String ANONYMOUS_LOGIN = "anonymous";
    private FTPClient ftp;
    private boolean is_connected = false;
    private int index = 0;
    private SimpleDateFormat sf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.CHINA);
    private boolean isM210 = false;
    private boolean isMavic = false;
    private List<LaserUploadBean> laserUploadBeanList;
    private long lastNoticeTime;
    private String lastDir;//每次FTP上传失败后必须走到创建文件夹才能再次恢复，所以强制让它走到创建文件夹

    /**
     * 构造函数
     */
    public FtpConnectionUpload(String missionId, String ftpPath, ArrayList<MediaFile> uploadMediaList, OnThreadResultListener listener) {
        this.ftpPath = ftpPath;
        this.missionId = missionId;
        this.uploadMediaList = uploadMediaList;
        this.listener = listener;
        is_connected = false;
        laserUploadBeanList = SpUtil.getLaserList();
        ftp = new FTPClient();
        ftp.setDefaultTimeout(FtpConfig.defaultTimeoutSecond);
        ftp.setConnectTimeout(FtpConfig.connectTimeoutSecond);
        ftp.setDataTimeout(FtpConfig.dataTimeoutSecond);
        ftp.setControlEncoding("GBK");
        ftp.setAutodetectUTF8(true);
        try {
            XLogUtil.INSTANCE.e("FtpConnectionUpload", "FtpConnectionUpload: start connect");
            initConnect(NetConfig.FtpHost, NetConfig.FtpPort, NetConfig.FtpName, NetConfig.FtpPwd);
        } catch (IOException e) {
            XLogUtil.INSTANCE.e("FtpConnectionUpload", "initConnect IOException:" + e.getCause() + "," + e.getLocalizedMessage() + "," + e.getMessage());
            e.printStackTrace();
        }
    }


    /**
     * 初始化连接
     *
     * @param host
     * @param port
     * @param user
     * @param password
     * @throws IOException
     */
    private void initConnect(String host, int port, String user, String password) throws IOException {
        try {
            ftp.connect(host, port);
        } catch (UnknownHostException ex) {
            //System.out.println("-UnknownHostException-"+ex.getMessage()+","+ex.getCause()+","+ex.getLocalizedMessage());
            XLogUtil.INSTANCE.e("FtpConnectionUpload", "Can't find FTP server '" + host + "'" + ex.getMessage() + "," + ex.getCause() + "," + ex.getLocalizedMessage());
            throw new IOException("Can't find FTP server '" + host + "'");
        }
        int reply = ftp.getReplyCode();
        if (!FTPReply.isPositiveCompletion(reply)) {
            disconnect();
            // System.out.println("-----disconnect 11");
            XLogUtil.INSTANCE.e("FtpConnectionUpload", "Can't connect to server '" + host + "'");
            throw new IOException("Can't connect to server '" + host + "'");
        }
        if (user.equals("")) {
            user = ANONYMOUS_LOGIN;
        }
        if (!ftp.login(user, password)) {
            is_connected = false;
            // System.out.println("-----disconnect 222");
            disconnect();
            XLogUtil.INSTANCE.e("FtpConnectionUpload", "Can't login to server '" + host + "'");
            throw new IOException("Can't login to server '" + host + "'");
        } else {
            //ftp.enterLocalActiveMode();
            ftp.enterLocalPassiveMode();
            ftp.setFileType(FTP.BINARY_FILE_TYPE);
            is_connected = true;
            XLogUtil.INSTANCE.e("FtpConnectionUpload", "-----ftp connect-");

            String tPth = ftpPath + "photo" + "/THRM/";
            createDirecroty(tPth);
            ftp.changeWorkingDirectory("/");
            String zPth = ftpPath + "photo" + "/ZOOM/";
            createDirecroty(zPth);
            ftp.changeWorkingDirectory("/");
            String wPth = ftpPath + "photo" + "/WIDE/";
            createDirecroty(wPth);
            ftp.changeWorkingDirectory("/");
            String vPth = ftpPath + "photo" + "/V/";
            createDirecroty(vPth);
            ftp.changeWorkingDirectory("/");
            String tPthVideo = ftpPath + "video" + "/THRM/";
            createDirecroty(tPthVideo);
            ftp.changeWorkingDirectory("/");
            String zPthVideo = ftpPath + "video" + "/ZOOM/";
            createDirecroty(zPthVideo);
            ftp.changeWorkingDirectory("/");
            String wPthVideo = ftpPath + "video" + "/WIDE/";
            createDirecroty(wPthVideo);
            ftp.changeWorkingDirectory("/");
            String vPthVideo = ftpPath + "video" + "/V/";
            createDirecroty(vPthVideo);
            ftp.changeWorkingDirectory("/");
            String sPthVideo = ftpPath + "video" + "/S/";
            createDirecroty(sPthVideo);
            ftp.changeWorkingDirectory("/");
        }
    }

    public void reConnect() {
        try {
            XLogUtil.INSTANCE.e("FtpConnectionUpload", "FtpConnectionUpload: start reConnect");
            initConnect(NetConfig.FtpHost, NetConfig.FtpPort, NetConfig.FtpName, NetConfig.FtpPwd);
        } catch (IOException e) {
            XLogUtil.INSTANCE.e("FtpConnectionUpload", "reConnect IOException:" + e.getCause() + "," + e.getLocalizedMessage() + "," + e.getMessage());
            e.printStackTrace();
        }
    }

    private long lastTime = 0;
    private int lastPoint = 0;

    public void start() {
        try {
            if (index >= uploadMediaList.size()) {
                if (System.currentTimeMillis() - lastNoticeTime < 10000) {
                    return;
                }
                ToastUtil.show("上传完毕");
                XLogUtil.INSTANCE.e("FtpConnectionUpload", "start: 上传完毕");
                if (NetConfig.PUBLIC_APP_SUFFIX.contains("数研院")) {
                    noticeAI();
                    noticeQiCloud();
                } else {
                    noticeNJ();
                }
                disconnect();
                lastPoint = 0;
                SpUtil.setLaserList(null);
                return;
            }
            MediaFile mediaFile = uploadMediaList.get(index);
            //XLogUtil.INSTANCE.e("FtpConnectionUpload", "startftp: " + mediaFile.getIndex());
            String originFilename = mediaFile.getFileName();
            String downloadName = originFilename.substring(0, originFilename.lastIndexOf("."));
            String suffix = ".jpg";
            if (originFilename.toLowerCase().contains("mp4")) {
                suffix = ".mp4";
            }
            File file = new File(Environment.getExternalStorageDirectory().getPath() + "/Skysys/download/" + downloadName + suffix);

            String uploadName = file.getName();

            String mediaUrl = setpath(ftpPath, uploadName);
            XLogUtil.INSTANCE.e("FtpConnectionUpload", "index = " + index + "  mediaUrl: " + mediaUrl);

            float targetDistance = -1;
            if (laserUploadBeanList != null && laserUploadBeanList.size() > 0) {
                for (LaserUploadBean laserUploadBean : laserUploadBeanList) {
                    if (laserUploadBean.getIndex() == mediaFile.getFileIndex()) {
                        targetDistance = laserUploadBean.getTargetDistance();
                        break;
                    }
                }
            }
            JSONObject jsonObject = new JSONObject();
            String fileType = "";
            String mediaViewType = "";
            try {
                jsonObject.put("mediaFileName", uploadName);
                if (uploadName.contains("T")) {
                    fileType = "THRM";
                    mediaViewType = "THRM";
                } else if (uploadName.contains("W")) {
                    fileType = "WIDE";
                    mediaViewType = "WIDE";
                } else if (uploadName.contains("Z")) {
                    fileType = "ZOOM";
                    mediaViewType = "ZOOM";
                } else if (uploadName.contains("V")) {
                    fileType = "V";
                    mediaViewType = "VISIBLE";
                } else if (uploadName.contains("S")) {
                    fileType = "S";
                    mediaViewType = "S";
                }

                if (uploadName.toLowerCase().contains("mp4")) {
                    jsonObject.put("mediaUrl", ftpPath + "video/" + fileType + "/");
                    jsonObject.put("mediaType", "video");
                    jsonObject.put("resolution", mediaFile.getResolution());
                } else {
                    jsonObject.put("mediaUrl", ftpPath + "photo/" + fileType + "/");
                    jsonObject.put("mediaType", "photo");
                    // 获取照片分辨率
                    int width = 0;
                    int height = 0;
                    ExifInterface exifInterface = new ExifInterface(file.getAbsolutePath());
                    width = exifInterface.getAttributeInt(ExifInterface.TAG_IMAGE_WIDTH, 0);
                    height = exifInterface.getAttributeInt(ExifInterface.TAG_IMAGE_LENGTH, 0);
                    jsonObject.put("resolution", width + "x" + height);
                }

                jsonObject.put("mediaViewType", mediaViewType);
                jsonObject.put("targetDistance", targetDistance);
                jsonObject.put("takePhotoTime", mediaFile.getDate().getYear() + "-" + mediaFile.getDate().getMonth() + "-" + mediaFile.getDate().getDay() + " " + mediaFile.getDate().getHour() + ":" + mediaFile.getDate().getMinute() + ":" + mediaFile.getDate().getSecond());
                ExifInterface exifInterface = new ExifInterface(file.getAbsolutePath());
                jsonObject.put("absoluteAltitude", 0);
                jsonObject.put("relativeAltitude", score2dimensionality(exifInterface.getAttribute(ExifInterface.TAG_GPS_ALTITUDE)));
                jsonObject.put("mediaFileSize", mediaFile.getFileSize());
                JSONObject shootPositionJsonObject = new JSONObject();
                shootPositionJsonObject.put("latitude", score2dimensionality(exifInterface.getAttribute(ExifInterface.TAG_GPS_LATITUDE)));
                shootPositionJsonObject.put("longitude", score2dimensionality(exifInterface.getAttribute(ExifInterface.TAG_GPS_LONGITUDE)));
                jsonObject.put("shootPosition", shootPositionJsonObject);
                //XLogUtil.INSTANCE.e("FtpConnectionUpload", "fpt targetDistance: " + targetDistance);
                jsonArray.put(jsonObject);
            } catch (JSONException e) {
                e.printStackTrace();
            }

            /*String end = "";
            if (mediaFile.getFileSize() < 2000000) {
                end = "_THRM.jpg";
            } else {
                end = "_ZOOM.jpg";
            }
            try {
                Date date = sf.parse(mediaFile.getDateCreated());
                long currentTime = date.getTime();
                if (currentTime - lastTime < 2200) {//判断为同一个点
                    String temp = getPointString(lastPoint);
                    uploadName = "DJI_" + lastTime + temp + end;
                } else {
                    lastPoint ++;
                    String temp = getPointString(lastPoint);
                    uploadName = "DJI_" + currentTime + temp + end;
                }
                lastTime = currentTime;
            } catch (ParseException e) {
                e.printStackTrace();
            }*/


            upload(mediaUrl, uploadName, file);
        } catch (IOException e) {
            listener.onError(index, "upload " + e.getLocalizedMessage(), null);
            XLogUtil.INSTANCE.e("FtpConnectionUpload", "start upload270行: " + e.getLocalizedMessage());
            e.printStackTrace();
        }
    }

    private double score2dimensionality(String string) {
        double dimensionality = 0.0;
        if (null == string) {
            return dimensionality;
        }

        //用 ，将数值分成3份
        String[] split = string.split(",");
        for (int i = 0; i < split.length; i++) {

            String[] s = split[i].split("/");
            //用112/1得到度分秒数值
            double v = Double.parseDouble(s[0]) / Double.parseDouble(s[1]);
            //将分秒分别除以60和3600得到度，并将度分秒相加
            dimensionality = dimensionality + v / Math.pow(60, i);
        }
        return dimensionality;
    }

    private String getPointString(int point) {
        String s = "";
        if (point < 10) {
            s = "_000" + point;
        } else if (point >= 10 && point <= 99) {
            s = "_00" + point;
        } else {
            s = "_0" + point;
        }
        return s;
    }

    /**
     * 上传文件
     *
     * @throws IOException
     */
    public void upload(String path, String ftpFileName, File localFile) throws IOException {
        XLogUtil.INSTANCE.e("FtpConnectionUpload", "upload: " + path + ftpFileName);
        //检查本地文件是否存在
        if (!is_connected) {
            listener.onError(index, "ftp--未连接", localFile);
            return;
        }
        //if (is_connected ){listener.onError(index,"测试",localFile);return;}
        if (ftp == null) {
            listener.onError(index, "ftp is null", localFile);
            return;
        }
        if (!localFile.exists()) {
            listener.onError(index, "This file doesn't exist", localFile);
            throw new IOException("Can't upload '" + localFile.getAbsolutePath() + "'. This file doesn't exist.");
        }
        //System.out.println("---上传ftp upload--");
        //设置工作路径
        if (!setWorkingDirectory(path)) {
            listener.onError(index, "设置工作路径失败", localFile);
            return;
        }
        //上传
        InputStream in = null;
        boolean isStore = false;
        try {

            XLogUtil.INSTANCE.e("FtpConnectionUpload", "开始上传到ftp: " + index);
            in = new BufferedInputStream(new FileInputStream(localFile));
            XLogUtil.INSTANCE.e("FtpConnectionUpload", "开始删除文件: " + index);
            ftp.deleteFile(ftpFileName);
            XLogUtil.INSTANCE.e("FtpConnectionUpload", "开始storeFile: " + index);
            //保存文件
            isStore = ftp.storeFile(ftpFileName, in);
            XLogUtil.INSTANCE.e("FtpConnectionUpload", "store完成: " + index + "  isStore = " + isStore);
            if (!isStore) {
                listener.onError(index, "Check FTP permissions and path", localFile);
                throw new IOException("Can't upload file '" + ftpFileName + "' to FTP server. Check FTP permissions and path.");
            }
        } finally {
            try {
                assert in != null;
                in.close();
                if (isStore) {
                    listener.onFinish(index);
                    //上传完就删除
                    localFile.delete();
                    index++;
                    XLogUtil.INSTANCE.e("FtpConnectionUpload", "下一个: " + index);
                    start();
                }else {
                    setWorkingDirectory(lastDir);
                }

                //disconnect();
            } catch (IOException ex) {
                listener.onError(index, "IOException11" + ex.getMessage() + "," + ex.getCause() + "," + ex.getLocalizedMessage(), localFile);
                System.out.println("----IOException11:" + ex.getMessage() + "," + ex.getCause() + "," + ex.getLocalizedMessage());
            }
        }
    }

    private void noticeNJ() {
        JSONObject data = new JSONObject();
        try {
            data.put("missionBatch", missionId);
            data.put("ftpAddr", NetConfig.FtpHost);
            data.put("ftpPassword", NetConfig.FtpPwd);
            data.put("ftpUsername", NetConfig.FtpName);
            data.put("ftpPort", NetConfig.FtpPort);
            data.put("ftpFloader", ftpPath);
            data.put("mediaRecordList", jsonArray);
            data.put("storageType", 2);
        } catch (JSONException e) {
            XLogUtil.INSTANCE.e("FtpConnectionUpload", "构建JSON失败" + e.getLocalizedMessage());
            return;
        }
        // 设置OkHttpClient的超时时间
        OkHttpClient httpClient = new OkHttpClient.Builder()
                .connectTimeout(20, TimeUnit.SECONDS) // 连接超时时间
                .writeTimeout(20, TimeUnit.SECONDS)   // 写操作超时时间
                .readTimeout(30, TimeUnit.SECONDS)    // 读操作超时时间
                .build();
        MediaType JSON = MediaType.parse("application/json;charset=utf-8");
        XLogUtil.INSTANCE.e("FtpConnectionUpload", "noticeNJ: " + CommExtKt.toJsonStr(data));
        RequestBody requestBody = RequestBody.create(JSON, data.toString());
        String url = NetConfig.NJ_MEDIA + "media/upload";
        Request request = new Request.Builder()
                .url(url)
                .post(requestBody)
                .build();
        Call call = httpClient.newCall(request);
        call.enqueue(new Callback() {
            @Override
            public void onFailure(Call call, IOException e) {
                Toaster.show("通知媒体服务失败:" + e.getMessage());
                XLogUtil.INSTANCE.e("FtpConnectionUpload", "noticeNJ onFailure: " + call.request() + "    error:" + e.getMessage());
            }
            @Override
            public void onResponse(Call call, Response response) throws IOException {
                try (ResponseBody responseBody = response.body()) {
                    if (response.isSuccessful()) {
                        Toaster.show("已通知媒体服务去处理数据");
                        XLogUtil.INSTANCE.e("FtpConnectionUpload", "noticeNJ onResponse: " + call.toString() + "  response:" + response.body());
                    } else {
                        Toaster.show("通知媒体服务失败:" + response.code());
                        XLogUtil.INSTANCE.e("FtpConnectionUpload", "Unexpected code " + response.code());
                    }
                }catch (Exception e) {
                    XLogUtil.INSTANCE.e("FtpConnectionUpload", "Failed to close response body on success: " + e.getMessage());
                }
            }
        });
    }

    private void noticeQiCloud() {
        JSONObject data = new JSONObject();
        try {
            data.put("missionBatch", missionId);
            data.put("ftpAddr", NetConfig.FtpHost);
            data.put("ftpPassword", NetConfig.FtpPwd);
            data.put("ftpUsername", NetConfig.FtpName);
            data.put("ftpPort", NetConfig.FtpPort);
            data.put("ftpFloader", ftpPath);
            data.put("mediaRecordList", jsonArray);
            data.put("storageType", 2);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        OkHttpClient httpClient = new OkHttpClient();
        MediaType JSON = MediaType.parse("application/json;charset=utf-8");
        Log.e("TAG", "noticeQiCloud: " + String.valueOf(data));
        RequestBody requestBody = RequestBody.create(JSON, String.valueOf(data));
        String url = NetConfig.QI_CLOUD_MEDIA + "media/MediaManage/uploadMeidaCallBack";
        Request request = new Request.Builder()
                .url(url)
                //.addHeader("token", "1D1sd3haH3adTgdDJ")
                .post(requestBody)
                .build();
        Call call = httpClient.newCall(request);
        call.enqueue(new Callback() {
            @Override
            public void onFailure(Call call, IOException e) {
                ContextUtil.getCurrentActivity().runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                        ToastUtil.show("通知祺云失败:" + e.getLocalizedMessage());
                    }
                });
                Log.e("TAG", "noticeQiCloud onFailure: " + call.toString() + "    error:" + e.getLocalizedMessage());
            }

            @Override
            public void onResponse(Call call, Response response) throws IOException {
                //在这里根据返回内容执行具体的操作
                Log.e("FtpConnectionUpload", "noticeQiCloud onResponse: " + call.toString() + "  response:" + response.toString());
                ContextUtil.getCurrentActivity().runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                        ToastUtil.show("已通知祺云去处理数据");
                    }
                });
            }
        });
    }

    private void noticeAI() {
        JSONObject data = new JSONObject();
        try {
            data.put("missionBatch", missionId);
            data.put("ftpAddr", NetConfig.FtpHost);
            data.put("ftpPassword", NetConfig.FtpPwd);
            data.put("ftpUsername", NetConfig.FtpName);
            data.put("ftpPort", NetConfig.FtpPort);
            data.put("ftpFloader", ftpPath);
            data.put("mediaRecordList", jsonArray);
            data.put("storageType", 2);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        OkHttpClient httpClient = new OkHttpClient();
        MediaType JSON = MediaType.parse("application/json;charset=utf-8");
        XLogUtil.INSTANCE.e("FtpConnectionUpload", "noticeAI: " + String.valueOf(data));
        RequestBody requestBody = RequestBody.create(JSON, String.valueOf(data));
        String url = NetConfig.LGURl2 + "UAV/Picture/Upload/Record";
        Request request = new Request.Builder()
                .url(url)
                .addHeader("token", "1D1sd3haH3adTgdDJ")
                .post(requestBody)
                .build();
        Call call = httpClient.newCall(request);
        call.enqueue(new Callback() {
            @Override
            public void onFailure(Call call, IOException e) {
                Toaster.show("通知后端失败:" + e.getLocalizedMessage());

                XLogUtil.INSTANCE.e("FtpConnectionUpload", "noticeAI onFailure: " + call.toString() + "    error:" + e.getLocalizedMessage());
            }

            @Override
            public void onResponse(Call call, Response response) throws IOException {
                //在这里根据返回内容执行具体的操作
                try (ResponseBody responseBody = response.body()) {
                    if (response.isSuccessful()) {
                        Toaster.show("已通知后台去处理数据");
                        XLogUtil.INSTANCE.e("FtpConnectionUpload", "noticeAI onResponse: " + call.toString() + "  response:" + response.toString());
                    } else {
                        Toaster.show("通知后台失败:" + response.code());
                        XLogUtil.INSTANCE.e("FtpConnectionUpload", "Unexpected code " + response.code());
                    }
                }catch (Exception e) {
                    XLogUtil.INSTANCE.e("FtpConnectionUpload", "Failed to close response body on success: " + e.getMessage());
                }
            }
        });

    }

    /**
     * 关闭连接
     *
     * @throws IOException
     */
    public void disconnect() throws IOException {
        if (ftp.isConnected()) {
            XLogUtil.INSTANCE.e("FtpConnectionUpload", "ftp.logout: ");
            try {
                ftp.logout();
            } catch (IOException ex) {
                System.out.println("-disconnect-" + ex.getLocalizedMessage() + "," + ex.getMessage());
                ex.printStackTrace();
            } finally {
                if (ftp.isConnected()) {
                    ftp.disconnect();
                }
                is_connected = false;
            }
        }
    }

    private String setpath(String path, String ftpFileName) {
        String pathTo;
        if (ftpFileName.contains("JPG") || ftpFileName.contains("jpg")) {
            //info.mediaRecordList.setMediaType("photo");
            if (ftpFileName.contains("T")) {
                //info.mediaRecordList.setMediaViewType("THRM");
                pathTo = path + "photo" + "/THRM/";
            } else if (ftpFileName.contains("W")) {
                //info.mediaRecordList.setMediaViewType("WIDE");
                pathTo = path + "photo" + "/WIDE/";
            } else if (ftpFileName.contains("Z")) {
                //info.mediaRecordList.setMediaViewType("ZOOM");
                pathTo = path + "photo" + "/ZOOM/";
            } else {
                pathTo = path + "photo/V/";
            }
        } else if (ftpFileName.toLowerCase().contains("mp4")) {
            //info.mediaRecordList.setMediaType("video");
            if (ftpFileName.contains("V")) {
                //info.mediaRecordList.setMediaViewType("SCRN");
                pathTo = path + "video" + "/V/";
            } else if (ftpFileName.contains("T")) {
                //info.mediaRecordList.setMediaViewType("THRM");
                pathTo = path + "video" + "/THRM/";
            } else if (ftpFileName.contains("W")) {
                //info.mediaRecordList.setMediaViewType("WIDE");
                pathTo = path + "video" + "/WIDE/";
            } else if (ftpFileName.contains("Z")) {
                //info.mediaRecordList.setMediaViewType("ZOOM");
                pathTo = path + "video" + "/ZOOM/";
            } else if (ftpFileName.contains("S")) {
                //info.mediaRecordList.setMediaViewType("ZOOM");
                pathTo = path + "video" + "/S/";
            } else {
                pathTo = path + "video/SINGlELEN/";
            }
        } else {
            pathTo = path;
        }
        return pathTo;

    }


    /**
     * 设置工作路径
     *
     * @param dir
     * @return
     */
    private boolean setWorkingDirectory(String dir) {
        lastDir = dir;
        if (!is_connected) {
            System.out.println("--连接失败");
            XLogUtil.INSTANCE.e("FtpConnectionUpload", "setWorkingDirectory: 连接失败");
            return false;
        }
        //如果目录不存在创建目录
        try {
            if (createDirecroty(dir)) {
                return ftp.changeWorkingDirectory(dir);
            }
        } catch (IOException e) {
            XLogUtil.INSTANCE.e("FtpConnectionUpload", "createDirecroty: " + e.getLocalizedMessage());
            e.printStackTrace();
        }

        return false;
    }

    /**
     * 是否连接
     *
     * @return
     */
    public boolean isConnected() {
        return is_connected;
    }

    /**
     * 创建目录
     *
     * @param remote
     * @return
     * @throws IOException
     */
    private boolean createDirecroty(String remote) throws IOException {

        //if (!remote.contains("/")){return true;}
        String directory = remote.substring(0, remote.lastIndexOf("/") + 1);
        // 如果远程目录不存在，则递归创建远程服务器目录
        if (!directory.equalsIgnoreCase("/") && !ftp.changeWorkingDirectory(directory)) {
            int start = 0;
            int end;
            if (directory.startsWith("/")) {
                start = 1;
            }
            end = directory.indexOf("/", start);
            do {
                String subDirectory = remote.substring(start, end);
                if (!ftp.changeWorkingDirectory(subDirectory)) {
                    if (ftp.makeDirectory(subDirectory)) {
                        ftp.changeWorkingDirectory(subDirectory);
                    } else {
                        XLogUtil.INSTANCE.e("FtpConnectionUpload", "createDirecroty error: " + subDirectory);
                        System.out.println("mack directory error :/" + subDirectory);
                        return false;
                    }
                }
                start = end + 1;
                end = directory.indexOf("/", start);
                // 检查所有目录是否创建完毕
            } while (end > start);
        }
        return true;
    }


}
