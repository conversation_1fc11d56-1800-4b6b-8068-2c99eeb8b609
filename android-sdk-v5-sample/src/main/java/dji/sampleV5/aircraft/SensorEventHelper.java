package dji.sampleV5.aircraft;

import android.content.Context;
import android.hardware.Sensor;
import android.hardware.SensorEvent;
import android.hardware.SensorEventListener;
import android.hardware.SensorManager;
import android.view.Display;
import android.view.Surface;
import android.view.WindowManager;

import java.util.ArrayList;
import java.util.List;

public class SensorEventHelper implements SensorEventListener {
    private SensorManager mSensorManager;
    private Sensor mSensor;
    private Context mContext;
    private List<RotationListener> listenerList = new ArrayList<>();

    public SensorEventHelper(Context context) {
        mContext = context;
        mSensorManager = (SensorManager) context
                .getSystemService(Context.SENSOR_SERVICE);
        mSensor = mSensorManager.getDefaultSensor(Sensor.TYPE_ORIENTATION);
    }

    public void init() {
        mSensorManager.registerListener(this, mSensor,
                SensorManager.SENSOR_DELAY_NORMAL);
    }

    public void release() {
        mSensorManager.unregisterListener(this, mSensor);
    }

    public void addRotationListener(RotationListener listener) {
        this.listenerList.add(listener);
    }

    public void removeRotationListener(RotationListener listener) {
        this.listenerList.remove(listener);
    }

    @Override
    public void onAccuracyChanged(Sensor sensor, int accuracy) {}

    private float lstX;
    @Override
    public void onSensorChanged(SensorEvent event) {
        switch (event.sensor.getType()) {
            case Sensor.TYPE_ORIENTATION: {
                float x = event.values[0];

                if (Math.abs(x - lstX) > 2f) {
                    for (RotationListener aListenerList : listenerList) {
                        aListenerList.onRotationChanged(pro(x + getScreenRotationOnPhone(mContext)));
                    }
                    lstX = x;
                }
            }
        }
    }

    private float pro(float x) {
        x %= 360.0F;
        if (x > 180.0F) {
            x -= 360.0F;
        } else if (x < -180.0F) {
            x += 360.0F;
        }
        return 360 - x;
    }

    private static int getScreenRotationOnPhone(Context context) {
        Display display = ((WindowManager) context
                .getSystemService(Context.WINDOW_SERVICE)).getDefaultDisplay();

        switch (display.getRotation()) {
            case Surface.ROTATION_0:
                return 0;
            case Surface.ROTATION_90:
                return 90;
            case Surface.ROTATION_180:
                return 180;
            case Surface.ROTATION_270:
                return -90;
        }
        return 0;
    }

    public interface RotationListener {
        void onRotationChanged(float rotation);
    }
}
