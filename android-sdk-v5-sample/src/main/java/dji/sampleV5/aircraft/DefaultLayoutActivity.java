/*
 * Copyright (c) 2018-2020 DJI
 *
 * Permission is hereby granted, free of charge, to any person obtaining a copy
 * of this software and associated documentation files (the "Software"), to deal
 * in the Software without restriction, including without limitation the rights
 * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
 * copies of the Software, and to permit persons to whom the Software is
 * furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in all
 * copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
 * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
 * SOFTWARE.
 *
 */

package dji.sampleV5.aircraft;

import static dji.sampleV5.aircraft.mvvm.ext.CommExtKt.gpsToAmap;
import static dji.sampleV5.aircraft.mvvm.ext.PopupExtKt.showMapTypePopup;

import android.annotation.SuppressLint;
import android.app.AlertDialog;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.os.Build;
import android.os.Bundle;
import android.os.SystemClock;
import android.text.TextUtils;
import android.util.DisplayMetrics;
import android.util.Log;
import android.util.TypedValue;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;
import android.view.animation.Animation;
import android.view.animation.Transformation;
import android.widget.ListView;
import android.widget.PopupWindow;
import android.widget.RelativeLayout;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.widget.AppCompatImageView;
import androidx.appcompat.widget.AppCompatTextView;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.core.view.GravityCompat;
import androidx.core.widget.PopupWindowCompat;
import androidx.databinding.DataBindingUtil;
import androidx.drawerlayout.widget.DrawerLayout;
import androidx.lifecycle.ViewModelProvider;

import com.alibaba.fastjson.JSONArray;
import com.amap.api.maps.AMap;
import com.amap.api.maps.CameraUpdateFactory;
import com.amap.api.maps.TextureMapView;
import com.amap.api.maps.model.BitmapDescriptorFactory;
import com.amap.api.maps.model.CameraPosition;
import com.amap.api.maps.model.LatLng;
import com.amap.api.maps.model.Marker;
import com.amap.api.maps.model.MarkerOptions;
import com.amap.api.maps.model.Polygon;
import com.amap.api.maps.model.PolygonOptions;
import com.amap.api.navi.AmapNaviType;
import com.amap.api.navi.model.NaviLatLng;
import com.hjq.toast.Toaster;
import com.lxj.xpopup.XPopup;
import com.lxj.xpopup.core.BasePopupView;
import com.lxj.xpopup.enums.PopupAnimation;
import com.lxj.xpopup.util.XPopupUtils;

import org.greenrobot.eventbus.Subscribe;
import org.json.JSONException;
import org.json.JSONObject;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import dji.sampleV5.aircraft.data.preference.SpUtil;
import dji.sampleV5.aircraft.databinding.ActivityDefaultLayoutBinding;
import dji.sampleV5.aircraft.event.Event;
import dji.sampleV5.aircraft.event.Events;
import dji.sampleV5.aircraft.lbs.LocationService;
import dji.sampleV5.aircraft.lbs.MapController;
import dji.sampleV5.aircraft.lbs.MapIndex;
import dji.sampleV5.aircraft.lbs.MapPainter;
import dji.sampleV5.aircraft.lbs.MapServiceFactory;
import dji.sampleV5.aircraft.lbs.bean.AppLatLng;
import dji.sampleV5.aircraft.mvvm.event.LiveDataEvent;
import dji.sampleV5.aircraft.mvvm.ext.CommExtKt;
import dji.sampleV5.aircraft.mvvm.ext.StorageExtKt;
import dji.sampleV5.aircraft.mvvm.ext.ViewExtKt;
import dji.sampleV5.aircraft.mvvm.key.ValueKey;
import dji.sampleV5.aircraft.mvvm.net.response.LocationInfoBean;
import dji.sampleV5.aircraft.mvvm.net.response.MissionInfoBean;
import dji.sampleV5.aircraft.mvvm.net.response.SiteAreaListBean;
import dji.sampleV5.aircraft.mvvm.ui.activity.multisite.MultisiteViewModel;
import dji.sampleV5.aircraft.mvvm.ui.activity.navi.AMapNaviActivity;
import dji.sampleV5.aircraft.mvvm.ui.activity.realtime.RealTimeViewModel;
import dji.sampleV5.aircraft.mvvm.util.AMapLocationUtil;
import dji.sampleV5.aircraft.mvvm.util.CoordinateConverterUtil;
import dji.sampleV5.aircraft.mvvm.util.MapOverlayUtil;
import dji.sampleV5.aircraft.mvvm.util.XLogUtil;
import dji.sampleV5.aircraft.mvvm.widget.camera.CameraThermalPaletteWidget;
import dji.sampleV5.aircraft.mvvm.widget.popup.MapTypePopup;
import dji.sampleV5.aircraft.mvvm.widget.popup.SiteLocationPopup;
import dji.sampleV5.aircraft.mvvm.widget.popup.TaskChecklistPopup;
import dji.sampleV5.aircraft.net.bean.FocusParam;
import dji.sampleV5.aircraft.net.bean.LocateInfo;
import dji.sampleV5.aircraft.net.bean.MissionJson;
import dji.sampleV5.aircraft.net.bean.NetConfig;
import dji.sampleV5.aircraft.page.fly.AircraftFragmentManager;
import dji.sampleV5.aircraft.page.fly.controller.MissionController;
import dji.sampleV5.aircraft.page.fly.controller.PopupWindowController;
import dji.sampleV5.aircraft.page.fly.controller.RtmpController;
import dji.sampleV5.aircraft.page.fly.controller.VirtualStickController;
import dji.sampleV5.aircraft.page.fly.setting.AircraftSettingFragment;
import dji.sampleV5.aircraft.util.GCJ02_WGS84;
import dji.sampleV5.aircraft.util.KeyOperateUtil;
import dji.sampleV5.aircraft.util.ToastUtil;
import dji.sampleV5.aircraft.util.phone.DensityUtil;
import dji.sampleV5.aircraft.util.phone.ImmerseUtil;
import dji.sampleV5.aircraft.view.SelectedOptionPanel;
import dji.sampleV5.aircraft.view.ThermalAreaMeterView;
import dji.sdk.keyvalue.key.CameraKey;
import dji.sdk.keyvalue.key.DJIKey;
import dji.sdk.keyvalue.key.FlightControllerKey;
import dji.sdk.keyvalue.key.GimbalKey;
import dji.sdk.keyvalue.key.KeyTools;
import dji.sdk.keyvalue.value.camera.CameraExposureCompensation;
import dji.sdk.keyvalue.value.camera.CameraExposureMode;
import dji.sdk.keyvalue.value.camera.CameraFocusMode;
import dji.sdk.keyvalue.value.camera.CameraISO;
import dji.sdk.keyvalue.value.camera.CameraShutterSpeed;
import dji.sdk.keyvalue.value.camera.CameraStorageInfo;
import dji.sdk.keyvalue.value.camera.CameraStorageInfos;
import dji.sdk.keyvalue.value.camera.CameraStorageLocation;
import dji.sdk.keyvalue.value.camera.CameraStreamSettingsInfo;
import dji.sdk.keyvalue.value.camera.CameraThermalPalette;
import dji.sdk.keyvalue.value.camera.CameraVideoStreamSourceType;
import dji.sdk.keyvalue.value.camera.TapZoomMode;
import dji.sdk.keyvalue.value.camera.ThermalGainMode;
import dji.sdk.keyvalue.value.camera.ZoomTargetPointInfo;
import dji.sdk.keyvalue.value.common.CameraLensType;
import dji.sdk.keyvalue.value.common.ComponentIndexType;
import dji.sdk.keyvalue.value.common.DoublePoint2D;
import dji.sdk.keyvalue.value.common.EmptyMsg;
import dji.sdk.keyvalue.value.common.LocationCoordinate2D;
import dji.sdk.keyvalue.value.flightcontroller.FlightControlAuthorityChangeReason;
import dji.sdk.keyvalue.value.gimbal.GimbalAngleRotation;
import dji.sdk.keyvalue.value.product.ProductType;
import dji.v5.common.callback.CommonCallbacks;
import dji.v5.common.error.IDJIError;
import dji.v5.common.video.channel.VideoChannelType;
import dji.v5.common.video.interfaces.IVideoChannel;
import dji.v5.common.video.interfaces.VideoChannelStateChangeListener;
import dji.v5.manager.KeyManager;
import dji.v5.manager.aircraft.rtk.RTKCenter;
import dji.v5.manager.aircraft.virtualstick.VirtualStickManager;
import dji.v5.manager.aircraft.virtualstick.VirtualStickState;
import dji.v5.manager.aircraft.virtualstick.VirtualStickStateListener;
import dji.v5.manager.datacenter.MediaDataCenter;
import dji.v5.manager.interfaces.ICameraStreamManager;
import dji.v5.network.DJINetworkManager;
import dji.v5.network.IDJINetworkStatusListener;
import dji.v5.utils.common.JsonUtil;
import dji.v5.utils.common.LogUtils;
import dji.v5.utils.common.NetworkUtils;
import dji.v5.ux.SyncDataLiveEvent;
import dji.v5.ux.accessory.RTKStartServiceHelper;
import dji.v5.ux.cameracore.widget.autoexposurelock.AutoExposureLockWidget;
import dji.v5.ux.cameracore.widget.cameracontrols.CameraControlsWidget;
import dji.v5.ux.cameracore.widget.focusexposureswitch.FocusExposureSwitchWidget;
import dji.v5.ux.cameracore.widget.focusmode.FocusModeWidget;
import dji.v5.ux.cameracore.widget.fpvinteraction.FPVInteractionWidget;
import dji.v5.ux.core.base.SchedulerProvider;
import dji.v5.ux.core.extension.ViewExtensions;
import dji.v5.ux.core.panel.systemstatus.SystemStatusListPanelWidget;
import dji.v5.ux.core.panel.topbar.TopBarPanelWidget;
import dji.v5.ux.core.util.CameraUtil;
import dji.v5.ux.core.util.DataProcessor;
import dji.v5.ux.core.widget.fpv.FPVWidget;
import dji.v5.ux.core.widget.hsi.PrimaryFlightDisplayWidget;
import dji.v5.ux.core.widget.setting.SettingWidget;
import dji.v5.ux.core.widget.systemstatus.SystemStatusWidget;
import dji.v5.ux.training.simulatorcontrol.SimulatorControlWidget;
import dji.v5.ux.visualcamera.CameraVisiblePanelWidget;
import io.reactivex.rxjava3.android.schedulers.AndroidSchedulers;
import io.reactivex.rxjava3.disposables.CompositeDisposable;

import kotlin.Unit;

import me.jessyan.autosize.internal.CancelAdapt;
import okhttp3.Call;
import okhttp3.Callback;
import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;

/**
 * Displays a sample layout of widgets similar to that of the various DJI apps.
 */
public class DefaultLayoutActivity extends AppCompatActivity implements CancelAdapt {

    private final String TAG = LogUtils.getTag(this);

    protected FPVWidget primaryFpvWidget;
    protected FPVWidget secondaryFPVWidget;
    protected RelativeLayout parentView;
    protected RelativeLayout rootView;
    protected SystemStatusListPanelWidget systemStatusListPanelWidget;
    protected SimulatorControlWidget simulatorControlWidget;
    protected CameraVisiblePanelWidget visualCameraPanel;
    protected CameraControlsWidget cameraControlsWidget;
    protected AutoExposureLockWidget autoExposureLockWidget;
    public FPVInteractionWidget fpvInteractionWidget;
    protected FocusModeWidget focusModeWidget;
    protected FocusExposureSwitchWidget focusExposureSwitchWidget;
    private ComponentIndexType lastDevicePosition = ComponentIndexType.UNKNOWN;
    private CameraLensType lastLensType = CameraLensType.UNKNOWN;
    protected PrimaryFlightDisplayWidget pfvFlightDisplayWidget;
    protected View cameraConfigBackground;
    protected SettingWidget settingWidget;
    private int widgetHeight;
    private int widgetWidth;
    private int widgetMargin;
    private int deviceWidth;
    private int deviceHeight;
    private CompositeDisposable compositeDisposable;
    private final DataProcessor<CameraSource> cameraSourceProcessor = DataProcessor.create(new CameraSource(ComponentIndexType.UNKNOWN,
            CameraLensType.UNKNOWN));

    public MapController mMapController;
    protected TextureMapView mapView;
    private MapPainter mMapPainter;
    private SensorEventHelper mSensorHelper;
    private int bigWidth;
    private int bigHeight;
    private int smallWidth;
    private int smallHeight;
    public static final int SMALL_WINDOW_RATIO = 4;
    private ActivityDefaultLayoutBinding binding;
    public int currentIndex = Events.IndexEvent.INDEX_MAP;
    private MissionController missionController;
    private PopupWindow cameraModePopupWindow;
    private AircraftFragmentManager mAircraftFragmentManager;
    private PopupWindow mPopupWindow;
    private RtmpController rtmpController;
    private VirtualStickController virtualStickController;
    private boolean isActiveSelect = false; //判断是否是主动切换相机镜头
    private boolean isFPVNow = false;  //判断当前主视角是否是fpv视角
    private String currentCameraMode;
    private VideoChannelStateChangeListener primaryChannelStateListener = null;
    private VideoChannelStateChangeListener secondaryChannelStateListener = null;
    private ThermalAreaMeterView thermalAreaMeterView;
    private AppLatLng aircraftLocation;//获取无人机实时坐标存储起来，方面后面找飞机

    private int manualMaxValue = 0;
    private int manualMinValue = 0;
    private LocationInfoBean locationInfoBean = new LocationInfoBean();
    private List<LocationInfoBean.MainLocation.SubLocation> subLocationList = new ArrayList<LocationInfoBean.MainLocation.SubLocation>();
    private MultisiteViewModel multiViewModel;
    private RealTimeViewModel realTimeView;
    private List<Marker> subMarkerList = new ArrayList<>();
    private List<Polygon> allSiteArea = new ArrayList<>();
    private Boolean isShowSubLocation = LiveDataEvent.INSTANCE.getShowSubLocationId().getValue();
    private CameraThermalPaletteWidget thermalPaletteWidget;

    private CameraExposureMode cameraExposureMode;
    // iso
    private DJIKey<List<CameraISO>> isoRangeKey;
    private String iso;
    private List<CameraISO> isoList;

    // 曝光模式
    private DJIKey<CameraExposureMode> exposureModeKey;
    private DJIKey<List<CameraExposureCompensation>> evRangeKey;
    private String evValue;
    private List<CameraExposureCompensation> evList;

    // 快门
    private DJIKey<List<CameraShutterSpeed>> shutterSpeedRangeKey;
    private String shutterSpeed;
    private List<CameraShutterSpeed> shutterSpeedList;
    private View[] exposureModeViews;
    // 当前选中的按钮索引（-1表示没有选中）
    private int currentSelectedButtonIndex = -1;

    private static List<String> loadMapNames = new ArrayList<>();
    private static List<String> downloadings = new ArrayList<>();

    private BasePopupView checkPopup;

    public ActivityDefaultLayoutBinding getBinding() {
        return binding;
    }

    private DrawerLayout mDrawerLayout;
    private double tuoQiuHeight = -9999;
    private boolean isOpenCheckList = false;
    private boolean isShowMissionNavi = false;
    private boolean isMissionFinished = false;
    private LayoutInflater inflater;

    private MapTypePopup typePopup;
    private LocationInfoBean.MainLocation.SubLocation subLocation;
    private double defaultThemalZoom = 1;
    private final IDJINetworkStatusListener networkStatusListener = isNetworkAvailable -> {
        if (isNetworkAvailable) {
            LogUtils.d(TAG, "isNetworkAvailable=" + true);
            RTKStartServiceHelper.INSTANCE.startRtkService(false);
        }
    };

    private final ICameraStreamManager.AvailableCameraUpdatedListener availableCameraUpdatedListener = new ICameraStreamManager.AvailableCameraUpdatedListener() {
        @Override
        public void onAvailableCameraUpdated(@NonNull List<ComponentIndexType> availableCameraList) {
            runOnUiThread(() -> updateFPVWidgetSource(availableCameraList));
        }

        @Override
        public void onCameraStreamEnableUpdate(@NonNull Map<ComponentIndexType, Boolean> cameraStreamEnableMap) {}
    };

    public AircraftFragmentManager getAircraftFragmentManager() {
        return this.mAircraftFragmentManager;
    }

    public MapPainter getmMapPainter() {
        return mMapPainter;
    }

    public void dismissPopupWindow() {
        if (mPopupWindow != null) {
            mPopupWindow.dismiss();
        }
    }

    public void showPopup(View anchor, ListView listView, int length) {
        if (listView != null) {
            if (length == 3) {
                mPopupWindow = new PopupWindow(listView, anchor.getWidth(), DensityUtil.px2dp(690));
            } else if (length == 2) {
                mPopupWindow = new PopupWindow(listView, anchor.getWidth(), DensityUtil.px2dp(460));
            } else if (length <= 1) {
                mPopupWindow = new PopupWindow(listView, anchor.getWidth(), DensityUtil.px2dp(230));
            } else {
                mPopupWindow = new PopupWindow(listView, anchor.getWidth(), DensityUtil.px2dp(900));
            }
            mPopupWindow.setFocusable(false);
            mPopupWindow.setBackgroundDrawable(new ColorDrawable());
            mPopupWindow.setOutsideTouchable(true);
            mPopupWindow.setFocusable(false);
            mPopupWindow.update();
            PopupWindowCompat.showAsDropDown(mPopupWindow, anchor, 0, -anchor.getHeight(), Gravity.CENTER);
            fullScreenImmersive(mPopupWindow.getContentView());
            mPopupWindow.setFocusable(true);
            mPopupWindow.update();
        }
    }

    public void showPopup1(View anchor, ListView listView, int length) {
        if (listView != null) {
            if (length == 3) {
                mPopupWindow = new PopupWindow(listView, anchor.getWidth(), DensityUtil.px2dp(600));
            } else if (length == 2) {
                mPopupWindow = new PopupWindow(listView, anchor.getWidth(), DensityUtil.px2dp(400));
            } else if (length <= 1) {
                mPopupWindow = new PopupWindow(listView, anchor.getWidth(), DensityUtil.px2dp(200));
            } else {
                mPopupWindow = new PopupWindow(listView, anchor.getWidth(), DensityUtil.px2dp(800));
            }
            mPopupWindow.setFocusable(false);
            mPopupWindow.setBackgroundDrawable(new ColorDrawable());
            mPopupWindow.setOutsideTouchable(true);
            mPopupWindow.setFocusable(false);
            mPopupWindow.update();
            PopupWindowCompat.showAsDropDown(mPopupWindow, anchor, 0, anchor.getHeight(), Gravity.CENTER);
            fullScreenImmersive(mPopupWindow.getContentView());
            mPopupWindow.setFocusable(true);
            mPopupWindow.update();
        }
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        Log.e(TAG, String.format("onCreate updateViewLevel: %d", currentIndex));
        binding = DataBindingUtil.setContentView(this, R.layout.activity_default_layout);
        ImmerseUtil.fullScreen(this);
        inflater = LayoutInflater.from(this);
        widgetHeight = (int) getResources().getDimension(R.dimen.uxsdk_mini_map_height);
        widgetWidth = (int) getResources().getDimension(R.dimen.uxsdk_mini_map_width);
        widgetMargin = (int) getResources().getDimension(R.dimen.uxsdk_mini_map_margin);
        // 初始化 ViewModel
        multiViewModel = new ViewModelProvider(this).get(MultisiteViewModel.class);
        realTimeView = new ViewModelProvider(this).get(RealTimeViewModel.class);
        exposureModeViews = new View[]{
                binding.pvCameraIso,
                binding.pvCameraShutter,
                binding.pvCameraEv
        };
        DisplayMetrics displayMetrics = getResources().getDisplayMetrics();
        deviceHeight = displayMetrics.heightPixels;
        deviceWidth = displayMetrics.widthPixels;

        // Setup top bar state callbacks
        TopBarPanelWidget topBarPanel = findViewById(R.id.panel_top_bar);
        SystemStatusWidget systemStatusWidget = topBarPanel.getSystemStatusWidget();
        if (systemStatusWidget != null) {
            systemStatusWidget.setOnClickListener(v -> ViewExtensions.toggleVisibility(systemStatusListPanelWidget));
        }

        mDrawerLayout = findViewById(R.id.drawer_view);

        settingWidget = topBarPanel.getSettingWidget();
        if (settingWidget != null) {
            settingWidget.setOnClickListener(v -> toggleRightDrawer());
        }
        primaryFpvWidget = findViewById(R.id.widget_primary_fpv);
        secondaryFPVWidget = findViewById(R.id.widget_secondary_fpv);
        if (DJIAircraftApplication.getInstance().getProductType() == ProductType.DJI_MAVIC_3_ENTERPRISE_SERIES) {
            secondaryFPVWidget.setVisibility(View.GONE);
        }
        primaryFpvWidget.setOnFPVStreamSourceListener((devicePosition, lensType) -> cameraSourceProcessor.onNext(new CameraSource(devicePosition, lensType)));

        fpvInteractionWidget = findViewById(R.id.widget_fpv_interaction);
        parentView = findViewById(R.id.root_view);
        rootView = findViewById(R.id.fly_page_main);
        systemStatusListPanelWidget = findViewById(R.id.widget_panel_system_status_list);
        simulatorControlWidget = findViewById(R.id.widget_simulator_control);
        cameraControlsWidget = findViewById(R.id.widget_camera_controls);
        visualCameraPanel = findViewById(dji.v5.ux.R.id.panel_visual_camera);
        focusModeWidget = findViewById(R.id.widget_focus_mode);
        focusExposureSwitchWidget = findViewById(R.id.widget_focus_exposure_switch);
        thermalPaletteWidget = findViewById(R.id.widget_thermal_palette);
        autoExposureLockWidget = findViewById(R.id.widget_auto_exposure_lock);
        initClickListener();

        MediaDataCenter.getInstance().getCameraStreamManager().addAvailableCameraUpdatedListener(availableCameraUpdatedListener);
        primaryFpvWidget.setOnFPVStreamSourceListener((devicePosition, lensType) -> cameraSourceProcessor.onNext(new CameraSource(devicePosition, lensType)));
        //小surfaceView放置在顶部，避免被大的遮挡
        secondaryFPVWidget.setSurfaceViewZOrderOnTop(true);
        secondaryFPVWidget.setSurfaceViewZOrderMediaOverlay(true);
        mAircraftFragmentManager = new AircraftFragmentManager(getSupportFragmentManager(), binding);
        MapOverlayUtil.INSTANCE.bindLifecycle(this);
        initMap(savedInstanceState);
        Event.register(this);
        rtmpController = new RtmpController(this);
        rtmpController.onConnected(true);
        missionController = new MissionController(this, rtmpController);
        missionController.onConnect(true);
        virtualStickController = new VirtualStickController(this);
        PopupWindowController.getInstance().setPopupWindowFromLeft(binding.popupWindowFromLeft);
        PopupWindowController.getInstance().setPopupWindowFromCenter(binding.viewMissionPanel.popupWindowCenter);

        thermalAreaMeterView = new ThermalAreaMeterView(this);
        binding.fpvWrapper.addView(thermalAreaMeterView);
        int viewWidth = DensityUtil.getScreenWidth();
        int viewHeight = DensityUtil.getScreenHeight();
        thermalAreaMeterView.startMeter(viewWidth, viewHeight);
        thermalAreaMeterView.setVisibility(View.GONE);
        // 界面首次创建时, 同步显示状态, 确保 map 显示在上层
        syncLayers();
        if (DJIAircraftApplication.getInstance().getProductType() == ProductType.M30_SERIES) {
            defaultThemalZoom = 2;
        } else {
            defaultThemalZoom = 1;
        }
        if (DJIAircraftApplication.getInstance().getProductType() != null) {
            KeyManager.getInstance().getValue(KeyTools.createKey(FlightControllerKey.KeyVirtualStickEnabled), new CommonCallbacks.CompletionCallbackWithParam<Boolean>() {
                @Override
                public void onSuccess(Boolean isVsEnable) {
                    if (isVsEnable != null) {
                        binding.rlVsTips.setVisibility(isVsEnable ? View.VISIBLE : View.GONE);
                        DJIAircraftApplication.getInstance().setVsEnable(isVsEnable);
                    }
                }

                @Override
                public void onFailure(@NonNull IDJIError error) {

                }
            });
        }

        VirtualStickManager.getInstance().setVirtualStickStateListener(new VirtualStickStateListener() {
            @Override
            public void onVirtualStickStateUpdate(@NonNull VirtualStickState stickState) {
                if (stickState != null) {
                    //ToastUtil.show("虚拟摇杆：" + stickState.isVirtualStickEnable());
                    binding.rlVsTips.setVisibility(stickState.isVirtualStickEnable() ? View.VISIBLE : View.GONE);
                    DJIAircraftApplication.getInstance().setVsEnable(stickState.isVirtualStickEnable());
                }
            }

            @Override
            public void onChangeReasonUpdate(@NonNull FlightControlAuthorityChangeReason reason) {

            }
        });
        isOpenCheckList = dji.v5.ux.core.util.SpUtil.getOpenCheckList();
        //加载离线任务
        LiveDataEvent.INSTANCE.getOfflineMissionInfo().observe(this, missionListInfo -> {
            if (missionListInfo != null) {
                XLogUtil.INSTANCE.d(TAG, "下发离线任务----------missionDetail:  " + CommExtKt.toJsonStr(missionListInfo));
                binding.zoomPanel.initZoom();
                changeDisplayMode(2);
                //延迟1秒
                ContextUtil.getHandler().postDelayed(() -> {
                    //设置默认红外变焦倍数
                    setThermalZoom();
                }, 1000);
                closeVMode();//关闭超分模式
                initThermalPalette();//初始化红外的色盘
                setThermalGainMode();//开启高增益模式
                isShowMissionNavi = dji.v5.ux.core.util.SpUtil.getNaviShow();
                binding.ivShowNaviLine.setSelected(isShowMissionNavi);
                if (isShowMissionNavi && currentIndex == 0) {
                    binding.ivShowNaviLine.setVisibility(View.VISIBLE);
                }
                isOpenCheckList = dji.v5.ux.core.util.SpUtil.getOpenCheckList();
                if (isOpenCheckList) {
                    if (checkPopup != null && checkPopup.isShow()) {
                        Toaster.show("重复下发，请关闭检查单后重新下发");
                        return;
                    }
                    showChecklistPopup(true, missionListInfo);

                } else {
                    ContextUtil.getHandler().postDelayed(new Runnable() {
                        @Override
                        public void run() {
                            missionController.TakeOffLineMission(missionListInfo);
                        }
                    }, 2000);
                }
            }
        });
        //showSyncRTKDataDialog();
        DJINetworkManager.getInstance().addNetworkStatusListener(networkStatusListener);
        //设置手动对焦值
        setManualValue();
        //从缓存里获取场站数据
        getAllLocation();
        //观察livedata
        observeLiveData();
        cameraInfoListener();
        //导航功能相关
        naviInfoListener();
        isShowMissionNavi = dji.v5.ux.core.util.SpUtil.getNaviShow();
        LiveDataEvent.INSTANCE.isMissionFinished().observe(this, isFinished -> {
            if (isFinished != null) {
                isMissionFinished = isFinished;
                if (isFinished) {
                    if (checkPopup != null) {
                        checkPopup.smartDismiss();
                        checkPopup = null;
                    }
                }
            }
        });
        realTimeView.getIssueTaskLiveData().observe(this, Toaster::show);
    }

    //显示任务检查单
    private void showChecklistPopup(boolean isOffline, Object missionInfo) {
        if (checkPopup != null) {
            checkPopup = null;
        }
        checkPopup = new XPopup.Builder(this)
                .isViewMode(true)
                .hasStatusBar(false)
                .hasNavigationBar(false)
                .autoFocusEditText(false)
                .popupAnimation(PopupAnimation.ScrollAlphaFromTop)
                .asCustom(new TaskChecklistPopup(this, lastDevicePosition, isOffline, missionInfo, () -> {
                    if (binding.btnCheckList.getVisibility() == View.GONE) {
                        binding.btnCheckList.setVisibility(View.VISIBLE);
                    }
                    return Unit.INSTANCE;
                }, (resetMission) -> {

                    if (isOffline) {
                        ContextUtil.getHandler().postDelayed(() -> missionController.TakeOffLineMission((MissionInfoBean) resetMission), 2000);
                    } else {
                        missionController.onRemoteMission((MissionJson) resetMission);
                    }
                    setPhotoType();
                    return Unit.INSTANCE;
                }));
        checkPopup.show();
    }

    //设置存储照片类型
    private void setPhotoType() {
        String photoType = dji.v5.ux.core.util.SpUtil.getSavePictureFormat();
        String[] formats = photoType.split(",");
        List<CameraVideoStreamSourceType> cameraVideoStreamSourceType = new ArrayList<>();
        for (String format : formats) {
            switch(format) {
                case "visable":
                    cameraVideoStreamSourceType.add(CameraVideoStreamSourceType.VISION_CAMERA);
                    break;
                case "zoom":
                    cameraVideoStreamSourceType.add(CameraVideoStreamSourceType.ZOOM_CAMERA);
                    break;
                case "ir":
                    cameraVideoStreamSourceType.add(CameraVideoStreamSourceType.INFRARED_CAMERA);
                    break;
                case "wide":
                    cameraVideoStreamSourceType.add(CameraVideoStreamSourceType.WIDE_CAMERA);
                    break;
            }
        }
        ProductType productType = DJIAircraftApplication.getInstance().getProductType();
        if (!photoType.isEmpty() && productType != null) {
            if (productType == ProductType.DJI_MAVIC_3_ENTERPRISE_SERIES) {
                CameraStreamSettingsInfo cameraStreamSettingsInfo = new CameraStreamSettingsInfo();
                cameraStreamSettingsInfo.setCameraVideoStreamSources(cameraVideoStreamSourceType);
                KeyManager.getInstance().setValue(KeyTools.createKey(CameraKey.KeyCaptureCameraStreamSettings), cameraStreamSettingsInfo, new CommonCallbacks.CompletionCallback() {
                    @Override
                    public void onSuccess() {
                        XLogUtil.INSTANCE.i(TAG, " 下发任务时设置照片存储类型成功");
                    }

                    @Override
                    public void onFailure(@NonNull IDJIError idjiError) {
                        XLogUtil.INSTANCE.i(TAG, " 下发任务时设置照片存储类型失败");
                    }
                });
            }
        }
    }

    private void naviInfoListener() {
        //监听无人机当前状态是否链接
        KeyManager.getInstance().listen(KeyTools.createKey(FlightControllerKey.KeyConnection), this, (aBoolean, isConnected) -> {
            if (isConnected == null) return;
            if (isConnected && currentIndex == 0) {
                binding.ivNaviToAircraft.setVisibility(View.VISIBLE);
            }
        });
        binding.ivNaviToAircraft.setOnClickListener(v -> {
            if (!NetworkUtils.isNetworkAvailable()) {
                Toaster.showShort("暂不支持离线导航！");
                return;
            }
            showNaviTypePopup();
        });
    }

    private void cameraInfoListener() {
        //相机参数相关
        binding.ivManual.setOnClickListener(v -> {
            closeAllPicView();
            currentSelectedButtonIndex = -1;
            if (cameraExposureMode == CameraExposureMode.MANUAL) {
                KeyOperateUtil.INSTANCE.setExposureMode(this, CameraExposureMode.PROGRAM, lastLensType, lastDevicePosition, (cameraExposureMode -> {
                    this.cameraExposureMode = cameraExposureMode;
                    updateExposureUI(cameraExposureMode);
                    return Unit.INSTANCE;
                }), djiError -> {
                    Toaster.show(djiError);
                    return Unit.INSTANCE;
                });
            } else if (cameraExposureMode == CameraExposureMode.PROGRAM) {
                KeyOperateUtil.INSTANCE.setExposureMode(this, CameraExposureMode.MANUAL, lastLensType, lastDevicePosition, (cameraExposureMode -> {
                    this.cameraExposureMode = cameraExposureMode;
                    updateExposureUI(cameraExposureMode);
                    return Unit.INSTANCE;
                }), djiError -> {
                    Toaster.show(djiError);
                    return Unit.INSTANCE;
                });
            }
        });
        //曝光锁监听
        autoExposureLockWidget.setOnAELockChangeListener(isLocked -> {
            if (ViewExtKt.isVisible(autoExposureLockWidget)) {
                updateEVMode(isLocked);
            }
        });
        //ISO点击监听
        binding.panelVisualCamera.getCameraConfigISOAndEIWidget().setOnClickListener(v -> {
            if (cameraExposureMode == CameraExposureMode.MANUAL) {
                showOnlyPickView(0);
            }
        });
        binding.panelVisualCamera.getCameraConfigISOAndEIWidget().setOnIsoValueChange(value -> {
            if (binding.pvCameraIso.getVisibility() == View.VISIBLE) {
                return;
            }
            if (cameraExposureMode == CameraExposureMode.MANUAL && !TextUtils.isEmpty(value)) {
                iso = value;
                getIsoValue();
            }
        });
        binding.pvCameraIso.addOnSelectedItemListener(index -> {
            if (isoList != null && !isoList.isEmpty()) {
                CameraISO currISO = isoList.get(index);
                if (isoList.contains(currISO)) {
                    KeyOperateUtil.INSTANCE.setCameraISOValue(DefaultLayoutActivity.this, currISO, lastDevicePosition, lastLensType, (() -> Unit.INSTANCE), onError -> {
                        Toaster.showShort(onError);
                        return Unit.INSTANCE;
                    });
                }
            }
            return Unit.INSTANCE;
        });

        //快门点击监听
        binding.panelVisualCamera.getCameraConfigShutterWidget().setOnClickListener(v -> {
            if (cameraExposureMode == CameraExposureMode.MANUAL) {
                showOnlyPickView(1);
            }
        });
        binding.panelVisualCamera.getCameraConfigShutterWidget().setOnShutterValueChange(value -> {
            if (binding.pvCameraShutter.getVisibility() == View.VISIBLE) {
                return;
            }
            if (cameraExposureMode == CameraExposureMode.MANUAL && !TextUtils.isEmpty(value)) {
                shutterSpeed = value;
                getShutterValue();
            }
        });
        binding.pvCameraShutter.addOnSelectedItemListener(index -> {
            if (shutterSpeedList != null && !shutterSpeedList.isEmpty()) {
                CameraShutterSpeed currShutter = shutterSpeedList.get(index);
                if (shutterSpeedList.contains(currShutter)) {
                    KeyOperateUtil.INSTANCE.setShutterSpeedValue(DefaultLayoutActivity.this, currShutter, lastDevicePosition, lastLensType, (() -> Unit.INSTANCE), onError -> {
                        Toaster.showShort(onError);
                        return Unit.INSTANCE;
                    });
                }
            }
            return Unit.INSTANCE;
        });
        //EV点击监听
        binding.panelVisualCamera.getCameraConfigEVWidget().setOnClickListener(v -> {
            if (cameraExposureMode == CameraExposureMode.PROGRAM) {
                showOnlyPickView(2);
            }
        });
        binding.panelVisualCamera.getCameraConfigEVWidget().setOnEVValueChange(value -> {
            if (binding.pvCameraEv.getVisibility() == View.VISIBLE) {
                return;
            }
            if (cameraExposureMode == CameraExposureMode.PROGRAM && !TextUtils.isEmpty(value)) {
                evValue = value;
                getEvValue();
            }
        });
        binding.pvCameraEv.addOnSelectedItemListener(index -> {
            if (evList != null && !evList.isEmpty()) {
                CameraExposureCompensation currEV = evList.get(index);
                if (evList.contains(currEV)) {
                    KeyOperateUtil.INSTANCE.setCameraExposureCompensationValue(DefaultLayoutActivity.this, currEV, lastDevicePosition, lastLensType, (() -> Unit.INSTANCE), onError -> {
                        Toaster.showShort(onError);
                        return Unit.INSTANCE;
                    });
                }
            }
            return Unit.INSTANCE;
        });
    }

    //获取ISO值
    private void getIsoValue() {
        isoRangeKey = KeyTools.createCameraKey(CameraKey.KeyISORange, lastDevicePosition, lastLensType);
        KeyManager.getInstance().getValue(isoRangeKey, new CommonCallbacks.CompletionCallbackWithParam<List<CameraISO>>() {
            @Override
            public void onSuccess(List<CameraISO> cameraISOS) {
                isoList = cameraISOS;
                updateIsoRange(cameraISOS);
            }

            @Override
            public void onFailure(@NonNull IDJIError idjiError) {
                XLogUtil.INSTANCE.d(TAG, "获取ISO值失败\t" + idjiError.errorCode() + "\t" + idjiError.description());
            }
        });
    }

    private void getShutterValue() {
        shutterSpeedRangeKey = KeyTools.createCameraKey(CameraKey.KeyShutterSpeedRange, lastDevicePosition, lastLensType);
        KeyManager.getInstance().getValue(shutterSpeedRangeKey, new CommonCallbacks.CompletionCallbackWithParam<List<CameraShutterSpeed>>() {
            @Override
            public void onSuccess(List<CameraShutterSpeed> cameraShutterSpeeds) {
                shutterSpeedList = cameraShutterSpeeds;
                updateShutterSpeedRange(cameraShutterSpeeds);
            }

            @Override
            public void onFailure(@NonNull IDJIError idjiError) {
                XLogUtil.INSTANCE.d(TAG, "获取快门值失败\t" + idjiError.errorCode() + "\t" + idjiError.description());
            }
        });
    }

    private void getEvValue() {
        evRangeKey = KeyTools.createCameraKey(CameraKey.KeyExposureCompensationRange, lastDevicePosition, lastLensType);
        KeyManager.getInstance().getValue(evRangeKey, new CommonCallbacks.CompletionCallbackWithParam<List<CameraExposureCompensation>>() {
            @Override
            public void onSuccess(List<CameraExposureCompensation> cameraExposureCompensations) {
                evList = cameraExposureCompensations;
                updateEvRange(cameraExposureCompensations);
            }

            @Override
            public void onFailure(@NonNull IDJIError idjiError) {
                XLogUtil.INSTANCE.d(TAG, "获取曝光补偿值失败\t" + idjiError.errorCode() + "\t" + idjiError.description());
            }
        });
    }

    /**
     * 更新ISO范围
     *
     * @param cameraISOS ISO列表
     */
    private void updateIsoRange(List<CameraISO> cameraISOS) {
        if (TextUtils.isEmpty(iso) || iso.equals("AUTO")) {
            return;
        }
        List<String> isoNameList;
        if (isoList != null && !isoList.isEmpty()) {
            isoNameList = new ArrayList<>();
            for (CameraISO iso : cameraISOS) {
                isoNameList.add(iso.name().replace("ISO_", ""));
            }
            if (!isoNameList.isEmpty()) {
                binding.pvCameraIso.setData(isoNameList);
                if (isoNameList.contains(iso)) {
                    binding.pvCameraIso.selectedItem(iso);
                } else {
                    binding.pvCameraIso.selectedEndItem();
                }
            }
        }
    }

    /**
     * 更新快门范围
     *
     * @param cameraShutterSpeeds
     */
    private void updateShutterSpeedRange(List<CameraShutterSpeed> cameraShutterSpeeds) {
        List<String> shutterSpeedNameList;
        if (cameraShutterSpeeds != null && !cameraShutterSpeeds.isEmpty()) {
            shutterSpeedNameList = new ArrayList<>();
            for (CameraShutterSpeed shutterSpeed : cameraShutterSpeeds) {
                shutterSpeedNameList.add(CameraUtil.shutterSpeedDisplayName(shutterSpeed));
            }
            if (!shutterSpeedNameList.isEmpty()) {
                binding.pvCameraShutter.setData(shutterSpeedNameList);
                if (shutterSpeedNameList.contains(shutterSpeed)) {
                    binding.pvCameraShutter.selectedItem(shutterSpeed);
                } else {
                    binding.pvCameraShutter.selectedEndItem();
                }
            }
        }
    }

    /**
     * 更新EV范围
     *
     * @param cameraExposureCompensations
     */
    private void updateEvRange(List<CameraExposureCompensation> cameraExposureCompensations) {
        List<String> evNameList;
        if (cameraExposureCompensations != null && !cameraExposureCompensations.isEmpty()) {
            evNameList = new ArrayList<>();
            for (CameraExposureCompensation ev : cameraExposureCompensations) {
                evNameList.add(CameraUtil.exposureValueDisplayName(ev));
            }
            if (!evNameList.isEmpty()) {
                binding.pvCameraEv.setData(evNameList);
                if (evNameList.contains(evValue)) {
                    binding.pvCameraEv.selectedItem(evValue);
                } else {
                    binding.pvCameraEv.selectedEndItem();
                }
            }
        }
    }

    /**
     * 初始化相机参数
     *
     * @param devicePosition
     * @param lensType
     */
    private void initCameraParam(ComponentIndexType devicePosition, CameraLensType lensType) {
        if (devicePosition == lastDevicePosition) {
            //相机曝光模式
            exposureModeKey = KeyTools.createCameraKey(CameraKey.KeyExposureMode, devicePosition, lensType);
            KeyManager.getInstance().cancelListen(exposureModeKey);
            KeyManager.getInstance().listen(exposureModeKey, this, (oldValue, cameraExposureMode) -> {
                this.cameraExposureMode = cameraExposureMode;
                updateExposureUI(cameraExposureMode);
                XLogUtil.INSTANCE.i(this.getClass().getName(), "updateExposureUI: " + cameraExposureMode);
            });
        }
    }

    private void updateExposureUI(CameraExposureMode cem) {
        if (cem == CameraExposureMode.PROGRAM) {
            binding.ivManual.setImageResource(R.drawable.selector_camera_exposure_auto);
            autoExposureLockWidget.setVisibility(View.VISIBLE);
            //ISO控件文字颜色
            binding.panelVisualCamera.getCameraConfigISOAndEIWidget().setISOAndEIValueTextColor(CommExtKt.getColorExt(dji.v5.ux.R.color.uxsdk_white_33_percent));
            binding.panelVisualCamera.getCameraConfigISOAndEIWidget().setISOAndEITitleTextColor(CommExtKt.getColorExt(dji.v5.ux.R.color.uxsdk_white_33_percent));
            //快门控件文字颜色
            binding.panelVisualCamera.getCameraConfigShutterWidget().setShutterValueTextColor(CommExtKt.getColorExt(dji.v5.ux.R.color.uxsdk_white_33_percent));
            binding.panelVisualCamera.getCameraConfigShutterWidget().setShutterTitleTextColor(CommExtKt.getColorExt(dji.v5.ux.R.color.uxsdk_white_33_percent));
            //曝光补偿控件文字颜色
            binding.panelVisualCamera.getCameraConfigEVWidget().setEnabled(true);
            binding.panelVisualCamera.getCameraConfigEVWidget().setEVValueTextColor(CommExtKt.getColorExt(dji.v5.ux.R.color.uxsdk_white));
            binding.panelVisualCamera.getCameraConfigEVWidget().setEVTitleTextColor(CommExtKt.getColorExt(dji.v5.ux.R.color.uxsdk_white));
        } else if (cem == CameraExposureMode.MANUAL) {
            autoExposureLockWidget.setVisibility(View.GONE);
            binding.ivManual.setImageResource(R.drawable.selector_camera_exposure_mode);
            binding.panelVisualCamera.getCameraConfigISOAndEIWidget().setISOAndEIValueTextColor(CommExtKt.getColorExt(dji.v5.ux.R.color.uxsdk_white));
            binding.panelVisualCamera.getCameraConfigISOAndEIWidget().setISOAndEITitleTextColor(CommExtKt.getColorExt(dji.v5.ux.R.color.uxsdk_white));

            binding.panelVisualCamera.getCameraConfigShutterWidget().setShutterValueTextColor(CommExtKt.getColorExt(dji.v5.ux.R.color.uxsdk_white));
            binding.panelVisualCamera.getCameraConfigShutterWidget().setShutterTitleTextColor(CommExtKt.getColorExt(dji.v5.ux.R.color.uxsdk_white));

            binding.panelVisualCamera.getCameraConfigEVWidget().setEVValueTextColor(CommExtKt.getColorExt(dji.v5.ux.R.color.uxsdk_white_33_percent));
            binding.panelVisualCamera.getCameraConfigEVWidget().setEVTitleTextColor(CommExtKt.getColorExt(dji.v5.ux.R.color.uxsdk_white_33_percent));
        }
    }

    private void updateEVMode(boolean isLock) {
        closeAllPicView();
        binding.panelVisualCamera.getCameraConfigEVWidget().setEnabled(!isLock);
        int widgetColor = isLock ? CommExtKt.getColorExt(dji.v5.ux.R.color.uxsdk_white_33_percent) : CommExtKt.getColorExt(dji.v5.ux.R.color.uxsdk_white);
        binding.panelVisualCamera.getCameraConfigEVWidget().setEVValueTextColor(widgetColor);
        binding.panelVisualCamera.getCameraConfigEVWidget().setEVTitleTextColor(widgetColor);
    }

    /**
     * 关闭所有View
     */
    private void closeAllPicView() {
        for (View v : exposureModeViews) {
            v.setVisibility(View.GONE);
        }
    }

    /**
     * 显示指定下标的View
     *
     * @param index
     */
    private void showOnlyPickView(int index) {
        // 检查当前按钮是否已经被选中
        if (currentSelectedButtonIndex == index) {
            // 如果已经选中，则恢复默认颜色并隐藏对应控件
            resetButtonColors(index);
            exposureModeViews[index].setVisibility(View.GONE);
            currentSelectedButtonIndex = -1;
        } else {
            // 如果当前按钮没有被选中，则切换颜色并显示对应控件
            resetButtonColors(currentSelectedButtonIndex); // 恢复其他按钮的颜色
            setButtonSelectedColor(index); // 设置当前按钮的颜色
            for (View v : exposureModeViews) {
                if (v.getId() == exposureModeViews[index].getId()) {
                    v.setVisibility(View.VISIBLE);
                } else {
                    v.setVisibility(View.GONE);
                }
            }
            currentSelectedButtonIndex = index; // 记录当前选中的按钮
        }
    }

    //恢复按钮的默认颜色
    private void resetButtonColors(int index) {
        switch (index) {
            case 0:
                binding.panelVisualCamera.getCameraConfigISOAndEIWidget().setISOAndEITitleTextColor(CommExtKt.getColorExt(dji.v5.ux.R.color.uxsdk_white));
                binding.panelVisualCamera.getCameraConfigISOAndEIWidget().setISOAndEIValueTextColor(CommExtKt.getColorExt(dji.v5.ux.R.color.uxsdk_white));
                break;
            case 1:
                binding.panelVisualCamera.getCameraConfigShutterWidget().setShutterTitleTextColor(CommExtKt.getColorExt(dji.v5.ux.R.color.uxsdk_white));
                binding.panelVisualCamera.getCameraConfigShutterWidget().setShutterValueTextColor(CommExtKt.getColorExt(dji.v5.ux.R.color.uxsdk_white));
                break;
            case 2:
                binding.panelVisualCamera.getCameraConfigEVWidget().setEVTitleTextColor(CommExtKt.getColorExt(dji.v5.ux.R.color.uxsdk_white));
                binding.panelVisualCamera.getCameraConfigEVWidget().setEVValueTextColor(CommExtKt.getColorExt(dji.v5.ux.R.color.uxsdk_white));
                break;
        }
    }

    //设置按钮为选中状态的颜色
    private void setButtonSelectedColor(int index) {
        switch (index) {
            case 0:
                binding.panelVisualCamera.getCameraConfigISOAndEIWidget().setISOAndEITitleTextColor(CommExtKt.getColorExt(dji.v5.ux.R.color.uxsdk_yellow_in_light));
                binding.panelVisualCamera.getCameraConfigISOAndEIWidget().setISOAndEIValueTextColor(CommExtKt.getColorExt(dji.v5.ux.R.color.uxsdk_yellow_in_light));
                break;
            case 1:
                binding.panelVisualCamera.getCameraConfigShutterWidget().setShutterTitleTextColor(CommExtKt.getColorExt(dji.v5.ux.R.color.uxsdk_yellow_in_light));
                binding.panelVisualCamera.getCameraConfigShutterWidget().setShutterValueTextColor(CommExtKt.getColorExt(dji.v5.ux.R.color.uxsdk_yellow_in_light));
                break;
            case 2:
                binding.panelVisualCamera.getCameraConfigEVWidget().setEVTitleTextColor(CommExtKt.getColorExt(dji.v5.ux.R.color.uxsdk_yellow_in_light));
                binding.panelVisualCamera.getCameraConfigEVWidget().setEVValueTextColor(CommExtKt.getColorExt(dji.v5.ux.R.color.uxsdk_yellow_in_light));
                break;
        }
    }

    private void observeLiveData() {
        multiViewModel.getSiteAreaListLiveData().observe(this, siteAreaListBeans -> {
            if (!siteAreaListBeans.isEmpty()) {
                //绘制子阵
                drawSubArea(siteAreaListBeans);
                if (isShowSubLocation) {
                    //绘制子阵编号
                    drawSubNumber(siteAreaListBeans);
                }
            } else {
                Toaster.show("暂无子阵信息");
            }
        });
        //观察子阵是否显示
        LiveDataEvent.INSTANCE.getShowSubLocationId().observe(this, isShow -> {
            isShowSubLocation = isShow;
            if (isShowSubLocation) {
                drawSubNumber(Objects.requireNonNull(multiViewModel.getSiteAreaListLiveData().getValue()));
            } else {
                clearSubMarkers();
            }
        });
        //观察同步椭球高状态
        SyncDataLiveEvent.INSTANCE.getSyncTuoQiuLiveData().observe(this, isSync -> {
            if (isSync) {
                syncTuoQiuData();
            }
        });
    }

    //选择导航方式
    private void showNaviTypePopup() {
        new XPopup.Builder(this)
                .isDestroyOnDismiss(true)
                .hasStatusBar(false)
                .hasNavigationBar(false)
                .hasShadowBg(false)
                .customHostLifecycle(getLifecycle())
                .borderRadius(XPopupUtils.dp2px(this, 15f))
                .asBottomList("请选择导航方式", new String[]{"驾车导航", "步行导航", "骑行导航"}, (position, text) -> {
                    switch (position) {
                        case 0:
                            startNavi(AmapNaviType.DRIVER);
                            break;
                        case 1:
                            startNavi(AmapNaviType.WALK);
                            break;
                        case 2:
                            startNavi(AmapNaviType.RIDE);
                            break;
                    }
                })
                .show();

    }

    NaviLatLng startLatLng, endLatLng;
    private void startNavi(AmapNaviType naviType) {
        try {
            AMapLocationUtil.INSTANCE.getLocation(this, (lat, lon) -> {
                startLatLng = new NaviLatLng(lat, lon);
            });
            if (aircraftLocation == null) {
                Toaster.showShort("无法获取无人机定位，导航失败！");
                return;
            }
            LocateInfo appLatLng = GCJ02_WGS84.wgs84_To_Gcj02(aircraftLocation.getLat(), aircraftLocation.getLng());
            endLatLng = new NaviLatLng(appLatLng.getLatitude(), appLatLng.getLongitude());
            Bundle bundle = new Bundle();
            bundle.putString("start_latlng", JsonUtil.toJson(startLatLng));
            bundle.putString("end_latlng", JsonUtil.toJson(endLatLng));
            bundle.putString("navi_type", JsonUtil.toJson(naviType));
            CommExtKt.toStartActivity(AMapNaviActivity.class, bundle);
        } catch (Exception e) {
            e.printStackTrace();
            XLogUtil.INSTANCE.e(TAG, "----导航异常：" + e.getMessage());
        }
    }

    private void toggleRightDrawer() {
        mDrawerLayout.openDrawer(GravityCompat.END);
    }

    @Override
    public void onBackPressed() {
        if (checkPopup != null && checkPopup.isShow()) {
            if (binding.btnCheckList.getVisibility() == View.GONE) {
                binding.btnCheckList.setVisibility(View.VISIBLE);
            }
            checkPopup.dismiss();
            return;
        }
        if (mDrawerLayout.isDrawerOpen(GravityCompat.END)) {
            mDrawerLayout.closeDrawers();
        } else if (mAircraftFragmentManager.onBackPressed()) {
            super.onBackPressed();
        }
    }

    private void initClickListener() {
        Log.e(TAG, "initClickListener: ");
        secondaryFPVWidget.setOnClickListener(v -> {
            Log.e(TAG, "swapVideoSource: ");
            swapVideoSource();
        });

        //initChannelStateListener();

        binding.cameraMode.setOnClickListener(this::showModeView);
        binding.setting.setOnClickListener(v -> {
            Bundle bundle = new Bundle();
            bundle.putInt("mode", AircraftSettingFragment.TAB_AIRCRAFT);
            getAircraftFragmentManager().addFragment(AircraftFragmentManager.SETTING, bundle);
        });

        binding.closeVs.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                virtualStickController.disableVS();
            }
        });

        binding.btnSiteChoose.setOnClickListener(v -> {
            showSitePopup();
        });
        binding.btnCheckList.setOnClickListener(v -> {
            if (checkPopup != null) {
                checkPopup.show();
            }
        });
        binding.ivShowNaviLine.setOnClickListener(v -> {
            isShowMissionNavi = !isShowMissionNavi;
            binding.ivShowNaviLine.setSelected(isShowMissionNavi);
            LiveDataEvent.INSTANCE.isShowMissionNavi().postValue(isShowMissionNavi);
            dji.v5.ux.core.util.SpUtil.setNaviShow(isShowMissionNavi);
        });
        binding.btnMapType.setOnClickListener( v -> {
            if (typePopup.isShow()) {
                typePopup.dismiss();
            }else {
                showMapTypePopup(this, typePopup, binding.btnMapType);
            }
        });
    }

    private void showSitePopup() {
        new XPopup.Builder(this)
                .isViewMode(true)
                .hasStatusBar(true)
                .popupWidth((int) (getResources().getDisplayMetrics().widthPixels / 2.5f))
                .offsetX(binding.btnSiteChoose.getWidth() + 20)
                .offsetY(-binding.btnSiteChoose.getHeight())
                .atView(binding.btnSiteChoose)
                .asCustom(new SiteLocationPopup(this, subLocationList, item -> {
                    binding.btnSiteChoose.setText(item.getLocation());
                    binding.btnSiteChoose.setCompoundDrawablePadding((int) TypedValue.applyDimension(TypedValue.COMPLEX_UNIT_DIP, 10, getResources().getDisplayMetrics()));
                    clearSubMarkers();
                    clearSiteArea();
                    multiViewModel.getSiteAreaList(new ArrayList<>(Collections.singletonList(item.getId())));
                    // 加载所选子场站的正射图
                    LatLng oldLatLng = new LatLng(item.getLocationLatitude(), item.getLocationLongitude());
                    LatLng newLatLng = gpsToAmap(oldLatLng);
                    mapView.getMap().moveCamera(CameraUpdateFactory.changeLatLng(newLatLng));
                    subLocation = item;
                }))
                .show();
    }

    @SuppressLint("NewApi")
    private void drawSubArea(List<SiteAreaListBean> subAreaListBean) {
        for (SiteAreaListBean areaInfo : subAreaListBean) {
            for (List<List<Double>> coordinates : areaInfo.getFeatures().getGeometry().getCoordinates()) {
                List<LatLng> latLngList = coordinates.stream()
                        .map(latlngInfo -> gpsToAmap(new LatLng(latlngInfo.get(1), latlngInfo.get(0))))
                        .collect(Collectors.toList());

                if (!latLngList.isEmpty()) {
                    PolygonOptions polygonOptions = new PolygonOptions()
                            .addAll(latLngList)
                            .strokeWidth(4f)
                            .strokeColor(Color.YELLOW)
                            .fillColor(Color.argb(0, 0, 0, 0))
                            .zIndex(MapIndex.SUB_ARRAY_INDEX);
                    mapView.getMap().addPolygon(polygonOptions);
                }
            }
        }
    }

    @SuppressLint("NewApi")
    private void drawSubNumber(List<SiteAreaListBean> subAreaListBean) {
        ArrayList<MarkerOptions> optionsList = new ArrayList<>();
        for (SiteAreaListBean areaInfo : subAreaListBean) {
            LatLng convertedLatLng = CoordinateConverterUtil.INSTANCE.convertWGS84ToAMap(
                    new LatLng(areaInfo.getCentPointLat(), areaInfo.getCentPointLon()));

            String siteId = areaInfo.getFeatures().getProperties().getID();
            MarkerOptions markerOptions = new MarkerOptions()
                    .position(convertedLatLng)
                    .icon(BitmapDescriptorFactory.fromView(areaMarkerStyle(siteId)))
                    .anchor(0.5f, 0.5f)
                    .zIndex(MapIndex.SUB_ARRAY_NUM_INDEX);
            optionsList.add(markerOptions);
        }
        ArrayList<Marker> subMarkerList = mapView.getMap().addMarkers(optionsList, false);
        this.subMarkerList.addAll(subMarkerList);
    }


    private View areaMarkerStyle(String markerTitle) {
        View markerLayout = inflater.inflate(R.layout.area_marker_layout, null);
        AppCompatTextView markerTitleView = markerLayout.findViewById(R.id.marker_title);
        markerTitleView.setText(markerTitle);
        return markerLayout;
    }

    private void clearSubMarkers() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            subMarkerList.forEach(Marker::remove);
        } else {
            for (Marker marker : subMarkerList) {
                marker.remove();
            }
        }
        subMarkerList.clear(); // 清空标记点引用列表
    }

    private void clearSiteArea() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            allSiteArea.forEach(Polygon::remove);
        } else {
            for (Polygon siteArea : allSiteArea) {
                siteArea.remove();
            }
        }
        allSiteArea.clear(); // 清空标记点引用列表
    }

    @SuppressLint("NewApi")
    private void getAllLocation() {
        String locationInfoStr = StorageExtKt.getMmkv().getString(ValueKey.LOCATION_LIST_INFO, "");
        if (!locationInfoStr.isEmpty()) {
            locationInfoBean = JsonUtil.toBean(locationInfoStr, LocationInfoBean.class);
        }
        if (locationInfoBean != null) {
            locationInfoBean.getMainLocationList().stream()
                    .flatMap(mainLocation -> mainLocation.getSubLocationList().stream())
                    .forEach(subLocation -> subLocationList.add(subLocation));
        }
    }

    private void showModeView(View view) {
        if (cameraModePopupWindow == null) {
            cameraModePopupWindow = new PopupWindow();
            SelectedOptionPanel panel = new SelectedOptionPanel(this);
            panel.setCallback((index, result) -> {
                String text = "镜头/" + result;
                binding.cameraMode.setText(text);
                //tvMode.setText(result);
                onModeSelected(index);
                cameraModePopupWindow.dismiss();
            });

            //panel.setCurrentText(((Button) view).getText().toString().trim());

            List<String> data = new ArrayList<>();
            data.add("红外");
            data.add("变焦");
            data.add("广角");
            panel.setData(data);

            cameraModePopupWindow.setContentView(panel);
            cameraModePopupWindow.setOutsideTouchable(true);
            cameraModePopupWindow.setWidth(view.getMeasuredWidth());
            cameraModePopupWindow.setHeight(ViewGroup.LayoutParams.WRAP_CONTENT);
        }

        cameraModePopupWindow.showAsDropDown(view, 0, 0);
    }

    private void onModeSelected(int index) {
        isActiveSelect = true;
        CameraVideoStreamSourceType cameraVideoStreamSourceType = null;
        if (index == 0) {
            binding.tvMode.setText("红外");
            binding.zoomPanel.setVisibility(View.INVISIBLE);
            binding.thermalZoomPanel.setVisibility(View.VISIBLE);
            cameraVideoStreamSourceType = CameraVideoStreamSourceType.INFRARED_CAMERA;
        } else if (index == 1) {
            binding.tvMode.setText("变焦");
            binding.zoomPanel.setVisibility(View.VISIBLE);
            binding.thermalZoomPanel.setVisibility(View.INVISIBLE);
            cameraVideoStreamSourceType = CameraVideoStreamSourceType.ZOOM_CAMERA;
        } else if (index == 2) {
            binding.tvMode.setText("广角");
            binding.zoomPanel.setVisibility(View.INVISIBLE);
            binding.thermalZoomPanel.setVisibility(View.INVISIBLE);
            cameraVideoStreamSourceType = CameraVideoStreamSourceType.WIDE_CAMERA;
        }
        KeyManager.getInstance().setValue(KeyTools.createKey(CameraKey.KeyCameraVideoStreamSource, lastDevicePosition), cameraVideoStreamSourceType, new CommonCallbacks.CompletionCallback() {
            @Override
            public void onSuccess() {
                if (index == 0) {
                    binding.thermalZoomPanel.updateThermalZoomNum();
                } else if (index == 1) {
                    binding.zoomPanel.updateH20THybridZoomNum();
                }
            }

            @Override
            public void onFailure(@NonNull IDJIError error) {

            }
        });
    }

    private void getCameraMode() {
        KeyManager.getInstance().getValue(KeyTools.createKey(CameraKey.KeyCameraVideoStreamSource, lastDevicePosition), new CommonCallbacks.CompletionCallbackWithParam<CameraVideoStreamSourceType>() {
            @Override
            public void onSuccess(CameraVideoStreamSourceType cameraVideoStreamSourceType) {
                if (cameraVideoStreamSourceType != null) {
                    switch (cameraVideoStreamSourceType) {
                        case INFRARED_CAMERA:
                            binding.cameraMode.setText("镜头/红外");
                            binding.tvMode.setText("红外");
                            binding.zoomPanel.setVisibility(View.INVISIBLE);
                            binding.thermalZoomPanel.setVisibility(View.VISIBLE);
                            binding.thermalZoomPanel.updateThermalZoomNum();
                            DJIAircraftApplication.getInstance().setDisplayMode(2);
                            break;
                        case ZOOM_CAMERA:
                            binding.cameraMode.setText("镜头/变焦");
                            binding.tvMode.setText("变焦");
                            binding.thermalZoomPanel.setVisibility(View.INVISIBLE);
                            binding.zoomPanel.setVisibility(View.VISIBLE);
                            binding.zoomPanel.updateH20THybridZoomNum();
                            DJIAircraftApplication.getInstance().setDisplayMode(1);
                            break;
                        case WIDE_CAMERA:
                            binding.cameraMode.setText("镜头/广角");
                            binding.tvMode.setText("广角");
                            DJIAircraftApplication.getInstance().setDisplayMode(5);
                            break;
                    }
                }
            }

            @Override
            public void onFailure(@NonNull IDJIError error) {

            }
        });

        KeyManager.getInstance().listen(KeyTools.createKey(CameraKey.KeyCameraVideoStreamSource, lastDevicePosition), this, new CommonCallbacks.KeyListener<CameraVideoStreamSourceType>() {
            @Override
            public void onValueChange(@Nullable CameraVideoStreamSourceType oldValue, @Nullable CameraVideoStreamSourceType newValue) {
                Log.e(TAG, "onValueChange: " + newValue);
                if (newValue != null) {
                    if (newValue == CameraVideoStreamSourceType.ZOOM_CAMERA && DJIAircraftApplication.getInstance().getZoom() >= 5) {
                        updateManualUI();
                    } else {
                        binding.manualZoomWidget.setVisibility(View.GONE);
                    }
                    switch (newValue) {
                        case INFRARED_CAMERA:
                            DJIAircraftApplication.getInstance().setDisplayMode(2);
                            if (!isActiveSelect) {  //如果不是主动切换镜头的，说明是飞控系统触发的，我们要更新一下界面内容
                                binding.cameraMode.setText("镜头/红外");
                                binding.tvMode.setText("红外");
                                binding.zoomPanel.setVisibility(View.GONE);
                                binding.thermalZoomPanel.setVisibility(View.VISIBLE);
                            }
                            isActiveSelect = false;
                            break;
                        case ZOOM_CAMERA:
                            DJIAircraftApplication.getInstance().setDisplayMode(1);
                            if (!isActiveSelect) {  //如果不是主动切换镜头的，说明是飞控系统触发的，我们要更新一下界面内容
                                binding.cameraMode.setText("镜头/变焦");
                                binding.tvMode.setText("变焦");
                                binding.zoomPanel.setVisibility(View.VISIBLE);
                                binding.thermalZoomPanel.setVisibility(View.GONE);
                                binding.zoomPanel.updateH20THybridZoomNum();
                            }
                            isActiveSelect = false;
                            break;
                        case WIDE_CAMERA:
                            DJIAircraftApplication.getInstance().setDisplayMode(5);
                            if (!isActiveSelect) {  //如果不是主动切换镜头的，说明是飞控系统触发的，我们要更新一下界面内容
                                binding.cameraMode.setText("镜头/广角");
                                binding.tvMode.setText("广角");
                                binding.zoomPanel.setVisibility(View.GONE);
                                binding.thermalZoomPanel.setVisibility(View.GONE);
                            }
                            isActiveSelect = false;
                            break;
                    }
                }
            }
        });

        KeyManager.getInstance().listen(KeyTools.createCameraKey(CameraKey.KeyCameraZoomRatios, lastDevicePosition, CameraLensType.CAMERA_LENS_ZOOM), this, new CommonCallbacks.KeyListener<Double>() {
            @Override
            public void onValueChange(@Nullable Double oldValue, @Nullable Double newValue) {
                Log.e(TAG, "onValueChange: " + newValue);
                if (newValue != null) {
                    binding.zoomPanel.setRatios(newValue);
                    DJIAircraftApplication.getInstance().setZoom(newValue.intValue());
                    getManualZoomRange();
                    DoublePoint2D doublePoint2D = new DoublePoint2D(Double.parseDouble("0.5"), Double.parseDouble("0.5"));
                    setAutoFocus(doublePoint2D);
                    if (newValue.intValue() >= 5 && DJIAircraftApplication.getInstance().getDisplayMode() == 1) {
                        updateManualUI();
                    } else {
                        binding.manualZoomWidget.setVisibility(View.GONE);
                    }
                }
            }
        });

        KeyManager.getInstance().listen(KeyTools.createCameraKey(CameraKey.KeyThermalZoomRatios, lastDevicePosition, CameraLensType.CAMERA_LENS_THERMAL), this, new CommonCallbacks.KeyListener<Double>() {
            @Override
            public void onValueChange(@Nullable Double oldValue, @Nullable Double newValue) {
                Log.e(TAG, "onValueChange: " + newValue);
                if (newValue != null) {
                    binding.thermalZoomPanel.setRatios(newValue);
                    //DJIAircraftApplication.getInstance().setZoom(newValue.intValue());
                }
            }
        });

        KeyManager.getInstance().listen(KeyTools.createKey(CameraKey.KeyCameraStorageInfos, lastDevicePosition), this, new CommonCallbacks.KeyListener<CameraStorageInfos>() {
            @Override
            public void onValueChange(@Nullable CameraStorageInfos oldValue, @Nullable CameraStorageInfos cameraStorageInfos) {
                if (cameraStorageInfos != null) {
                    CameraStorageInfo sdcardInfo = cameraStorageInfos.getCameraStorageInfoByLocation(CameraStorageLocation.SDCARD);
                    if (sdcardInfo != null) {
                        DJIAircraftApplication.getInstance().setLeftStorageCapacity(sdcardInfo.getStorageLeftCapacity());
                        DJIAircraftApplication.getInstance().setStorageCapacity(sdcardInfo.getStorageCapacity());
                    }
                }
            }
        });

        KeyManager.getInstance().listen(KeyTools.createKey(FlightControllerKey.KeyAircraftLocation), this, new CommonCallbacks.KeyListener<LocationCoordinate2D>() {
            @Override
            public void onValueChange(@Nullable LocationCoordinate2D oldValue, @Nullable LocationCoordinate2D newValue) {
                if (newValue != null) {
                    if (aircraftLocation == null) {
                        aircraftLocation = new AppLatLng();
                    }
                    aircraftLocation.setLat(newValue.getLatitude());
                    aircraftLocation.setLng(newValue.getLongitude());
                    SpUtil.setAircraftLastLocation(aircraftLocation);
                }
            }
        });

        KeyManager.getInstance().listen(KeyTools.createCameraKey(CameraKey.KeyThermalPalette, lastDevicePosition, CameraLensType.CAMERA_LENS_THERMAL), this, new CommonCallbacks.KeyListener<CameraThermalPalette>() {
            @Override
            public void onValueChange(@Nullable CameraThermalPalette cameraThermalPalette, @Nullable CameraThermalPalette t1) {
                if (t1 != null) {
                    DJIAircraftApplication.getInstance().setCameraThermalPalette(t1);
                    //ToastUtil.show("thermalPalette: " + t1.name());
                }
            }
        });

    }

    @Override
    protected void onStart() {
        super.onStart();
        mMapPainter.onStart();
    }

    @Override
    protected void onStop() {
        super.onStop();
        mMapPainter.onStop();
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        Event.unregister(this);
        missionController.onDestroy();

        mMapController.onDestroy();
        mMapPainter.onDestroy();

        mSensorHelper.release();
        rtmpController.onDestroy();
        //MediaDataCenter.getInstance().getVideoStreamManager().clearAllStreamSourcesListeners();
        MediaDataCenter.getInstance().getCameraStreamManager().removeAvailableCameraUpdatedListener(availableCameraUpdatedListener);
        KeyManager.getInstance().cancelListen(this);
        removeChannelStateListener();

        if (DJIAircraftApplication.getInstance().isVsEnable()) {
            virtualStickController.disableVS();
        }
        VirtualStickManager.getInstance().clearAllVirtualStickStateListener();

        DJINetworkManager.getInstance().removeNetworkStatusListener(networkStatusListener);

        loadMapNames.clear();
        downloadings.clear();

        if (checkPopup != null) {
            checkPopup = null;
        }
        LiveDataEvent.INSTANCE.clearData();
        MapOverlayUtil.INSTANCE.clearMapOverlay(false);
    }

    @Subscribe
    public void onReceiveFlyWarning(MissionJson missionJson) {
        XLogUtil.INSTANCE.d(TAG, "onReceiveFlyWarning: " + missionJson);
        runOnUiThread(new Runnable() {
            @Override
            public void run() {
                if (missionJson == null || missionJson.getType() == null) {
                    return;
                }
                if (TextUtils.equals(missionJson.getType(), "code")) {
                    if (TextUtils.equals(missionJson.getCode(), "palette")) {
                        setThermalPalette(missionJson.getThlLensParam().getPalette());
                    } else if (TextUtils.equals(missionJson.getCode(), "displaymode")) {
                        changeDisplayMode(missionJson.getDisplaymode());
                    } else if (TextUtils.equals(missionJson.getCode(), "gimbalheading")) {
                        float gimbalheading = missionJson.getGimbalheading();
                    } else if (TextUtils.equals(missionJson.getCode(), "gimbal")) {
                        int gimbal = missionJson.getGimbal();
                        rotateGimbalPitch(gimbal);
                    } else if (TextUtils.equals(missionJson.getCode(), "zoom")) {
                        int zoom = missionJson.getZoom();
                        //只有御3行业进阶才支持小于2倍的变焦
                        if (zoom < 2 && DJIAircraftApplication.getInstance().getProductType() != ProductType.DJI_MAVIC_3_ENTERPRISE_SERIES) {
                            zoom = 2;
                        }
                        binding.zoomPanel.setRatios(zoom);
                        binding.zoomPanel.setZoom();
                    } else if (TextUtils.equals(missionJson.getCode(), "gimbalreform")) {

                    } else if (TextUtils.equals(missionJson.getCode(), "joystick")) {
                        virtualStickController.onJoyStick(missionJson);
                    } else if (TextUtils.equals(missionJson.getCode(), "photo")) {
                        KeyManager.getInstance().performAction(KeyTools.createKey(CameraKey.KeyStartShootPhoto), new CommonCallbacks.CompletionCallbackWithParam<EmptyMsg>() {
                            @Override
                            public void onSuccess(EmptyMsg emptyMsg) {
                                ToastUtil.show("开始拍照");
                            }

                            @Override
                            public void onFailure(@NonNull IDJIError error) {
                                ToastUtil.show("拍照失败:" + error.description());
                            }
                        });

                    } else if (TextUtils.equals(missionJson.getCode(), "record")) {
                        KeyManager.getInstance().performAction(KeyTools.createKey(CameraKey.KeyStartRecord), new CommonCallbacks.CompletionCallbackWithParam<EmptyMsg>() {
                            @Override
                            public void onSuccess(EmptyMsg emptyMsg) {
                                ToastUtil.show("开始录像");
                            }

                            @Override
                            public void onFailure(@NonNull IDJIError error) {
                                ToastUtil.show("开始录像失败，请确认是否开启录像模式");
                            }
                        });
                    } else if (TextUtils.equals(missionJson.getCode(), "stoprecord")) {
                        KeyManager.getInstance().performAction(KeyTools.createKey(CameraKey.KeyStopRecord), new CommonCallbacks.CompletionCallbackWithParam<EmptyMsg>() {
                            @Override
                            public void onSuccess(EmptyMsg emptyMsg) {
                                ToastUtil.show("结束录像");
                            }

                            @Override
                            public void onFailure(@NonNull IDJIError error) {
                            }
                        });
                    } else if (TextUtils.equals(missionJson.getCode(), "focus")) { //连续、自动对焦
                        FocusParam focusParam = missionJson.getFocusParam();
                        DoublePoint2D doublePoint = new DoublePoint2D();
                        doublePoint.setX(focusParam.getAutoFocusTarget().get(0));
                        doublePoint.setY(focusParam.getAutoFocusTarget().get(1));
                        float targetX = Math.round(doublePoint.getX().floatValue() * fpvInteractionWidget.getWidth());
                        float targetY = Math.round(doublePoint.getY().floatValue() * fpvInteractionWidget.getHeight());
                        setTriggerFocus(fpvInteractionWidget, MotionEvent.ACTION_UP, targetX, targetY);
                    } else if (TextUtils.equals(missionJson.getCode(), "tapzoom")) { //指点对焦
                        float[] areaPoint = missionJson.getAreapoint();
                        double x = (areaPoint[0] + areaPoint[2]) / 2;
                        double y = (areaPoint[1] + areaPoint[3]) / 2;
                        tapZoomAtTarget(x, y);
                    } else if (TextUtils.equals(missionJson.getCode(), "tapzoom")) {
                        tapZoomAtTarget(missionJson.getX(), missionJson.getY());
                    } else {
                        binding.zoomPanel.initZoom();
                        missionController.onControlMission(missionJson);
                    }
                } else if (missionJson.getType().equals("adjust")) {
                    virtualStickController.onAdjustAircraft(missionJson);
                } else if (missionJson.getType().equals("joystick")) {// {"msgFrom":"GID_ConsoleDebug@@@skysys010","type":"joystick","mode":1,"joystick":{"vertical":0,"pitch":10,"roll":0,"yaw":0}}
                    virtualStickController.onJoyStick(missionJson);
                } else {
                    //初始化变焦倍数时如果在可见光状态下，切换到红外镜头是，红外变焦倍数未生效
                    binding.zoomPanel.initZoom();
                    changeDisplayMode(2);
                    //延迟1秒，避免切换镜头时，变焦倍数未生效
                    ContextUtil.getHandler().postDelayed(() -> {
                        //设置默认红外变焦倍数
                        setThermalZoom();
                    }, 1000);
                    closeVMode();//关闭超分模式
                    initThermalPalette();//初始化红外的色盘
                    setThermalGainMode();//开启高增益模式
                    isShowMissionNavi = dji.v5.ux.core.util.SpUtil.getNaviShow();
                    binding.ivShowNaviLine.setSelected(isShowMissionNavi);
                    if (isShowMissionNavi && currentIndex == 0) {
                        binding.ivShowNaviLine.setVisibility(View.VISIBLE);
                    }
                    isOpenCheckList = dji.v5.ux.core.util.SpUtil.getOpenCheckList();
                    if (isOpenCheckList) {
                        if (checkPopup != null && checkPopup.isShow()) {
                            Toaster.show("重复下发，请关闭检查单后重新下发");
                            return;
                        }
                        showChecklistPopup(false, missionJson);
                    } else {
                        missionController.onRemoteMission(missionJson);
                    }
                }
            }
        });
    }

    //框选变焦
    private void tapZoomAtTarget(double x, double y) {
        ZoomTargetPointInfo zoomTargetPointInfo = new ZoomTargetPointInfo();
        zoomTargetPointInfo.setX(x);
        zoomTargetPointInfo.setY(y);
        zoomTargetPointInfo.setMode(TapZoomMode.GIMBAL_FOLLOW);
        zoomTargetPointInfo.setTapZoomModeEnable(true);
        KeyManager.getInstance().performAction(KeyTools.createCameraKey(CameraKey.KeyTapZoomAtTarget, lastDevicePosition, CameraLensType.CAMERA_LENS_ZOOM),
                zoomTargetPointInfo, new CommonCallbacks.CompletionCallbackWithParam<EmptyMsg>() {
                    @Override
                    public void onSuccess(EmptyMsg emptyMsg) {
                        ToastUtil.show("框选变焦成功");
                        binding.zoomPanel.tapZoom();
                    }

                    @Override
                    public void onFailure(@NonNull IDJIError idjiError) {
                        ToastUtil.show("框选变焦失败" + idjiError.description());
                    }
                });
    }

    private void rotateGimbalPitch(int gimbalPitch) {
        GimbalAngleRotation gimbalAngleRotation = new GimbalAngleRotation();
        gimbalAngleRotation.setPitch((double) gimbalPitch);
        gimbalAngleRotation.setRollIgnored(true);
        gimbalAngleRotation.setYawIgnored(true);
        KeyManager.getInstance().performAction(KeyTools.createKey(GimbalKey.KeyRotateByAngle), gimbalAngleRotation, new CommonCallbacks.CompletionCallbackWithParam<EmptyMsg>() {
            @Override
            public void onSuccess(EmptyMsg emptyMsg) {
                //ToastUtil.show("设置云台俯仰角度成功");
            }

            @Override
            public void onFailure(@NonNull IDJIError error) {
                ToastUtil.show("设置云台俯仰角度失败:" + error.description());
            }
        });
    }

    private void closeVMode() {
        KeyManager.getInstance().setValue(KeyTools.createCameraKey(CameraKey.KeyThermalSuperResolution, lastDevicePosition, CameraLensType.CAMERA_LENS_THERMAL), false, new CommonCallbacks.CompletionCallback() {
            @Override
            public void onSuccess() {
                ToastUtil.show("关闭超分模式成功");
            }

            @Override
            public void onFailure(@NonNull IDJIError error) {
                if (error != null) {
                    Log.e("关闭超分模式失败", "onFailure: " + error.description());
                }

            }
        });
    }

    private void setThermalZoom() {
        KeyManager.getInstance().setValue(KeyTools.createCameraKey(CameraKey.KeyThermalZoomRatios, lastDevicePosition, CameraLensType.CAMERA_LENS_THERMAL), defaultThemalZoom, new CommonCallbacks.CompletionCallback() {
            @Override
            public void onSuccess() {
            }

            @Override
            public void onFailure(@NonNull IDJIError error) {
                if (error != null) {
                    Log.e("KeyCameraZoomRatios", "onFailure: " + error.description());
                }
            }
        });
    }

    private void setThermalPalette(String palette) {
        CameraThermalPalette cameraThermalPalette = CameraThermalPalette.IRONBOW1;
        switch (palette) {
            case "WHITE_HOT":
                cameraThermalPalette = CameraThermalPalette.WHITE_HOT;
                break;
            case "BLACK_HOT":
                cameraThermalPalette = CameraThermalPalette.BLACK_HOT;
                break;
            case "RED_HOT":
                cameraThermalPalette = CameraThermalPalette.RED_HOT;
                break;
            case "IRONBOW1":
                cameraThermalPalette = CameraThermalPalette.IRONBOW1;
                break;
            case "COLOR2":
                cameraThermalPalette = CameraThermalPalette.COLOR2;
                break;
            case "ICE_FIRE":
                cameraThermalPalette = CameraThermalPalette.ICE_FIRE;
                break;
            case "GREEN_HOT":
                cameraThermalPalette = CameraThermalPalette.GREEN_HOT;
                break;
            case "COLOR1":
                cameraThermalPalette = CameraThermalPalette.COLOR1;
                break;
            case "RAINBOW":
                cameraThermalPalette = CameraThermalPalette.RAINBOW;
                break;
            case "RAIN":
                cameraThermalPalette = CameraThermalPalette.RAIN;
                break;
        }
        KeyManager.getInstance().setValue(KeyTools.createCameraKey(CameraKey.KeyThermalPalette, lastDevicePosition, CameraLensType.CAMERA_LENS_THERMAL), cameraThermalPalette, new CommonCallbacks.CompletionCallback() {
            @Override
            public void onSuccess() {
                //ToastUtil.show("设置红外模式为铁红");
            }

            @Override
            public void onFailure(@NonNull IDJIError error) {
                if (error != null) {
                    Log.e("设置色盘失败", "onFailure: " + error.description());
                }

            }
        });
    }

    private void initThermalPalette() {
        KeyManager.getInstance().setValue(KeyTools.createCameraKey(CameraKey.KeyThermalPalette, lastDevicePosition, CameraLensType.CAMERA_LENS_THERMAL), CameraThermalPalette.IRONBOW1, new CommonCallbacks.CompletionCallback() {
            @Override
            public void onSuccess() {
                //ToastUtil.show("设置红外模式为铁红");
            }

            @Override
            public void onFailure(@NonNull IDJIError error) {
                if (error != null) {
                    Log.e("设置色盘失败", "onFailure: " + error.description());
                }

            }
        });
    }

    private void setThermalGainMode() {
        KeyManager.getInstance().setValue(KeyTools.createCameraKey(CameraKey.KeyThermalGainMode, lastDevicePosition, CameraLensType.CAMERA_LENS_THERMAL), ThermalGainMode.HIGH, new CommonCallbacks.CompletionCallback() {
            @Override
            public void onSuccess() {
                //ToastUtil.show("调整为高增益模式");
            }

            @Override
            public void onFailure(@NonNull IDJIError error) {
                if (error != null) {
                    Log.e("调整增益模式失败", "onFailure: " + error.description());
                }

            }
        });
    }

    //监听到切换视角的指令
    private void changeDisplayMode(int displayMode) {
        switch (displayMode) {
            case 1: //可见光
                onModeSelected(1);
                binding.cameraMode.setText("镜头/变焦");
                break;
            case 2://红外
                onModeSelected(0);
                binding.cameraMode.setText("镜头/红外");
                break;
            case 3://画中画
                break;
            case 4://混合
                break;
            case 5://广角
                onModeSelected(2);
                binding.cameraMode.setText("镜头/广角");
                break;
        }
    }

    public void startMeasureTem() {
        if (TextUtils.equals(binding.tvMode.getText(), "红外")) {
            thermalAreaMeterView.startMeasureTem();
            thermalAreaMeterView.setVisibility(View.VISIBLE);
        }
    }

    public void stopMeasureTem() {
        thermalAreaMeterView.stopMeasureTem();
    }

    @Override
    protected void onResume() {
        super.onResume();
        //沉浸式状态栏
        mMapController.onResume();
        compositeDisposable = new CompositeDisposable();
        compositeDisposable.add(systemStatusListPanelWidget.closeButtonPressed()
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(pressed -> {
                    if (pressed) {
                        ViewExtensions.hide(systemStatusListPanelWidget);
                    }
                }));
        compositeDisposable.add(simulatorControlWidget.getUIStateUpdates()
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(simulatorControlWidgetState -> {
                    if (simulatorControlWidgetState instanceof SimulatorControlWidget.UIState.VisibilityUpdated) {
                        if (((SimulatorControlWidget.UIState.VisibilityUpdated) simulatorControlWidgetState).isVisible()) {
                            hideOtherPanels(simulatorControlWidget);
                        }
                    }
                }));
        compositeDisposable.add(cameraSourceProcessor.toFlowable()
                .observeOn(SchedulerProvider.io())
                .throttleLast(500, TimeUnit.MILLISECONDS)
                .subscribeOn(SchedulerProvider.io())
                .subscribe(result -> runOnUiThread(() -> onCameraSourceUpdated(result.devicePosition, result.lensType)))
        );
        //更新当前位置
        LocationService locationService = MapServiceFactory.getLocationServiceInstance();
        locationService.connectService();
        locationService.startOnceLocation();
    }

    @Override
    protected void onPause() {
        if (compositeDisposable != null) {
            compositeDisposable.dispose();
            compositeDisposable = null;
        }
        super.onPause();
        mMapController.onPause();
    }

    //endregion

    public MapController getMapController() {
        return this.mMapController;
    }

    /**
     * 初始化地图
     *
     * @param savedInstanceState
     */
    private void initMap(Bundle savedInstanceState) {
        mMapController = MapServiceFactory.produceMapController();
        mapView = mMapController.getMapView(this);
        //mapView.setVisibility(View.INVISIBLE);

        mSensorHelper = new SensorEventHelper(ContextUtil.getApplicationContext());
        mSensorHelper.init();

        mMapPainter = new MapPainter(this, mMapController, mSensorHelper, false);

        mMapController.onCreate(savedInstanceState);
        mMapPainter.onCreate();

        initUI();
        mapView.getMap().setOnCameraChangeListener(new AMap.OnCameraChangeListener() {
            @Override
            public void onCameraChange(CameraPosition cameraPosition) {

            }

            @Override
            public void onCameraChangeFinish(CameraPosition cameraPosition) {
                if (subLocation != null) {
                    MapOverlayUtil.INSTANCE.showMapOverlay(mapView.getMap(), subLocation, cameraPosition);
                }
            }
        });
        typePopup = new MapTypePopup(this, position -> {
            switch (position) {
                case 0:
                    mapView.getMap().setMapType(AMap.MAP_TYPE_NORMAL);
                    break;
                case 1:
                    mapView.getMap().setMapType(AMap.MAP_TYPE_SATELLITE);
                    break;
            }
        });
    }

    private void showSyncRTKDataDialog() {
        if (SpUtil.getIsShowSyncRTKTips()) {
            AlertDialog rtkDialog = new AlertDialog.Builder(this)
                    .setMessage("在执行任务前打开RTK并到RTK设置里点击按钮同步起飞点椭球高数据")
                    .setPositiveButton("我知道了", (dialog, which) -> {

                    })
                    .setNegativeButton("不再提醒", (dialog, which) -> {
                        SpUtil.setIsShowSyncRTKTips(false);
                    })
                    .create();
            rtkDialog.setCanceledOnTouchOutside(false);
            rtkDialog.show();
        }
    }

    private void initUI() {
        initWindowSize();
        RelativeLayout container = binding.fpvHolder.findViewById(R.id.map_container);
        container.addView(mapView);
        initAnimation();
    }

    private void initAnimation() {
        binding.ivAnimation.setParam(smallWidth, smallHeight, bigWidth, bigHeight);
        updateSmallWindowParams(binding.ivAnimation, smallWidth, smallHeight);
        binding.ivAnimation.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                binding.ivAnimation.startAnimation();
                animate();
            }
        });
        binding.flyPageMain.bringChildToFront(binding.ivAnimation);
        //binding.flyPageMain.bringChildToFront(binding.viewShootRecorder.main);
    }

    /**
     * 同步层叠顺序: 修改 fpvWrapper 的大小, 然后把 FPVWidget 重新放入, 强迫它适应外层容器大小
     */
    private void syncLayers() {
        /*FPVWidget bigFpv = binding.fpvHolder.findViewById(R.id.widget_primary_fpv);
        View mapContainer = binding.fpvHolder.findViewById(R.id.map_container);*/

        // 调整大小: 如果是视屏在上, 地图全屏; 反之...
        if (currentIndex == Events.IndexEvent.INDEX_LIVE) {
            updateFullWindowParams(binding.mapContainer);
            updateSmallWindowParams(binding.fpvWrapper, smallWidth, smallHeight);

           /* fpvWrapper.removeView(bigFpv);
            fpvWrapper.addView(bigFpv);*/
            binding.widgetPrimaryFpv.setSurfaceViewZOrderMediaOverlay(true);
            binding.fpvHolder.bringChildToFront(binding.fpvWrapper);
        } else {
            updateFullWindowParams(binding.fpvWrapper);
            updateSmallWindowParams(binding.mapContainer, smallWidth, smallHeight);
            /*fpvWrapper.removeView(bigFpv);
            fpvWrapper.addView(bigFpv);*/
            binding.widgetPrimaryFpv.setSurfaceViewZOrderMediaOverlay(false);
            binding.fpvHolder.bringChildToFront(binding.mapContainer);
        }

        binding.fpvHolder.invalidate();
        binding.flyPageMain.bringChildToFront(binding.ivAnimation);
        boolean hasExecutingTask = missionController.hasExecutingTask();
        if (currentIndex == 0) {
            binding.btnSiteChoose.setVisibility(View.VISIBLE);
            binding.ivNaviToAircraft.setVisibility(View.VISIBLE);
            binding.btnMapType.setVisibility(View.VISIBLE);
            //导航路线
            if (checkPopup != null && hasExecutingTask) {
                binding.ivShowNaviLine.setVisibility(View.VISIBLE);
                binding.ivShowNaviLine.setSelected(isShowMissionNavi);
            }
            //检查单面板
            binding.btnCheckList.setVisibility(View.GONE);
            //任务进度
            binding.missionProgress.setVisibility(View.GONE);
            //任务名称
            binding.missionName.setVisibility(View.GONE);
            //任务控制面板
            binding.viewMissionPanel.main.setVisibility(View.GONE);
        } else {
            binding.btnSiteChoose.setVisibility(View.GONE);
            binding.ivNaviToAircraft.setVisibility(View.GONE);
            binding.btnMapType.setVisibility(View.GONE);
            //导航路线
            binding.ivShowNaviLine.setVisibility(View.GONE);
            if (checkPopup != null && !isMissionFinished) {
                binding.btnCheckList.setVisibility(View.VISIBLE);
            }
            if (!isMissionFinished && hasExecutingTask) {
                binding.missionProgress.setVisibility(View.VISIBLE);
                if (!binding.missionName.getText().toString().isEmpty() && !binding.missionName.getText().toString().contains("NA")) {
                    binding.missionName.setVisibility(View.VISIBLE);
                }
                binding.viewMissionPanel.main.setVisibility(View.VISIBLE);
            }
        }
        Event.post(new Events.IndexEvent(currentIndex));
    }

    public void animate() {
        // 切换图层状态
        currentIndex = currentIndex == Events.IndexEvent.INDEX_LIVE ? Events.IndexEvent.INDEX_MAP : Events.IndexEvent.INDEX_LIVE;
        // 同步显示状态
        syncLayers();
    }

    /**
     * 设置窗口为全屏
     *
     * @param view
     */
    private void updateFullWindowParams(View view) {
        RelativeLayout.LayoutParams lp = new RelativeLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT);
        view.setLayoutParams(lp);
    }

    private void updateSmallWindowParams(View view, int width, int height) {
        RelativeLayout.LayoutParams lp = new RelativeLayout.LayoutParams(width, height);
        lp.addRule(RelativeLayout.ALIGN_PARENT_LEFT);
        lp.addRule(RelativeLayout.ALIGN_PARENT_BOTTOM);
        view.setLayoutParams(lp);
    }

    private void initWindowSize() {
        bigWidth = DensityUtil.getScreenWidth();
        bigHeight = DensityUtil.getScreenHeight();

        smallWidth = bigWidth / SMALL_WINDOW_RATIO;
        smallHeight = smallWidth * 9 / 16;
    }

    private void hideOtherPanels(@Nullable View widget) {
        View[] panels = {simulatorControlWidget};
        for (View panel : panels) {
            if (widget != panel) {
                panel.setVisibility(View.GONE);
            }
        }
    }

    private void updateFPVWidgetSource(List<ComponentIndexType> availableCameraList) {
        LogUtils.i(TAG, JsonUtil.toJson(availableCameraList));
        if (availableCameraList == null) {
            return;
        }

        ArrayList<ComponentIndexType> cameraList = new ArrayList<>(availableCameraList);

        //没有数据
        if (cameraList.isEmpty()) {
            secondaryFPVWidget.setVisibility(View.GONE);
            return;
        }

        //仅一路数据
        if (cameraList.size() == 1) {
            primaryFpvWidget.updateVideoSource(availableCameraList.get(0));
            secondaryFPVWidget.setVisibility(View.GONE);
            return;
        }

        //大于两路数据
        ComponentIndexType primarySource = getSuitableSource(cameraList, ComponentIndexType.LEFT_OR_MAIN);
        primaryFpvWidget.updateVideoSource(primarySource);
        cameraList.remove(primarySource);

        ComponentIndexType secondarySource = getSuitableSource(cameraList, ComponentIndexType.FPV);
        secondaryFPVWidget.updateVideoSource(secondarySource);

        secondaryFPVWidget.setVisibility(View.VISIBLE);
    }

    private ComponentIndexType getSuitableSource(List<ComponentIndexType> cameraList, ComponentIndexType defaultSource) {
        if (cameraList.contains(ComponentIndexType.LEFT_OR_MAIN)) {
            return ComponentIndexType.LEFT_OR_MAIN;
        } else if (cameraList.contains(ComponentIndexType.RIGHT)) {
            return ComponentIndexType.RIGHT;
        } else if (cameraList.contains(ComponentIndexType.UP)) {
            return ComponentIndexType.UP;
        } else if (cameraList.contains(ComponentIndexType.PORT_1)) {
            return ComponentIndexType.PORT_1;
        } else if (cameraList.contains(ComponentIndexType.PORT_2)) {
            return ComponentIndexType.PORT_2;
        } else if (cameraList.contains(ComponentIndexType.PORT_3)) {
            return ComponentIndexType.PORT_4;
        } else if (cameraList.contains(ComponentIndexType.PORT_4)) {
            return ComponentIndexType.PORT_4;
        } else if (cameraList.contains(ComponentIndexType.VISION_ASSIST)) {
            return ComponentIndexType.VISION_ASSIST;
        }
        return defaultSource;
    }

    private void onCameraSourceUpdated(ComponentIndexType devicePosition, CameraLensType lensType) {
        LogUtils.i(TAG, devicePosition, lensType);
        if (devicePosition == lastDevicePosition && lensType == lastLensType) {
            return;
        }
        lastDevicePosition = devicePosition;
        lastLensType = lensType;
        initCameraParam(devicePosition, lensType);
        updateViewVisibility(devicePosition, lensType);
        updateInteractionEnabled();
        getCameraMode();
        //如果无需使能或者显示的，也就没有必要切换了。
        if (fpvInteractionWidget.isInteractionEnabled()) {
            fpvInteractionWidget.updateCameraSource(devicePosition, lensType);
        }
        if (visualCameraPanel.getVisibility() == View.VISIBLE) {
            visualCameraPanel.updateCameraSource(devicePosition, lensType);
        }
        if (focusModeWidget.getVisibility() == View.VISIBLE) {
            focusModeWidget.updateCameraSource(devicePosition, lensType);
        }
        if (focusExposureSwitchWidget.getVisibility() == View.VISIBLE) {
            focusExposureSwitchWidget.updateCameraSource(devicePosition, lensType);
        }
        if (cameraControlsWidget.getVisibility() == View.VISIBLE) {
            cameraControlsWidget.updateCameraSource(devicePosition, lensType);
        }
        if (thermalPaletteWidget.getVisibility() == View.VISIBLE) {
            thermalPaletteWidget.updateCameraSource(devicePosition, lensType);
        }
        if (autoExposureLockWidget.getVisibility() == View.VISIBLE) {
            autoExposureLockWidget.updateCameraSource(devicePosition, lensType);
        }
        if (binding.zoomPanel.getVisibility() == View.VISIBLE) {
            binding.zoomPanel.updateCameraSource(devicePosition, lensType);
        }
        if (binding.thermalZoomPanel.getVisibility() == View.VISIBLE) {
            binding.thermalZoomPanel.updateCameraSource(devicePosition, lensType);
        }
    }

    private void updateViewVisibility(ComponentIndexType devicePosition, CameraLensType lensType) {
        //fpv下不显示
        focusModeWidget.setVisibility(CameraUtil.isFPVTypeView(devicePosition) ? View.INVISIBLE : View.VISIBLE);
        focusExposureSwitchWidget.setVisibility(CameraUtil.isFPVTypeView(devicePosition) ? View.INVISIBLE : View.VISIBLE);
        cameraControlsWidget.setVisibility(CameraUtil.isFPVTypeView(devicePosition) ? View.INVISIBLE : View.VISIBLE);
        thermalPaletteWidget.setVisibility(CameraUtil.isFPVTypeView(devicePosition) ? View.INVISIBLE : View.VISIBLE);
        thermalPaletteWidget.setVisibility(lensType == CameraLensType.CAMERA_LENS_THERMAL ? View.VISIBLE : View.INVISIBLE);
        visualCameraPanel.setVisibility(CameraUtil.isFPVTypeView(devicePosition) ? View.INVISIBLE : View.VISIBLE);
        visualCameraPanel.setVisibility(lensType == CameraLensType.CAMERA_LENS_ZOOM ? View.VISIBLE : View.GONE);
        autoExposureLockWidget.setVisibility(CameraUtil.isFPVTypeView(devicePosition) ? View.INVISIBLE : View.VISIBLE);
        autoExposureLockWidget.setVisibility(lensType == CameraLensType.CAMERA_LENS_ZOOM ? View.VISIBLE : View.INVISIBLE);
        binding.constraintCamera.setVisibility(CameraUtil.isFPVTypeView(devicePosition) ? View.INVISIBLE : View.VISIBLE);
        binding.constraintCamera.setVisibility(lensType == CameraLensType.CAMERA_LENS_ZOOM ? View.VISIBLE : View.GONE);
        binding.ivManual.setVisibility(CameraUtil.isFPVTypeView(devicePosition) ? View.INVISIBLE : View.VISIBLE);
    }

    /**
     * Helper method to resize the FPV and Map Widgets.
     *
     * @param viewToEnlarge The view that needs to be enlarged to full screen.
     * @param viewToShrink  The view that needs to be shrunk to a thumbnail.
     */
    private void resizeViews(View viewToEnlarge, View viewToShrink) {
        //enlarge first widget
        ResizeAnimation enlargeAnimation = new ResizeAnimation(viewToEnlarge, widgetWidth, widgetHeight, deviceWidth, deviceHeight, 0);
        viewToEnlarge.startAnimation(enlargeAnimation);

        //shrink second widget
        ResizeAnimation shrinkAnimation = new ResizeAnimation(viewToShrink, deviceWidth, deviceHeight, widgetWidth, widgetHeight, widgetMargin);
        viewToShrink.startAnimation(shrinkAnimation);
    }

    private void removeChannelStateListener() {
        IVideoChannel primaryChannel =
                MediaDataCenter.getInstance().getVideoStreamManager().getAvailableVideoChannel(VideoChannelType.PRIMARY_STREAM_CHANNEL);
        IVideoChannel secondaryChannel =
                MediaDataCenter.getInstance().getVideoStreamManager().getAvailableVideoChannel(VideoChannelType.SECONDARY_STREAM_CHANNEL);
        if (primaryChannel != null) {
            primaryChannel.removeVideoChannelStateChangeListener(primaryChannelStateListener);
        }
        if (secondaryChannel != null) {
            secondaryChannel.removeVideoChannelStateChangeListener(secondaryChannelStateListener);
        }
    }

    /**
     * Swap the video sources of the FPV and secondary FPV widgets.
     */
    private void swapVideoSource() {
        Log.e(TAG, "swapVideoSource called");
        ComponentIndexType primarySource = primaryFpvWidget.getWidgetModel().getCameraIndex();
        ComponentIndexType secondarySource = secondaryFPVWidget.getWidgetModel().getCameraIndex();
        //两个source都存在的情况下才进行切换
        if (primarySource != ComponentIndexType.UNKNOWN && secondarySource != ComponentIndexType.UNKNOWN) {
            primaryFpvWidget.updateVideoSource(secondarySource);
            secondaryFPVWidget.updateVideoSource(primarySource);

            isFPVNow = !isFPVNow;
            binding.cameraMode.setVisibility(isFPVNow ? View.GONE : View.VISIBLE);
            if (isFPVNow) {
                currentCameraMode = binding.tvMode.getText().toString();
            }
            binding.tvMode.setText(isFPVNow ? "FPV" : currentCameraMode);
        }
    }

    private void updateInteractionEnabled() {
        fpvInteractionWidget.setInteractionEnabled(primaryFpvWidget.getWidgetModel().getCameraIndex() != ComponentIndexType.FPV);
    }


    /**
     * Animation to change the size of a view.
     */
    private static class ResizeAnimation extends Animation {

        private static final int DURATION = 300;

        private View view;
        private int toHeight;
        private int fromHeight;
        private int toWidth;
        private int fromWidth;
        private int margin;

        private ResizeAnimation(View v, int fromWidth, int fromHeight, int toWidth, int toHeight, int margin) {
            this.toHeight = toHeight;
            this.toWidth = toWidth;
            this.fromHeight = fromHeight;
            this.fromWidth = fromWidth;
            view = v;
            this.margin = margin;
            setDuration(DURATION);
        }

        @Override
        protected void applyTransformation(float interpolatedTime, Transformation t) {
            float height = (toHeight - fromHeight) * interpolatedTime + fromHeight;
            float width = (toWidth - fromWidth) * interpolatedTime + fromWidth;
            ConstraintLayout.LayoutParams p = (ConstraintLayout.LayoutParams) view.getLayoutParams();
            p.height = (int) height;
            p.width = (int) width;
            p.rightMargin = margin;
            p.bottomMargin = margin;
            view.requestLayout();
        }
    }

    private static class CameraSource {
        ComponentIndexType devicePosition;
        CameraLensType lensType;

        public CameraSource(ComponentIndexType devicePosition, CameraLensType lensType) {
            this.devicePosition = devicePosition;
            this.lensType = lensType;
        }
    }

    private void fullScreenImmersive(View view) {
        int uiOptions = View.SYSTEM_UI_FLAG_LAYOUT_STABLE
                | View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION
                | View.SYSTEM_UI_FLAG_HIDE_NAVIGATION
                | View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY
                | View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
                | View.SYSTEM_UI_FLAG_FULLSCREEN;
        view.setSystemUiVisibility(uiOptions);
    }

    @SuppressLint("NewApi")
    private void getManualZoomRange() {
        // Step 1: 获取最大值
        CompletableFuture<Void> maxValueFuture = new CompletableFuture<>();
        KeyManager.getInstance().getValue(KeyTools.createKey(CameraKey.KeyCameraFocusRingMaxValue), new CommonCallbacks.CompletionCallbackWithParam<Integer>() {

            @Override
            public void onSuccess(Integer newValue) {
                XLogUtil.INSTANCE.e(TAG, "当前手动对焦最大值---->onValueChange: " + newValue);
                if (newValue != null) {
                    manualMaxValue = newValue;
                    maxValueFuture.complete(null);
                } else {
                    maxValueFuture.completeExceptionally(new RuntimeException("Failed to get max value"));
                }
            }

            @Override
            public void onFailure(@NonNull IDJIError idjiError) {
                XLogUtil.INSTANCE.e(TAG, "获取手动对焦最大值---->onFailure: " + idjiError.description());
            }
        });

        // Step 2: 获取最小值
        CompletableFuture<Void> minValueFuture = new CompletableFuture<>();
        KeyManager.getInstance().getValue(KeyTools.createKey(CameraKey.KeyCameraFocusRingMinValue), new CommonCallbacks.CompletionCallbackWithParam<Integer>() {

            @Override
            public void onSuccess(Integer newValue) {
                XLogUtil.INSTANCE.e(TAG, "当前手动对焦最小值---->onValueChange: " + newValue);
                if (newValue != null) {
                    manualMinValue = newValue;
                    minValueFuture.complete(null);
                } else {
                    minValueFuture.completeExceptionally(new RuntimeException("Failed to get min value"));
                }
            }

            @Override
            public void onFailure(@NonNull IDJIError idjiError) {
                XLogUtil.INSTANCE.e(TAG, "获取手动对焦最小值---->onFailure: " + idjiError.description());
            }
        });

        // 等待两个值都获取到后，设置范围并获取当前值
        maxValueFuture.thenCombine(minValueFuture, (v1, v2) -> {
                    runOnUiThread(() -> {
                        if (manualMinValue >= 0 && manualMaxValue >= 0) {
                            binding.manualZoomWidget.setRange(manualMinValue, manualMaxValue);
                        }
                    });
                    return null;
                }).thenRun(this::getManualCurrentValue)
                .exceptionally(ex -> {
                    XLogUtil.INSTANCE.e(TAG, "无法获取最小值或最大值" + ex);
                    return null;
                });
    }

    private void getManualCurrentValue() {
        KeyManager.getInstance().getValue(KeyTools.createCameraKey(CameraKey.KeyCameraFocusRingValue, ComponentIndexType.LEFT_OR_MAIN, CameraLensType.CAMERA_LENS_ZOOM), new CommonCallbacks.CompletionCallbackWithParam<Integer>() {
            @Override
            public void onSuccess(Integer value) {
                XLogUtil.INSTANCE.e(TAG, "当前手动对焦值---->onSuccess: " + value);
                if (value != null) {
                    binding.manualZoomWidget.setCurrentValue(value);
                }
            }

            @Override
            public void onFailure(@NonNull IDJIError idjiError) {
                XLogUtil.INSTANCE.e(TAG, "获取手动对焦值失败---->onFailure: " + idjiError.description());
            }
        });
    }

    private void updateManualUI() {
        KeyManager.getInstance().getValue(KeyTools.createKey(CameraKey.KeyCameraFocusMode), new CommonCallbacks.CompletionCallbackWithParam<CameraFocusMode>() {
            @Override
            public void onSuccess(CameraFocusMode cameraFocusMode) {
                if (cameraFocusMode != null) {
                    if (cameraFocusMode == CameraFocusMode.MANUAL && DJIAircraftApplication.getInstance().getZoom() >= 5.0) {
                        binding.manualZoomWidget.setVisibility(View.VISIBLE);
                    } else {
                        binding.manualZoomWidget.setVisibility(View.GONE);
                    }
                }
            }

            @Override
            public void onFailure(@NonNull IDJIError idjiError) {
            }
        });

        KeyManager.getInstance().listen(KeyTools.createKey(CameraKey.KeyCameraFocusMode), this, (cameraFocusMode, newValue) -> {
            if (newValue != null) {
                if (newValue == CameraFocusMode.MANUAL && DJIAircraftApplication.getInstance().getZoom() >= 5.0) {
                    binding.manualZoomWidget.setVisibility(View.VISIBLE);
                } else {
                    binding.manualZoomWidget.setVisibility(View.GONE);
                }
            }
        });
    }

    //手动设置当前变焦值
    private void setManualValue() {
        binding.manualZoomWidget.setOnValueChangeListener(value -> KeyManager.getInstance().setValue(KeyTools.createCameraKey(CameraKey.KeyCameraFocusRingValue, ComponentIndexType.LEFT_OR_MAIN, CameraLensType.CAMERA_LENS_ZOOM), value, new CommonCallbacks.CompletionCallback() {
            @Override
            public void onSuccess() {
                XLogUtil.INSTANCE.e(TAG, "手动设置变焦成功---->onSuccess: 当前值：" + value);
            }

            @Override
            public void onFailure(@NonNull IDJIError error) {
                XLogUtil.INSTANCE.e(TAG, "手动设置变焦失败---->onFailure: " + error.description());
            }
        }));
    }

    //设置一次自动对焦
    private void setAutoFocus(DoublePoint2D doublePoint2D) {
        KeyManager.getInstance().setValue(KeyTools.createCameraKey(CameraKey.KeyCameraFocusTarget, ComponentIndexType.LEFT_OR_MAIN, CameraLensType.CAMERA_LENS_ZOOM), doublePoint2D, new CommonCallbacks.CompletionCallback() {

            @Override
            public void onSuccess() {
                XLogUtil.INSTANCE.i(TAG, "自动对焦成功");
            }

            @Override
            public void onFailure(@NonNull IDJIError idjiError) {
                XLogUtil.INSTANCE.i(TAG, "自动对焦失败");
            }
        });
    }

    //指点对焦
    private void setTapZoomAtTarget(ZoomTargetPointInfo zoomTargetPointInfo) {
        KeyManager.getInstance().performAction(KeyTools.createKey(CameraKey.KeyTapZoomAtTarget), zoomTargetPointInfo, new CommonCallbacks.CompletionCallbackWithParam<EmptyMsg>() {
            @Override
            public void onSuccess(EmptyMsg emptyMsg) {
                XLogUtil.INSTANCE.i(TAG, "指点对焦设置成功------->" + emptyMsg);
            }

            @Override
            public void onFailure(@NonNull IDJIError idjiError) {
                XLogUtil.INSTANCE.i(TAG, "指点对焦设置失败------->" + idjiError);
            }
        });
    }

    //触发对焦监听
    private void setTriggerFocus(View view, int action, float x, float y) {
        long downTime = SystemClock.uptimeMillis();
        long eventTime = SystemClock.uptimeMillis();
        MotionEvent motionEvent = MotionEvent.obtain(downTime, eventTime, action, x, y, 0);
        view.dispatchTouchEvent(motionEvent);
    }

    private void syncTuoQiuData() {
        //获取椭球高
        RTKCenter.getInstance().addRTKLocationInfoListener(newValue -> {
            if (newValue != null && newValue.getRtkLocation() != null && newValue.getRtkLocation().getMobileStationLocation() != null) {
                tuoQiuHeight = newValue.getRtkLocation().getMobileStationLocation().getAltitude();
                if (tuoQiuHeight == 0) {
                    tuoQiuHeight = 1;
                }
            } else {
                tuoQiuHeight = -9999;
            }
        });
        LocationCoordinate2D locationCoordinate2D = KeyManager.getInstance().getValue(KeyTools.createKey(FlightControllerKey.KeyAircraftLocation));
        if (locationCoordinate2D == null) {
            ToastUtil.show("没有获取到无人机坐标！");
            return;
        }
        if (tuoQiuHeight == -9999) {
            ToastUtil.show("RTK数据异常，请确认RTK正常连接后再重试");
        } else {
            AlertDialog rtkDialog = new AlertDialog.Builder(ContextUtil.getCurrentActivity())
                    .setMessage("当前无人机椭球高为" + tuoQiuHeight + "米，是否上报")
                    .setPositiveButton("确认", (dialog, which) -> {
                        syncRtkData(locationCoordinate2D.getLongitude(), locationCoordinate2D.getLatitude(), tuoQiuHeight);
                    })
                    .setNegativeButton("取消", (dialog, which) -> {
                    })
                    .create();
            rtkDialog.setCanceledOnTouchOutside(false);
            rtkDialog.show();
        }
    }

    private void syncRtkData(double lon, double lat, double tuoQiuHeight) {
        JSONObject data = new JSONObject();
        try {
            JSONArray array = new JSONArray();
            array.add(lon);
            array.add(lat);
            data.put("supplementLonlat", array);
            data.put("supplementHeight", tuoQiuHeight);
            String sn = KeyManager.getInstance().getValue(KeyTools.createKey(FlightControllerKey.KeySerialNumber));
            data.put("sn", sn);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        OkHttpClient httpClient = new OkHttpClient();
        MediaType JSON = MediaType.parse("application/json;charset=utf-8");
        Log.e("TAG", "syncRtkData: " + String.valueOf(data));
        RequestBody requestBody = RequestBody.create(JSON, String.valueOf(data));
        String url = NetConfig.LGURl3 + "thirdParty/supplementLocationInfo";
        Request request = new Request.Builder()
                .url(url)
                .post(requestBody)
                .build();
        Call call = httpClient.newCall(request);
        call.enqueue(new Callback() {
            @Override
            public void onFailure(Call call, IOException e) {
                ContextUtil.getCurrentActivity().runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                        ToastUtil.show("同步椭球高失败:" + e.getLocalizedMessage());
                    }
                });
                Log.e("TAG", "syncRtkData onFailure: " + call.toString() + "    error:" + e.getLocalizedMessage());
            }

            @Override
            public void onResponse(Call call, Response response) throws IOException {
                //在这里根据返回内容执行具体的操作
                String result = response.body().string();
                Log.e("syncRtkData", "onResponse: " + result);
                ContextUtil.getCurrentActivity().runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                        try {
                            com.alibaba.fastjson.JSONObject jsonObject = com.alibaba.fastjson.JSONObject.parseObject(result);
                            if (TextUtils.equals(jsonObject.getString("message"), "success")) {
                                ToastUtil.show("同步椭球高成功");
                            } else {
                                ToastUtil.show("同步椭球高失败，请联系管理员");
                            }
                        } catch (com.alibaba.fastjson.JSONException e) {
                            e.printStackTrace();
                        }
                    }
                });
            }
        });
    }
}
