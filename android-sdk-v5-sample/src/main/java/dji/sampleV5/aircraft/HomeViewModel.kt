package dji.sampleV5.aircraft

import androidx.lifecycle.MutableLiveData
import dji.sampleV5.aircraft.mvvm.base.BaseViewModel
import dji.sampleV5.aircraft.mvvm.ext.rxHttpRequest
import dji.sampleV5.aircraft.mvvm.net.LoadingType
import dji.sampleV5.aircraft.mvvm.net.repository.UserRepository
import dji.sampleV5.aircraft.mvvm.net.request.RegisterSiteRequest
import dji.sampleV5.aircraft.mvvm.net.request.RegisterUavRequest
import dji.sampleV5.aircraft.mvvm.net.request.SiteLocationRequest
import dji.sampleV5.aircraft.net.api.Api
import dji.sampleV5.aircraft.page.login.LoginCache

class HomeViewModel: BaseViewModel() {
    //注册无人机返回
    val addUavLiveData = MutableLiveData<String>()

    //注册站点返回
    val addStationLiveData = MutableLiveData<String>()
    //app版本信息
    val appVersionLiveData = MutableLiveData<LoginCache.AppVersionInfo>()

    //更新站点位置
    val updateSiteLiveData = MutableLiveData<String>()

    fun updateSiteLocation(siteLocationRequest: SiteLocationRequest) {
        rxHttpRequest {
            onRequest = {
                updateSiteLiveData.value = UserRepository.updateSiteLocation(siteLocationRequest).await()
            }
            loadingType = LoadingType.LOADING_NULL //选传 默认为 LoadingType.LOADING_NULL
            loadingMessage = "正在更新站点位置....." // 选传
            requestCode = Api.UPDATE_SITE_LOCATION // 选传，如果要判断接口错误业务的话必传
        }
    }

    fun appVersionCheck() {
        rxHttpRequest {
            onRequest = {
                appVersionLiveData.value = UserRepository.appVersionCheck().await()
            }
            loadingType = LoadingType.LOADING_NULL //选传 默认为 LoadingType.LOADING_NULL
            loadingMessage = "检查版本，请稍后....." // 选传
            requestCode = Api.APP_UPDATE // 选传，如果要
        }
    }

}