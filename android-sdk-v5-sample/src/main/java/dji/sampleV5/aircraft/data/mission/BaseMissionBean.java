package dji.sampleV5.aircraft.data.mission;



public class BaseMissionBean {
    public static final String FINISHED_ACTION_GO_HOME = "GO_HOME";
    public static final String FINISHED_ACTION_NO_ACTION = "NO_ACTION";
    public static final String FINISHED_ACTION_AUTO_LAND = "AUTO_LAND";
    public static final String FINISHED_ACTION_AUTO_CONTINUE_UNTIL_END = "CONTINUE_UNTIL_END";
    public static final String FINISHED_ACTION_AUTO_GO_FIRST_WAYPOINT = "GO_FIRST_WAYPOINT";

    public static final String HEADING_MODE_AUTO = "AUTO";
    public static final String HEADING_MODE_CONTROL_BY_REMOTE_CONTROLLER = "CONTROL_BY_REMOTE_CONTROLLER";
    public static final String HEADING_MODE_TOWARD_POINT_OF_INTEREST = "TOWARD_POINT_OF_INTEREST";
    public static final String HEADING_MODE_USING_INITIAL_DIRECTION = "USING_INITIAL_DIRECTION";
    public static final String HEADING_MODE_USING_WAYPOINT_HEADING = "USING_WAYPOINT_HEADING";

    public static final String TURN_MODE_CLOCKWISE = "Clockwise";
    public static final String TURN_MODE_COUNTERCLOCKWISE = "CounterClockwise";

    public static final String ACTION_TYPE_STAY = "STAY";
    public static final String ACTION_TYPE_START_TAKE_PHOTO = "START_TAKE_PHOTO";
    public static final String ACTION_TYPE_START_RECORD = "START_RECORD";
    public static final String ACTION_TYPE_STOP_RECORD = "STOP_RECORD";
    public static final String ACTION_TYPE_ROTATE_AIRCRAFT = "ROTATE_AIRCRAFT";
    public static final String ACTION_TYPE_GIMBAL_PITCH = "GIMBAL_PITCH";
    public static final String ACTION_TYPE_GIMBAL_YAW = "GIMBAL_YAW";
    public static final String ACTION_TYPE_CAMERA_ZOOM = "CAMERA_ZOOM";
    public static final String ACTION_TYPE_CAMERA_FOCUS = "CAMERA_FOCUS";

    public static final String TAKE_PHOTO_MODE_HOVER_ON_WAYPOINT = "stay";
    public static final String TAKE_PHOTO_MODE_TIME_INTERVAL = "bytime";
    public static final String TAKE_PHOTO_MODE_DISTANCE_INTERVAL = "bydistance";

    public static final String GEN_MODE_SCAN = "scan";
    public static final String GEN_MODE_AREA = "inner";

    public static final String CAMERA_ORITATION_PARALLEL = "parallel";
    public static final String CAMERA_ORITATION_VERTICAL = "verticality";


    private String aid;
    private String name;
    private String address;
    private String thumbnail;
    private Long createdat;
    private Long updatedat;
    private Long executedat;
    private Long deletedat;
    private Integer isexecuted;
    private Integer isdeleted;

    public BaseMissionBean() {}

    public String getAid() {
        return aid;
    }

    public void setAid(String aid) {
        this.aid = aid;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public Long getCreatedat() {
        return createdat;
    }

    public void setCreatedat(Long createdat) {
        this.createdat = createdat;
    }

    public Long getUpdatedat() {
        return updatedat;
    }

    public void setUpdatedat(Long updatedat) {
        this.updatedat = updatedat;
    }

    public Long getExecutedat() {
        return executedat;
    }

    public void setExecutedat(Long executedat) {
        this.executedat = executedat;
    }

    public Long getDeletedat() {
        return deletedat;
    }

    public void setDeletedat(Long deletedat) {
        this.deletedat = deletedat;
    }

    public Integer getIsexecuted() {
        return isexecuted;
    }

    public void setIsexecuted(Integer isexecuted) {
        this.isexecuted = isexecuted;
    }

    public Integer getIsdeleted() {
        return isdeleted;
    }

    public void setIsdeleted(Integer isdeleted) {
        this.isdeleted = isdeleted;
    }

    public String getThumbnail() {
        return thumbnail;
    }

    public void setThumbnail(String thumbnail) {
        this.thumbnail = thumbnail;
    }
}
