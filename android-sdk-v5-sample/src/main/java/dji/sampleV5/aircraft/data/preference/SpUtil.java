package dji.sampleV5.aircraft.data.preference;

import android.content.Context;
import android.content.SharedPreferences;
import android.util.Log;

import java.util.List;

import dji.sampleV5.aircraft.common.json.JsonUtil;
import dji.sampleV5.aircraft.common.json.TypeBuilder;
import dji.sampleV5.aircraft.common.voice.SoundPlayerBean;
import dji.sampleV5.aircraft.data.mission.LaserUploadBean;
import dji.sampleV5.aircraft.data.mission.MissionDetail;
import dji.sampleV5.aircraft.lbs.bean.AppLatLng;
import dji.sampleV5.aircraft.mvvm.net.response.DeliverMissionInfo;
import dji.sampleV5.aircraft.mvvm.net.response.LocationInfoBean;
import dji.sampleV5.aircraft.net.bean.UAVInfoSN;
import dji.sampleV5.aircraft.page.ai.vo.MainLocationVO;
import dji.sampleV5.aircraft.page.login.LoginCache;
import dji.v5.utils.common.LogUtils;

public class SpUtil {
    private final static String TAG = LogUtils.getTag("SpUtil");
    private static SharedPreferences sp;

    public static void init(Context context) {
        sp = context.getSharedPreferences("skysys", Context.MODE_PRIVATE);
    }


    public static boolean getRemoteMode() {
        return sp.getBoolean("Remote_mode", false);
    }

    /**
     * 美国手/日本手/中国手
     *
     * @param remoteMode 遥控器操作模式
     */
    public static void setRemoteMode(boolean remoteMode) {
        sp.edit().putBoolean("Remote_mode", remoteMode).apply();
    }

    public static int getRecorderIndex() {
        return sp.getInt("recorder_index", 1);
    }


    public static void setRecorderIndex(int index) {
        sp.edit().putInt("recorder_index", index).apply();
    }


    public static boolean getDeviceRecorder() {
        return sp.getBoolean("device_recorder", false);
    }

    public static void setDeviceRecorder(boolean isOpen) {
        sp.edit().putBoolean("device_recorder", isOpen).apply();
    }

    public static boolean getRadarUIShow() {
        return sp.getBoolean("RadarUIShow", false);
    }

    public static void setRadarUIShow(boolean RadarUIShow) {
        sp.edit().putBoolean("RadarUIShow", RadarUIShow).apply();
    }

    public static SoundPlayerBean getPlayerSound() {
        String playerSound = sp.getString("SoundPlayerBean", null);
        return JsonUtil.fromJson(playerSound, SoundPlayerBean.class);
    }

    public static void setPlayerSound(SoundPlayerBean playerSound) {
        sp.edit().putString("SoundPlayerBean", JsonUtil.toJson(playerSound)).apply();
    }

    /**
     * 返回批处理任务的 id
     * @return String
     */
    public static String getMissionBatch(){
        return sp.getString("missionBatch","");
    }

    public static void setMissionBatch(String missionBatch){
        sp.edit().putString("missionBatch", missionBatch).apply();
    }

    public static String getAPPName(){
        return sp.getString("appName","");
    }

    public static void setAPPName(String appName){
        sp.edit().putString("appName", appName).apply();
    }

    /**
     * 缓冲任务
     * @param missionBatch 任务 id
     * @param missionDetail 任务 json
     */
    public static void setMissionData(String missionBatch, MissionDetail missionDetail){
        sp.edit().putString(missionBatch, JsonUtil.toJson(missionDetail)).apply();
    }

    /**
     * 根据任务 id 获得任务 json 信息. 实际名称应为: getMissionDataByMissionId
     * @param missionBatch 任务 id
     * @return MissionDetail
     */
    public static MissionDetail getMissionData(String missionBatch){
        String detail = sp.getString(missionBatch, null);
        return JsonUtil.fromJson(detail, MissionDetail.class);
    }

    public static CustomRTKSetting getCustomRTKSetting(){
        String detail = sp.getString("CustomRTKSetting", null);
        return JsonUtil.fromJson(detail, CustomRTKSetting.class);
    }

    public static void setCustomRTKSetting(CustomRTKSetting customRTKSetting){
        sp.edit().putString("CustomRTKSetting", JsonUtil.toJson(customRTKSetting)).apply();
    }

    public static void setAircraftLastLocation(AppLatLng appLatLng){
        sp.edit().putString("lastLocation", JsonUtil.toJson(appLatLng)).apply();
    }

    public static AppLatLng getAircraftLastLocation(){
        String location = sp.getString("lastLocation", null);
        return JsonUtil.fromJson(location, AppLatLng.class);
    }

    public static void setLoginCache(LoginCache loginCache){
        sp.edit().putString("LoginCache", JsonUtil.toJson(loginCache)).apply();
    }

    public static LoginCache getLoginCache(){
        String detail = sp.getString("LoginCache", null);
        Log.e(TAG, "getLoginCache:" + detail);
        return JsonUtil.fromJson(detail, LoginCache.class);
    }

    public static void setStationList(List<MainLocationVO> list){
        sp.edit().putString("stationList", JsonUtil.toJson(list)).apply();
    }

    public static String getSiteList(){
        return sp.getString("siteList","");
    }

    public static void setSiteList(String siteList){
        sp.edit().putString("siteList", siteList).apply();
    }

    public static List<MainLocationVO> getStationList() {
        return JsonUtil.fromJson(sp.getString("stationList", null), TypeBuilder.newInstance(List.class).addTypeParam(MainLocationVO.class).build());
    }

    public static void remove(String missionBatch){
        sp.edit().remove(missionBatch).apply();
    }

    public static void setIsMissionAutoFly(boolean isAuto){
        sp.edit().putBoolean("isAutoFly", isAuto).apply();
    }

    public static boolean getIsMissionAutoFly(){
        return sp.getBoolean("isAutoFly", true);
    }

    public static void setIsHover(boolean isHover){
        sp.edit().putBoolean("isHover", isHover).apply();
    }

    public static boolean getisHover(){
        return sp.getBoolean("isHover", true);
    }

    public static void setIsShowSyncRTKTips(boolean isShowSyncRTKTips){
        sp.edit().putBoolean("isShowSyncRTKTips", isShowSyncRTKTips).apply();
    }

    public static boolean getIsShowSyncRTKTips(){
        return sp.getBoolean("isShowSyncRTKTips", true);
    }


    /**
     * app是否需要向DJI服务器请求校验
     *
     * @param registered isRegister
     */
    public static void setDJIRegistered(boolean registered) {
        sp.edit().putBoolean("dji_registered", registered).apply();
    }

    public static void setDefaultHDSignalChannel() {
        sp.edit().putBoolean("isset_defaultchannel", true).apply();
    }

    public static boolean getIsSetDefaultHDSignalChannel() {
        return sp.getBoolean("isset_defaultchannel", false);
    }

    public static void setIsMeasureTemOpen(boolean isOpen) {
        sp.edit().putBoolean("IsMeasereTemOpen", isOpen).apply();
    }

    public static boolean getIsMeasureTemOpen() {
        return sp.getBoolean("IsMeasereTemOpen", false);
    }

    public static void setLaserList(List<LaserUploadBean> laserList) {
        sp.edit()
                .putString("laserList", JsonUtil.toJson(laserList))
                .apply();
    }

    public static List<LaserUploadBean> getLaserList() {
        return JsonUtil.fromJson(sp.getString("laserList", null), TypeBuilder.newInstance(List.class).addTypeParam(LaserUploadBean.class).build());
    }

    public static void saveUavInfo(UAVInfoSN uavInfoSN){
        sp.edit().putString("uavInfoSN", JsonUtil.toJson(uavInfoSN)).apply();
    }

    public static UAVInfoSN getUAVInfoSN() {
        return JsonUtil.fromJson(sp.getString("uavInfoSN", null), UAVInfoSN.class);
    }

    public static void setDeliverMissionInfo(DeliverMissionInfo deliverMissionInfo) {
        sp.edit().putString("deliverMissionInfo", JsonUtil.toJson(deliverMissionInfo)).apply();
    }

    public static DeliverMissionInfo getDeliverMissionInfo() {
        return JsonUtil.fromJson(sp.getString("deliverMissionInfo", null), DeliverMissionInfo.class);
    }

    public static int getRCameraIndex() {
        return sp.getInt("camera_index", 0);
    }

    public static void setRCameraIndex(int index) {
        sp.edit().putInt("camera_index", index).apply();
    }
}
