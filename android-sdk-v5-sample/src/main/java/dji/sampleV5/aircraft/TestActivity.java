package dji.sampleV5.aircraft;

import android.os.Bundle;
import android.view.View;
import android.view.ViewGroup;
import android.widget.RelativeLayout;

import androidx.annotation.Nullable;
import androidx.appcompat.app.AppCompatActivity;
import androidx.databinding.DataBindingUtil;

import com.amap.api.maps.AMap;

import dji.sampleV5.aircraft.databinding.ActivityTestBinding;
import dji.sampleV5.aircraft.util.ToastUtil;
import me.jessyan.autosize.internal.CancelAdapt;

public class TestActivity extends AppCompatActivity implements CancelAdapt {
    private ActivityTestBinding binding;
    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        binding = DataBindingUtil.setContentView(this,R.layout.activity_test);
        binding.mv.onCreate(savedInstanceState);// 此方法必须重写
        binding.rootView.bringChildToFront(binding.mv);
        AMap aMap = binding.mv.getMap();

        binding.button1.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                RelativeLayout.LayoutParams lp = new RelativeLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT);
                binding.mv.setLayoutParams(lp);

                RelativeLayout.LayoutParams lp2 = new RelativeLayout.LayoutParams(300, 300);
                lp.addRule(RelativeLayout.ALIGN_PARENT_LEFT);
                lp.addRule(RelativeLayout.ALIGN_PARENT_BOTTOM);
                binding.widgetPrimaryFpv.setLayoutParams(lp);

                /*binding.rootView.bringChildToFront(binding.widgetPrimaryFpv);
                binding.rootView.bringChildToFront(binding.widgetSecondaryFpv);*/
                //binding.rootView.bringChildToFront(binding.cameraControl);
               /* binding.cameraControl.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        ToastUtil.show("!!!!!!!!!!!!!");
                    }
                });*/
            }
        });
    }
}
