package dji.sampleV5.aircraft.waypointV2;

public enum MISSION_ACTION {

    UNKNOW(-1, "未知", "未知",""),

    PHOTO(1,"拍照","拍照动作","photo"),
    PHOTO_INTERVAL_TIME(2,"定时拍照","时间间隔拍照","phototime"),
    PHOTO_INTERVAL_DISTANCE(3,"定距拍照","距离间隔拍照","photodistance"),
    RECORD(5,"录像","开始录像","record"),
    RECORD_STOP(6,"停止录像","停止录像","stoprecord"),
    UAV_YAW(7,"机身朝向","调整机身朝向","rotate"),
    GIMBAL(8,"云台调整","云台姿态调整","gimbal"),
    //GIMBAL_YAW(9,"云台朝向","云台朝向调整"),
    GIMBAL_RESET(10,"云台重置","云台姿态重置","reste"),
    CAMERA_ZOOM(11,"变焦","调整变焦","zoom"),
    CAMERA_ZOOM_RESET(12,"变焦重置","变焦重置","resetgimbalyaw"),
    WAIT(13,"等待","悬停等待","stay"),
    PANO_SHOT(14,"全景拍照","全景拍照","panoShot"),
    ;

    private int code;
    private String name;
    private String description;
    private String value;

    MISSION_ACTION(int code, String name, String description, String value){
        this.code=code;
        this.name=name;
        this.description=description;
        this.value = value;
    }

    public static MISSION_ACTION getByCode(String  valueIn){
        for(MISSION_ACTION action:MISSION_ACTION.values()){
            if(valueIn.equals(action.value)){return action;}
        }
        return UNKNOW;
    }

    public int getCode() {return code;}
    public void setCode(int code) {this.code = code;}
    public String getName() {return name;}
    public void setName(String name) {this.name = name;}
    public String getDescription() {return description;}
    public void setDescription(String description) {this.description = description;}

}
