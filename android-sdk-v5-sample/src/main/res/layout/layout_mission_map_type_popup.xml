<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content">

    <ImageView
        android:id="@+id/mission_map_type_normal_iv"
        android:layout_width="@dimen/uxsdk_68_dp"
        android:layout_height="@dimen/uxsdk_68_dp"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="@dimen/uxsdk_10_dp"
        android:layout_marginStart="@dimen/uxsdk_10_dp"
        android:src="@drawable/map_type_default_cn"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/mission_map_type_satellite_iv"
        android:layout_width="@dimen/uxsdk_68_dp"
        android:layout_height="@dimen/uxsdk_68_dp"
        android:layout_gravity="center_horizontal"
        android:layout_marginStart="@dimen/uxsdk_10_dp"
        android:layout_marginEnd="@dimen/uxsdk_10_dp"
        android:src="@drawable/map_type_satellite_cn"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/mission_map_type_normal_iv"
        app:layout_constraintTop_toTopOf="@+id/mission_map_type_normal_iv" />

    <TextView
        android:id="@+id/mission_map_type_normal_tv"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="@dimen/uxsdk_10_dp"
        android:layout_marginBottom="@dimen/uxsdk_10_dp"
        android:gravity="center"
        android:text="标准"
        android:textSize="@dimen/text_size_13"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@+id/mission_map_type_satellite_tv"
        app:layout_constraintHorizontal_weight="1.0"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/mission_map_type_normal_iv" />

    <TextView
        android:id="@+id/mission_map_type_satellite_tv"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:gravity="center"
        android:text="卫星"
        android:textSize="@dimen/text_size_13"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_weight="1.0"
        app:layout_constraintStart_toEndOf="@+id/mission_map_type_normal_tv"
        app:layout_constraintTop_toTopOf="@id/mission_map_type_normal_tv" />
</androidx.constraintlayout.widget.ConstraintLayout>