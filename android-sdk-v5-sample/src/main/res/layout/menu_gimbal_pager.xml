<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:android="http://schemas.android.com/apk/res/android">

    <data>

        <variable
            name="activityMenuPresenter"
            type="dji.sampleV5.aircraft.page.fly.setting.ActivityMenuPresenter" />

        <variable
            name="gimbalAutoCalibration"
            type="dji.sampleV5.aircraft.page.fly.setting.pager.detail.GimbalAutoCalibration" />
    </data>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:divider="@color/divider_light"
        android:orientation="vertical"
        android:showDividers="middle|end">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <TextView
                android:id="@+id/tv_thermal_meter"
                style="@style/AircraftStandardTextView"
                android:text="开启红外测温" />

            <ToggleButton
                android:id="@+id/toggle_thermal_meter"
                android:layout_width="36dp"
                android:layout_height="32dp"
                android:layout_alignParentEnd="true"
                android:layout_centerVertical="true"
                android:layout_marginStart="10dp"
                android:layout_marginEnd="@dimen/space_20"
                android:background="@drawable/toggle_button1"
                android:textOff=""
                android:textOn="" />
        </RelativeLayout>


        <RelativeLayout
            android:id="@+id/v_fpv_show"
            style="@style/AircraftSetPadding"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:visibility="gone">

            <TextView
                style="@style/AircraftStandardTextView"
                android:text="@string/fpv_show" />

            <ToggleButton
                android:id="@+id/tb_fpv_show"
                android:layout_width="36dp"
                android:layout_height="32dp"
                android:layout_alignParentEnd="true"
                android:layout_centerVertical="true"
                android:layout_marginEnd="@dimen/space_20"
                android:background="@drawable/toggle_button1"
                android:textOff=""
                android:textOn="" />
        </RelativeLayout>

        <RelativeLayout
            android:id="@+id/v_gimbal_choose"
            style="@style/AircraftSetPadding"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:visibility="gone">

            <TextView
                style="@style/AircraftStandardTextView"
                android:layout_centerVertical="true"
                android:text="@string/gimbal_choose" />

            <RadioGroup
                android:id="@+id/rg_gimbal_choose"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentEnd="true"
                android:orientation="horizontal">

                <RadioButton
                    style="@style/AircraftStandardTextView"
                    android:background="@drawable/bg_select_check_corner"
                    android:button="@null"
                    android:checked="@{activityMenuPresenter.gimbalIndex == 0}"
                    android:text="@string/gimbal1" />

                <RadioButton
                    style="@style/AircraftStandardTextView"
                    android:layout_marginStart="@dimen/space_5"
                    android:background="@drawable/bg_select_check_corner"
                    android:button="@null"
                    android:checked="@{activityMenuPresenter.gimbalIndex == 1}"
                    android:text="@string/gimbal2" />
            </RadioGroup>
        </RelativeLayout>

        <RelativeLayout
            android:visibility="gone"
            android:id="@+id/rl_gimbal_set_advanced"
            style="@style/AircraftSetPadding"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@drawable/bg_status_tag"
            android:onClick="@{()->activityMenuPresenter.GimbalSetAdvanced()}">

            <TextView
                style="@style/AircraftStandardTextView"
                android:layout_centerVertical="true"
                android:text="@string/advanced_setting" />

            <ImageView
                style="@style/cameraStandardImageView"
                android:layout_alignParentEnd="true"
                android:layout_centerVertical="true"
                android:src="@drawable/ic_keyboard_arrow_right_black_24dp"
                tools:ignore="ContentDescription"
                app:tint="@color/icon_tint" />
        </RelativeLayout>

        <TextView
            android:visibility="gone"
            android:id="@+id/tv_gimbal_reset"
            style="@style/AircraftStandardTextView"
            android:layout_width="match_parent"
            android:layout_gravity="center"
            android:background="@drawable/bg_status_tag"
            android:gravity="center"
            android:onClick="@{()->activityMenuPresenter.onClickReset()}"
            android:padding="@dimen/space_15"
            android:text="@string/gimbal_reset"
            android:textColor="@color/colorBright" />

        <TextView
            android:visibility="gone"
            android:id="@+id/tv_gimbal_roll_set"
            style="@style/AircraftStandardTextView"
            android:layout_width="match_parent"
            android:layout_gravity="center"
            android:background="@drawable/bg_status_tag"
            android:gravity="center"
            android:onClick="@{()->activityMenuPresenter.onClickRoll(activityMenuPresenter.gimbalIndex)}"
            android:padding="@dimen/space_15"
            android:text="@string/gimbal_roll_adjust"
            android:textColor="@color/colorBright" />

        <TextView
            android:visibility="gone"
            android:id="@+id/tv_gimbal_auto_set"
            style="@style/AircraftStandardTextView"
            android:layout_width="match_parent"
            android:layout_gravity="center"
            android:background="@drawable/bg_status_tag"
            android:gravity="center"
            android:onClick="@{()->gimbalAutoCalibration.onClickAutoCalibration(activityMenuPresenter.gimbalIndex)}"
            android:padding="@dimen/space_15"
            android:text="@string/gimbal_calibration_auto"
            android:textColor="@color/colorBright" />
    </LinearLayout>
</layout>