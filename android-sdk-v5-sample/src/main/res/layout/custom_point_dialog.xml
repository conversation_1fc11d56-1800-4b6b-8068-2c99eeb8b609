<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:layout_gravity="center"
    android:layout_marginLeft="50dp"
    android:layout_marginRight="50dp"
    android:orientation="vertical"
    android:background="@color/color_transparent"
    android:gravity="center">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/shape_radian_dialog_white"
        android:orientation="vertical">

        <TextView
            android:id="@+id/tv_alert_title"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginTop="10dp"
            android:gravity="center"
            android:text="自定义开始航点"
            android:textColor="#000000"
            android:textSize="17sp"
            android:visibility="visible" />


        <LinearLayout
            android:layout_marginTop="@dimen/space_10"
            android:orientation="horizontal"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <TextView
                android:textSize="@dimen/text_size_17"
                android:textColor="@color/black"
                android:layout_marginStart="@dimen/space_20"
                android:text="航点："
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"/>

            <EditText
                android:inputType="number"
                android:id="@+id/edit_point"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:layout_marginLeft="20dp"
                android:layout_marginRight="20dp"
                android:gravity="center"
                android:textColor="#000000"
                android:text="0"
                android:textSize="14sp" />
        </LinearLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="0.3dp"
            android:background="#33000000" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="horizontal">

            <Button
                android:id="@+id/btn_alert_cancel"
                android:layout_width="match_parent"
                android:layout_height="40dp"
                android:layout_weight="1"
                android:background="@color/color_transparent"
                android:gravity="center"
                android:text="取消"
                android:textColor="#80000000"
                android:textSize="16sp"
                android:visibility="visible" />

            <View
                android:id="@+id/v_alert_line"
                android:layout_width="0.3dp"
                android:layout_height="match_parent"
                android:background="#33000000" />

            <Button
                android:id="@+id/btn_alert_ok"
                android:layout_width="match_parent"
                android:layout_height="40dp"
                android:layout_weight="1"
                android:background="@color/color_transparent"
                android:gravity="center"
                android:text="确定"
                android:textColor="@color/colorAccent"
                android:textSize="16sp" />
        </LinearLayout>
    </LinearLayout>
</LinearLayout>
