<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/light_gray2"
    android:padding="3dp"
    android:orientation="horizontal">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/bg_stroke_common">


    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:orientation="vertical">

        <LinearLayout
            android:id="@+id/ll_filter_container"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="center"
                android:layout_marginTop="5dp"
                android:paddingLeft="5dp"
                android:orientation="vertical">
                <TextView
                    android:id="@+id/tv_list_title"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/key"
                    android:textColor="@color/white"
                    android:textSize="20sp"
                    android:textStyle="bold"/>
                <TextView
                    android:id="@+id/tv_count"
                    android:layout_width="wrap_content"
                    android:layout_height="15dp"
                    android:textSize="12sp"
                    android:text="(12573)"
                    android:textColor="@color/gray"/>
            </LinearLayout>


            <ImageView
                android:id="@+id/iv_action_arrow"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:layout_marginRight="20dp"
                android:src="@mipmap/icon_arrow"/>

        </LinearLayout>

        <include layout="@layout/layout_deliver_horizontal_line"/>

        <LinearLayout
            android:id="@+id/ll_filter"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <EditText
                android:id="@+id/et_filter"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginLeft="10dp"
                android:layout_marginRight="10dp"
                android:layout_weight="1"
                android:drawablePadding="5dp"
                android:drawableRight="@mipmap/key_search_icon"
                android:gravity="center"
                android:hint="@string/key_search"
                android:imeOptions="actionDone"
                android:singleLine="true"
                android:textColor="@color/black"
                android:textColorHint="@color/gray"
                android:textStyle="italic"/>



        </LinearLayout>
        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recyclerView"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"/>
    </LinearLayout>

    <include layout="@layout/layout_deliver_vertical_line"/>

    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_weight="4"
        android:orientation="vertical">

        <RelativeLayout
            android:id="@+id/ll_channel_filter_container"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <Switch
                android:id="@+id/iv_capability"
                android:layout_width="wrap_content"
                android:layout_height="25dp"
                android:layout_alignParentLeft="true"
                android:layout_centerInParent="true"
                android:layout_marginLeft="20dp"
                android:enabled="false" />

            <TextView
                android:id="@+id/tv_capablity"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:layout_marginStart="4dp"
                android:textColor="@color/white"
                android:layout_toEndOf="@+id/iv_capability"
                />


            <LinearLayout
                android:id="@+id/tv_operate_title_lyt"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:layout_marginStart="10dp"
                android:layout_marginTop="10dp"
                android:layout_marginEnd="10dp"
                android:layout_marginBottom="10dp"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/tv_operate_title"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Camera"
                    android:textColor="@color/white"
                    android:textSize="20sp"
                    android:textStyle="bold" />

                <ImageView
                    android:id="@+id/iv_action_arrow1"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:layout_marginLeft="10dp"
                    android:layout_marginRight="10dp"
                    android:src="@mipmap/icon_arrow" />
            </LinearLayout>


            <ImageView
                android:id="@+id/iv_question_mark"
                android:layout_width="25dp"
                android:layout_height="25dp"
                android:layout_centerInParent="true"
                android:layout_alignParentRight="true"
                android:layout_marginRight="20dp"
                android:src="@mipmap/question_mark_icon" />
        </RelativeLayout>

        <include layout="@layout/layout_deliver_horizontal_line"/>

        <include layout="@layout/layout_key_operate"/>

    </LinearLayout>
    </LinearLayout>
</LinearLayout>

