<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
              xmlns:tools="http://schemas.android.com/tools"
              android:id="@+id/params_item"
              android:layout_width="match_parent"
              android:layout_height="wrap_content"
              android:orientation="vertical">

    <TextView
        android:id="@+id/tv_item_common_name"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_centerVertical="true"
        android:layout_gravity="center"
        android:ellipsize="end"
        android:gravity="center"
        android:padding="10dp"
        android:singleLine="true"
        android:textColor="@color/white"
        android:textSize="18sp"
        tools:text="Item name"/>

    <include layout="@layout/layout_deliver_horizontal_line"/>

</LinearLayout>