<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>
        <variable
            name="report"
            type="dji.sampleV5.aircraft.mvvm.net.response.LocationInfoBean.MainLocation.SubLocation" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/item_site"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content">

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tv_site_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="20dp"
            android:paddingVertical="10dp"
            android:text="@{report.location}"
            android:textColor="@{report.checkedTextColor}"
            android:singleLine="true"
            android:textSize="15sp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/iv_choose_status"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="20dp"
            android:visibility="@{report.checkedIcon}"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:srcCompat="@drawable/ic_confirm" />

        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:background="@color/white_gray_transparent"
            app:layout_constraintTop_toBottomOf="@+id/tv_site_name" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>