<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <TextView
                style="@style/AircraftStandardTextView"
                android:text="@string/go_home_height" />

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentEnd="true"
                android:orientation="horizontal">

                <TextView
                    style="@style/AircraftParticularsTextView"
                    android:text="(20-500M)"
                    tools:ignore="HardcodedText" />

                <View
                    android:layout_width="0px"
                    android:layout_height="0px"
                    android:focusable="true"
                    android:focusableInTouchMode="true" />

                <EditText
                    android:id="@+id/tv_ASet_GoHoneHigh"
                    style="@style/AircraftParticularsTextView"
                    android:layout_width="@dimen/space_42"
                    android:layout_height="wrap_content"
                    android:background="@drawable/ic_kuang"
                    android:gravity="center"
                    android:imeOptions="actionDone"
                    android:inputType="number"
                    android:padding="@dimen/space_5"
                    android:text=""
                    android:textColor="@color/colorBright"
                    tools:ignore="LabelFor" />

                <TextView
                    style="@style/AircraftParticularsTextView"
                    android:text="M"
                    tools:ignore="HardcodedText" />
            </LinearLayout>
        </RelativeLayout>

    </LinearLayout>
</layout>