<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:orientation="vertical"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:gravity="center_horizontal"
    android:background="@color/white">
    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="wrap_content">
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">
    <androidx.cardview.widget.CardView
        xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        android:layout_width="240dp"
        android:layout_height="240dp"
        android:layout_margin="5dp"
        android:layout_gravity="center"
        app:cardCornerRadius="8dp">
        <ImageView
            android:id='@+id/image'
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:scaleType="centerCrop"
            android:transitionName="tansitionImage"
            tools:ignore="ContentDescription,UnusedAttribute" />
        <include
            layout="@layout/layout_media_play_download_progress"/>
    </androidx.cardview.widget.CardView>



    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:orientation="vertical"
        android:padding="10dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:orientation="horizontal"
            android:padding="10dp">

            <Button
                android:id="@+id/preview_file"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="center"
                android:text="预览图"
                android:textAllCaps="false"/>

            <Button
                android:id="@+id/download_file"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="center"
                android:text="下载原图"
                android:textAllCaps="false"/>

            <Button
                android:id="@+id/cancel_download"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="center"
                android:text="取消下载"
                android:textAllCaps="false"
                android:visibility="visible"/>

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:orientation="horizontal"
            android:padding="10dp">

            <Button
                android:id="@+id/pull_xmp_file_data"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="center"
                android:text="XMP"
                android:textAllCaps="false"/>

            <Button
                android:id="@+id/pull_xmp_custom_info"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="center"
                android:text="XMP Custom Info"
                android:textAllCaps="false"/>
        </LinearLayout>

        <LinearLayout
            android:id="@+id/download_ll_dcf"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:visibility="gone"
            android:orientation="horizontal">
            <Button
                android:id="@+id/download_file_json"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="center"
                android:text="下载Json"
                android:textAllCaps="false"/>
            <Button
                android:id="@+id/download_file_subfile"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="center"
                android:text="下载子文件"
                android:textAllCaps="false"/>
        </LinearLayout>
    </LinearLayout>
  </LinearLayout>
 </ScrollView>

</LinearLayout>