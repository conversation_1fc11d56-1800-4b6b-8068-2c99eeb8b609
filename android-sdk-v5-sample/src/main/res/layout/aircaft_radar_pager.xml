<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="activityMenuPresenter"
            type="dji.sampleV5.aircraft.page.fly.setting.ActivityMenuPresenter" />
    </data>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <ScrollView
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content">

                    <TextView
                        android:id="@+id/tv_radar_obstacle_avoidance"
                        style="@style/AircraftStandardTextView"
                        android:text="开启机身所有避障系统" />

                    <ToggleButton
                        android:id="@+id/toggle_obstacleAvoidance"
                        android:layout_width="36dp"
                        android:layout_height="32dp"
                        android:layout_alignParentEnd="true"
                        android:layout_centerVertical="true"
                        android:layout_marginStart="10dp"
                        android:layout_marginEnd="@dimen/space_20"
                        android:background="@drawable/toggle_button1"
                        android:textOff=""
                        android:textOn="" />
                </RelativeLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content">

                    <ImageView
                        android:layout_width="@dimen/space_32"
                        android:layout_height="@dimen/space_32"
                        android:layout_gravity="center"
                        android:scaleType="fitCenter"
                        android:src="@drawable/setting_ui_vision_ass_icon1"
                        tools:ignore="ContentDescription" />

                    <TextView
                        style="@style/AircraftParticularsTextView"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/radar_obstacle_avoidance1" />
                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content">

                    <ImageView
                        android:layout_width="@dimen/space_32"
                        android:layout_height="@dimen/space_32"
                        android:layout_gravity="center"
                        android:scaleType="fitCenter"
                        android:src="@drawable/setting_ui_vision_ghass_icon"
                        tools:ignore="ContentDescription" />

                    <TextView
                        style="@style/AircraftParticularsTextView"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/radar_obstacle_avoidance2" />
                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content">

                    <ImageView
                        android:layout_width="@dimen/space_32"
                        android:layout_height="@dimen/space_32"
                        android:layout_gravity="center"
                        android:scaleType="fitCenter"
                        android:src="@drawable/setting_ui_vision_ass_icon2"
                        tools:ignore="ContentDescription" />

                    <TextView
                        style="@style/AircraftParticularsTextView"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/radar_obstacle_avoidance3" />
                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content">

                    <ImageView
                        android:layout_width="@dimen/space_32"
                        android:layout_height="@dimen/space_32"
                        android:layout_gravity="center"
                        android:scaleType="fitCenter"
                        android:src="@drawable/setting_ui_vision_ass_icon3"
                        tools:ignore="ContentDescription" />

                    <TextView
                        style="@style/AircraftParticularsTextView"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/radar_obstacle_avoidance4" />
                </LinearLayout>

                <include layout="@layout/line_between" />

                <RelativeLayout
                    android:layout_marginBottom="@dimen/space_10"
                    android:layout_marginTop="@dimen/space_10"
                    android:id="@+id/rl_Radar_obstacleUpwards"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:visibility="gone">

                    <TextView
                        style="@style/AircraftStandardTextView"
                        android:text="@string/radar_obstacle_upwards" />

                    <ToggleButton
                        android:id="@+id/toggle_Radar_obstacleUpwards"
                        android:layout_width="36dp"
                        android:layout_height="32dp"
                        android:layout_alignParentEnd="true"
                        android:layout_centerVertical="true"
                        android:layout_marginStart="10dp"
                        android:layout_marginEnd="@dimen/space_20"
                        android:background="@drawable/toggle_button1"
                        android:textOff=""
                        android:textOn="" />
                </RelativeLayout>

                <include layout="@layout/line_between" />
                
                <RelativeLayout
                    android:visibility="gone"
                    android:layout_marginBottom="@dimen/space_10"
                    android:layout_marginTop="@dimen/space_10"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content">

                    <TextView
                        style="@style/AircraftStandardTextView"
                        android:text="@string/setting_ui_vision_radar" />

                    <ToggleButton
                        android:id="@+id/toggle_show_radar"
                        android:layout_width="36dp"
                        android:layout_height="32dp"
                        android:layout_alignParentEnd="true"
                        android:layout_centerVertical="true"
                        android:layout_marginStart="10dp"
                        android:layout_marginEnd="@dimen/space_20"
                        android:background="@drawable/toggle_button1"
                        android:textOff=""
                        android:textOn="" />
                </RelativeLayout>

                <include layout="@layout/line_between" />

                <RelativeLayout
                    android:visibility="gone"
                    android:layout_marginBottom="@dimen/space_10"
                    android:layout_marginTop="@dimen/space_10"
                    android:id="@+id/rl_vision_advance_set"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center">

                    <TextView
                        style="@style/AircraftStandardTextView"
                        android:text="@string/advanced_setting" />

                    <ImageView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentEnd="true"
                        android:layout_centerInParent="true"
                        android:src="@drawable/ic_keyboard_arrow_right_black_24dp"
                        tools:ignore="ContentDescription"
                        app:tint="@color/icon_tint" />
                </RelativeLayout>

                <include layout="@layout/line_between" />

                <!--<include
                    android:id="@+id/menu_auxiliary_light_set"
                    layout="@layout/menu_auxiliary_light_set" />-->
                <!--<dw.ux.panel.aircraft.AuxiliaryLightPanel
                    android:id="@+id/auxiliary_light"
                    android:visibility="gone"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"/>-->
            </LinearLayout>
        </ScrollView>
    </LinearLayout>

</layout>