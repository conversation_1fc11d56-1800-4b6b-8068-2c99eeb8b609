<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="match_parent"
    android:paddingStart="@dimen/uxsdk_2_dp"
    android:paddingEnd="@dimen/uxsdk_2_dp"
    android:id="@+id/stream_selector_root_view"
    android:stateListAnimator="@animator/uxsdk_pressed_alpha_040">

    <dji.v5.ux.core.ui.component.StrokeTextView
        android:id="@+id/tv_content"
        style="@style/CameraStatusActionBarItem_TextView"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:textSize="@dimen/text_size_11"
        android:text=""/>

</FrameLayout>