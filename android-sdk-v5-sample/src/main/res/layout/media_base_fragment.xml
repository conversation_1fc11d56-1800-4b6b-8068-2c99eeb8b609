<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <FrameLayout
        android:id="@+id/fl_content_media"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

    <RelativeLayout
        android:id="@+id/downloadLL"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/white"
        android:visibility="invisible">

        <TextView
            android:id="@+id/btn_warning"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerHorizontal="true"
            android:layout_marginTop="@dimen/space_40"
            android:text="@string/downloading_warning"
            android:textColor="@color/red"
            android:textSize="@dimen/text_size_20" />

        <!--<TextView
            android:layout_below="@+id/btn_warning"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/space_30"
            android:layout_marginTop="@dimen/space_50"
            android:text="@string/downloading_aircraft"
            android:textColor="@color/black"
            android:textSize="@dimen/text_size_20" />-->

        <TextView
            android:id="@+id/download_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@+id/btn_warning"
            android:layout_centerHorizontal="true"
            android:layout_marginTop="@dimen/space_20"
            android:textSize="@dimen/text_size_16" />

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@+id/download_text"
            android:layout_centerHorizontal="true"
            android:layout_marginTop="@dimen/space_20"
            android:orientation="horizontal">

            <ProgressBar
                android:id="@+id/downloadProgressBar"
                style="?android:attr/progressBarStyleHorizontal"
                android:layout_width="250dp"
                android:layout_height="3dp"
                android:layout_gravity="center_vertical"
                android:layout_marginStart="@dimen/space_30"
                android:max="100"
                android:progressDrawable="@drawable/progress_shape" />

            <TextView
                android:id="@+id/down_percent"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:layout_marginStart="@dimen/space_10"
                android:text="0" />
        </LinearLayout>
    </RelativeLayout>
</RelativeLayout>