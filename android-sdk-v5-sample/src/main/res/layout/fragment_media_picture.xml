<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <import type="android.view.View" />

        <variable
            name="mediaFragmentPresenter"
            type="dji.sampleV5.aircraft.page.picture.media.MediaFragmentPresenter" />
    </data>

    <dji.sampleV5.aircraft.view.LinearLayoutTouch
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/white"
        android:orientation="vertical">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/black">

            <ImageView
                android:id="@+id/iv_back"
                android:layout_width="@dimen/space_32"
                android:layout_height="@dimen/space_32"
                android:layout_margin="@dimen/space_10"
                android:background="@drawable/ic_arrow_left_black_24dp"
                android:backgroundTint="@color/white"
                tools:ignore="ContentDescription" />

            <TextView
                style="@style/AircraftStandardTextView"
                android:layout_centerInParent="true"
                android:text="@string/playback_select_tip"
                android:visibility="@{mediaFragmentPresenter.selectDelete?View.VISIBLE:View.GONE}" />

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:layout_gravity="center"
                android:visibility="@{mediaFragmentPresenter.selectDelete?View.GONE:View.VISIBLE}">

                <TextView
                    android:id="@+id/tv_media_all"
                    style="@style/AircraftStandardTextView"
                    android:text="@string/all"
                    android:textColor="@{mediaFragmentPresenter.tabAll?@color/gray:@color/white}"
                    android:textSize="@dimen/text_size_13" />

                <TextView
                    android:visibility="gone"
                    android:id="@+id/tv_media_image"
                    style="@style/AircraftStandardTextView"
                    android:text="@string/image"
                    android:textColor="@{mediaFragmentPresenter.tabImage?@color/gray:@color/white}"
                    android:textSize="@dimen/text_size_13" />

                <TextView
                    android:visibility="gone"
                    android:id="@+id/tv_media_video"
                    style="@style/AircraftStandardTextView"
                    android:text="@string/video"
                    android:textColor="@{mediaFragmentPresenter.tabVideo?@color/gray:@color/white}"
                    android:textSize="@dimen/text_size_13" />
            </LinearLayout>

            <TextView
                android:id="@+id/tv_media_select"
                style="@style/AircraftStandardTextView"
                android:layout_alignParentEnd="true"
                android:layout_centerVertical="true"
                android:layout_marginEnd="@dimen/space_10"
                android:text="@string/select" />

            <RelativeLayout
                android:id="@+id/rl_media_delete"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_toStartOf="@id/tv_media_select"
                android:background="@drawable/bg_status_tag"
                android:clickable="true"
                android:focusable="true"
                android:padding="@dimen/space_10">

                <ImageView
                    android:layout_width="@dimen/space_26"
                    android:layout_height="@dimen/space_26"
                    android:src="@drawable/ic_delete_black_24dp"
                    app:tint="@color/white"
                    android:visibility="@{mediaFragmentPresenter.selectDelete?View.VISIBLE:View.GONE}"
                    tools:ignore="ContentDescription" />
            </RelativeLayout>

            <RelativeLayout
                android:id="@+id/rl_media_download"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_toStartOf="@id/rl_media_delete"
                android:background="@drawable/bg_status_tag"
                android:clickable="true"
                android:focusable="true"
                android:padding="@dimen/space_10">

                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:src="@drawable/ic_download"
                    app:tint="@color/white"
                    android:visibility="@{mediaFragmentPresenter.selectDelete?View.VISIBLE:View.GONE}"
                    tools:ignore="ContentDescription" />
            </RelativeLayout>

        </RelativeLayout>

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <TextView
                style="@style/AircraftStandardTextView"
                android:layout_centerInParent="true"
                android:layout_gravity="center"
                android:layout_marginTop="@dimen/space_50"
                android:text="@string/not_image"
                android:textColor="@color/gray"
                android:visibility="@{mediaFragmentPresenter.isEmpty?View.VISIBLE:View.GONE}" />

            <dji.sampleV5.aircraft.view.NoScrollViewPager
                android:id="@+id/vp_media_fragment"
                android:layout_width="match_parent"
                android:layout_height="match_parent" />

        </RelativeLayout>


    </dji.sampleV5.aircraft.view.LinearLayoutTouch>
</layout>