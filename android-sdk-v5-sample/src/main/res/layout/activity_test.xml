<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <RelativeLayout
        android:id="@+id/root_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <com.amap.api.maps.MapView
            android:id="@+id/mv"
            android:layout_width="200dp"
            android:layout_height="100dp"
            android:layout_alignParentStart="true"
            android:layout_alignParentBottom="true" />

        <dji.v5.ux.core.widget.fpv.FPVWidget
            android:id="@+id/widget_primary_fpv"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:uxsdk_cameraNameTextSize="14dp"
            app:uxsdk_cameraSideTextSize="14dp"
            app:uxsdk_centerPointEnabled="false"
            app:uxsdk_sourceCameraNameVisibility="false"
            app:uxsdk_sourceCameraSideVisibility="false">

            <!-- <dji.v5.ux.core.widget.hsi.PrimaryFlightDisplayWidget
                 android:id="@+id/widget_fpv_flight_display_widget"
                 android:layout_width="wrap_content"
                 android:layout_height="wrap_content" />-->
        </dji.v5.ux.core.widget.fpv.FPVWidget>

        <dji.v5.ux.core.widget.fpv.FPVWidget
            android:id="@+id/widget_secondary_fpv"
            android:layout_width="@dimen/uxsdk_mini_map_width"
            android:layout_height="@dimen/uxsdk_mini_map_height"
            android:layout_alignParentEnd="true"
            android:layout_alignParentBottom="true"
            android:layout_marginEnd="@dimen/uxsdk_10_dp"
            android:layout_marginBottom="@dimen/uxsdk_10_dp"
            app:uxsdk_cameraNameTextSize="8dp"
            app:uxsdk_cameraSideTextSize="8dp"
            app:uxsdk_interactionEnabled="false"
            app:uxsdk_sourceCameraNameVisibility="false"
            app:uxsdk_sourceCameraSideVisibility="false" />



        <Button
            android:id="@+id/button1"
            android:layout_width="@dimen/space_50"
            android:layout_height="@dimen/space_50"
            android:text="放大地图" />

       <!-- <dji.v5.ux.cameracore.widget.cameracontrols.CameraControlsWidget
            android:id="@+id/camera_control"
            android:layout_width="@dimen/space_60"
            android:layout_height="@dimen/space_200"
            android:layout_centerInParent="true"/>-->

        <dji.v5.ux.cameracore.widget.cameracontrols.lenscontrol.LensControlWidget
            android:layout_centerInParent="true"
            android:layout_width="@dimen/space_100"
            android:layout_height="@dimen/space_200"/>

    </RelativeLayout>
</layout>