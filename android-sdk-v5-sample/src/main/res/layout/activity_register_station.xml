<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/main"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".mvvm.ui.activity.site.RegisterStationActivity">

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/iv_title"
        android:layout_width="0dp"
        android:layout_height="@dimen/uxsdk_50_dp"
        android:background="@color/black_3e"
        android:elevation="@dimen/uxsdk_5_dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.appcompat.widget.AppCompatTextView
        android:layout_width="wrap_content"
        android:layout_height="@dimen/uxsdk_50_dp"
        android:elevation="@dimen/uxsdk_5_dp"
        android:gravity="center"
        android:text="新建非机库站点"
        android:textColor="@color/white"
        android:textSize="@dimen/uxsdk_text_size_medium"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/iv_log_back"
        android:layout_width="@dimen/uxsdk_50_dp"
        android:layout_height="@dimen/uxsdk_50_dp"
        android:elevation="@dimen/uxsdk_5_dp"
        android:src="@drawable/ic_arrow_left_white_24dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.core.widget.NestedScrollView
        android:id="@+id/scrollView"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginBottom="@dimen/uxsdk_40_dp"
        app:layout_constraintBottom_toTopOf="@+id/cl_bottom"
        app:layout_constraintTop_toTopOf="parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tv_basicinfo_title"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/uxsdk_10_dp"
                android:layout_marginTop="@dimen/uxsdk_10_dp"
                android:background="@drawable/bg_plan_mission_title"
                android:drawableStart="@drawable/ic_basicinfo"
                android:drawablePadding="@dimen/uxsdk_7_dp"
                android:gravity="center_vertical"
                android:paddingHorizontal="@dimen/uxsdk_10_dp"
                android:paddingVertical="@dimen/uxsdk_13_dp"
                android:text="基本信息"
                android:textColor="@color/black"
                android:textSize="16sp"
                android:textStyle="bold"
                app:layout_constraintTop_toTopOf="parent" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tv_station_name"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/uxsdk_40_dp"
                android:layout_marginStart="@dimen/uxsdk_35_dp"
                android:layout_marginTop="@dimen/uxsdk_15_dp"
                android:drawableStart="@drawable/ic_star"
                android:drawablePadding="@dimen/uxsdk_5_dp"
                android:gravity="center"
                android:text="站点名称"
                android:textColor="@color/black_gray"
                android:textSize="14sp"
                android:textStyle="bold"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tv_basicinfo_title" />

            <androidx.appcompat.widget.AppCompatEditText
                android:id="@+id/et_station_name"
                android:layout_width="@dimen/uxsdk_350_dp"
                android:layout_height="0dp"
                android:layout_marginStart="@dimen/uxsdk_10_dp"
                android:background="@drawable/bg_linear_choose_date_selected"
                android:gravity="center_vertical"
                android:hint="请输入站点名称"
                android:paddingHorizontal="@dimen/uxsdk_10_dp"
                android:singleLine="true"
                android:textColor="@color/black_gray"
                android:textSize="@dimen/uxsdk_dic_text_size_18sp"
                app:layout_constraintBottom_toBottomOf="@+id/tv_station_name"
                app:layout_constraintStart_toEndOf="@+id/tv_station_name"
                app:layout_constraintTop_toTopOf="@+id/tv_station_name"
                tools:text="测试测试测试测试" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tv_location"
                android:layout_width="wrap_content"
                android:layout_height="0dp"
                android:layout_marginEnd="@dimen/uxsdk_24_dp"
                android:drawableStart="@drawable/ic_star"
                android:drawablePadding="@dimen/uxsdk_5_dp"
                android:gravity="center"
                android:text="地理位置"
                android:textColor="@color/black_gray"
                android:textSize="14sp"
                android:textStyle="bold"
                app:layout_constraintBottom_toBottomOf="@+id/tv_station_name"
                app:layout_constraintEnd_toStartOf="@+id/et_location"
                app:layout_constraintTop_toTopOf="@+id/tv_station_name" />

            <androidx.appcompat.widget.AppCompatEditText
                android:id="@+id/et_location"
                android:layout_width="@dimen/uxsdk_350_dp"
                android:layout_height="0dp"
                android:layout_marginStart="@dimen/uxsdk_10_dp"
                android:layout_marginEnd="@dimen/uxsdk_15_dp"
                android:background="@drawable/bg_linear_choose_date_selected"
                android:gravity="center_vertical"
                android:hint="请输入当前坐标"
                android:paddingHorizontal="@dimen/uxsdk_10_dp"
                android:singleLine="true"
                android:textColor="@color/black_gray"
                android:textSize="@dimen/uxsdk_dic_text_size_18sp"
                app:layout_constraintBottom_toBottomOf="@+id/tv_location"
                app:layout_constraintEnd_toStartOf="@+id/iv_location"
                app:layout_constraintTop_toTopOf="@+id/tv_location"
                tools:text="121.90315203，30.97387479" />

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/iv_location"
                android:layout_width="@dimen/uxsdk_40_dp"
                android:layout_height="@dimen/uxsdk_40_dp"
                android:background="@drawable/bg_check"
                android:padding="@dimen/uxsdk_8_dp"
                android:src="@drawable/ic_site_location_white"
                app:layout_constraintBottom_toBottomOf="@+id/et_location"
                app:layout_constraintEnd_toEndOf="@+id/rl_map"
                app:layout_constraintTop_toTopOf="@+id/et_location" />

            <RelativeLayout
                android:id="@+id/rl_map"
                android:layout_width="0dp"
                android:layout_height="@dimen/uxsdk_200_dp"
                android:layout_marginTop="@dimen/uxsdk_15_dp"
                android:layout_marginEnd="@dimen/uxsdk_15_dp"
                android:background="@drawable/btn_bg_delete_plan_normal"
                android:clipChildren="true"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="@+id/et_location"
                app:layout_constraintTop_toBottomOf="@+id/et_location">

                <dji.sampleV5.aircraft.mvvm.widget.CustomMapView
                    android:id="@+id/map_site_loc"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:background="@null" />
            </RelativeLayout>

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tv_config_mode"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/uxsdk_40_dp"
                android:layout_marginTop="@dimen/uxsdk_15_dp"
                android:drawableStart="@drawable/ic_star"
                android:drawablePadding="@dimen/uxsdk_5_dp"
                android:gravity="center"
                android:text="配置模式"
                android:textColor="@color/black_gray"
                android:textSize="14sp"
                android:textStyle="bold"
                app:layout_constraintStart_toStartOf="@+id/tv_station_name"
                app:layout_constraintTop_toBottomOf="@+id/tv_station_name" />

            <dji.sampleV5.aircraft.mvvm.widget.spinner.EditSpinner
                android:id="@+id/sp_config_mode"
                android:layout_width="@dimen/uxsdk_350_dp"
                android:layout_height="@dimen/uxsdk_40_dp"
                android:layout_marginStart="@dimen/uxsdk_10_dp"
                app:es_arrowImage="@drawable/uxsdk_ic_spinner_cell_expand"
                app:es_background="@drawable/bg_linear_choose_date_selected"
                app:es_dropdown_bg="@drawable/btn_bg_delete_plan_ban"
                app:es_height="@dimen/uxsdk_40_dp"
                app:es_hint="请选择配置模式"
                app:es_input_disable="true"
                app:es_isFilterKey="true"
                app:es_textColor="@color/black_gray"
                app:es_textSize="14sp"
                app:layout_constraintBottom_toBottomOf="@+id/tv_config_mode"
                app:layout_constraintStart_toEndOf="@+id/tv_config_mode"
                app:layout_constraintTop_toTopOf="@+id/tv_config_mode" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tv_fly_mode"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/uxsdk_40_dp"
                android:layout_marginTop="@dimen/uxsdk_15_dp"
                android:drawableStart="@drawable/ic_star"
                android:drawablePadding="@dimen/uxsdk_5_dp"
                android:gravity="center"
                android:text="飞行模式"
                android:textColor="@color/black_gray"
                android:textSize="14sp"
                android:textStyle="bold"
                app:layout_constraintStart_toStartOf="@+id/tv_config_mode"
                app:layout_constraintTop_toBottomOf="@+id/tv_config_mode" />

            <dji.sampleV5.aircraft.mvvm.widget.spinner.EditSpinner
                android:id="@+id/sp_fly_mode"
                android:layout_width="@dimen/uxsdk_350_dp"
                android:layout_height="@dimen/uxsdk_40_dp"
                android:layout_marginStart="@dimen/uxsdk_10_dp"
                app:es_arrowImage="@drawable/uxsdk_ic_spinner_cell_expand"
                app:es_background="@drawable/bg_linear_choose_date_selected"
                app:es_dropdown_bg="@drawable/btn_bg_delete_plan_ban"
                app:es_height="@dimen/uxsdk_40_dp"
                app:es_hint="请选择飞行模式"
                app:es_input_disable="true"
                app:es_isFilterKey="true"
                app:es_textColor="@color/black_gray"
                app:es_textSize="14sp"
                app:layout_constraintBottom_toBottomOf="@+id/tv_fly_mode"
                app:layout_constraintStart_toEndOf="@+id/tv_fly_mode"
                app:layout_constraintTop_toTopOf="@+id/tv_fly_mode" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tv_return_height"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/uxsdk_40_dp"
                android:layout_marginTop="@dimen/uxsdk_15_dp"
                android:drawableStart="@drawable/ic_star"
                android:drawablePadding="@dimen/uxsdk_5_dp"
                android:gravity="center"
                android:text="返航高度"
                android:textColor="@color/black_gray"
                android:textSize="14sp"
                android:textStyle="bold"
                app:layout_constraintStart_toStartOf="@+id/tv_config_mode"
                app:layout_constraintTop_toBottomOf="@+id/tv_fly_mode" />

            <androidx.appcompat.widget.AppCompatEditText
                android:id="@+id/et_return_height"
                android:layout_width="@dimen/uxsdk_350_dp"
                android:layout_height="0dp"
                android:layout_marginStart="@dimen/uxsdk_10_dp"
                android:background="@drawable/bg_linear_choose_date_selected"
                android:gravity="center_vertical"
                android:hint="请输入返航高度"
                android:inputType="numberDecimal"
                android:paddingHorizontal="@dimen/uxsdk_10_dp"
                android:singleLine="true"
                android:text="120"
                android:textColor="@color/black_gray"
                android:textSize="@dimen/uxsdk_dic_text_size_18sp"
                app:layout_constraintBottom_toBottomOf="@+id/tv_return_height"
                app:layout_constraintStart_toEndOf="@+id/tv_return_height"
                app:layout_constraintTop_toTopOf="@+id/tv_return_height"
                tools:text="120" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tv_ellipsoid_height"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/uxsdk_40_dp"
                android:layout_marginStart="@dimen/uxsdk_13_dp"
                android:layout_marginTop="@dimen/uxsdk_15_dp"
                android:gravity="center"
                android:text="椭球高度"
                android:textColor="@color/black_gray"
                android:textSize="14sp"
                android:textStyle="bold"
                app:layout_constraintEnd_toEndOf="@+id/tv_config_mode"
                app:layout_constraintTop_toBottomOf="@+id/tv_return_height" />

            <androidx.appcompat.widget.AppCompatEditText
                android:id="@+id/et_ellipsoid_height"
                android:layout_width="@dimen/uxsdk_350_dp"
                android:layout_height="0dp"
                android:background="@drawable/bg_linear_choose_date_selected"
                android:gravity="center_vertical"
                android:hint="请输入椭球高度"
                android:inputType="numberDecimal"
                android:paddingHorizontal="@dimen/uxsdk_10_dp"
                android:singleLine="true"
                android:text="0"
                android:textColor="@color/black_gray"
                android:textSize="@dimen/uxsdk_dic_text_size_18sp"
                app:layout_constraintBottom_toBottomOf="@+id/tv_ellipsoid_height"
                app:layout_constraintStart_toStartOf="@+id/et_return_height"
                app:layout_constraintTop_toTopOf="@+id/tv_ellipsoid_height"
                tools:text="0" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tv_uav_info"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/uxsdk_10_dp"
                android:layout_marginTop="@dimen/uxsdk_20_dp"
                android:background="@drawable/bg_plan_mission_title"
                android:drawableStart="@drawable/ic_site_uav"
                android:drawablePadding="@dimen/uxsdk_7_dp"
                android:gravity="center_vertical"
                android:paddingHorizontal="@dimen/uxsdk_10_dp"
                android:paddingVertical="@dimen/uxsdk_13_dp"
                android:text="无人机信息"
                android:textColor="@color/black"
                android:textSize="16sp"
                android:textStyle="bold"
                app:layout_constraintTop_toBottomOf="@+id/tv_ellipsoid_height" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tv_bind_new_drone"
                android:layout_width="wrap_content"
                android:layout_height="0dp"
                android:layout_marginEnd="@dimen/uxsdk_15_dp"
                android:background="@drawable/selector_add_new_mission_confirm_bg"
                android:gravity="center"
                android:paddingHorizontal="@dimen/uxsdk_15_dp"
                android:text="绑定新建无人机"
                android:textColor="@color/white"
                android:textSize="12sp"
                android:clickable="true"
                android:focusable="true"
                app:layout_constraintBottom_toBottomOf="@+id/tv_bind_existing_drone"
                app:layout_constraintEnd_toStartOf="@+id/tv_bind_existing_drone"
                app:layout_constraintTop_toTopOf="@+id/tv_bind_existing_drone" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tv_bind_existing_drone"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/uxsdk_30_dp"
                android:layout_marginEnd="@dimen/uxsdk_5_dp"
                android:background="@drawable/selector_add_new_mission_cancel_bg_focused"
                android:gravity="center"
                android:paddingHorizontal="@dimen/uxsdk_15_dp"
                android:text="绑定已有无人机"
                android:textColor="@color/item_btn_start"
                android:textSize="12sp"
                android:clickable="true"
                android:focusable="true"
                app:layout_constraintBottom_toBottomOf="@+id/tv_uav_info"
                app:layout_constraintEnd_toEndOf="@+id/tv_uav_info"
                app:layout_constraintTop_toTopOf="@+id/tv_uav_info" />


            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tv_uav_name"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/uxsdk_40_dp"
                android:layout_marginStart="@dimen/uxsdk_35_dp"
                android:layout_marginTop="@dimen/uxsdk_15_dp"
                android:drawableStart="@drawable/ic_star"
                android:drawablePadding="@dimen/uxsdk_5_dp"
                android:gravity="center"
                android:text="无人机名称"
                android:textColor="@color/black_gray"
                android:textSize="14sp"
                android:textStyle="bold"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tv_uav_info" />

            <androidx.appcompat.widget.AppCompatEditText
                android:id="@+id/et_uav_name"
                android:layout_width="@dimen/uxsdk_350_dp"
                android:layout_height="0dp"
                android:layout_marginStart="@dimen/uxsdk_10_dp"
                android:background="@drawable/bg_linear_choose_date_selected"
                android:gravity="center_vertical"
                android:hint="请输入无人机名称"
                android:paddingHorizontal="@dimen/uxsdk_10_dp"
                android:singleLine="true"
                android:textColor="@color/black_gray"
                android:textSize="@dimen/uxsdk_dic_text_size_18sp"
                app:layout_constraintBottom_toBottomOf="@+id/tv_uav_name"
                app:layout_constraintStart_toEndOf="@+id/tv_uav_name"
                app:layout_constraintTop_toTopOf="@+id/tv_uav_name"
                tools:text="测试测试测试测试" />

            <dji.sampleV5.aircraft.mvvm.widget.spinner.EditSpinner
                android:id="@+id/es_uav_name"
                android:layout_width="@dimen/uxsdk_350_dp"
                android:layout_height="@dimen/uxsdk_40_dp"
                android:layout_marginStart="@dimen/uxsdk_10_dp"
                android:visibility="gone"
                app:es_arrowImage="@drawable/uxsdk_ic_spinner_cell_expand"
                app:es_background="@drawable/bg_linear_choose_date_selected"
                app:es_dropdown_bg="@drawable/btn_bg_delete_plan_ban"
                app:es_height="@dimen/uxsdk_40_dp"
                app:es_hint="请选择无人机名称"
                app:es_input_disable="true"
                app:es_isFilterKey="true"
                app:es_textColor="@color/black_gray"
                app:es_textSize="14sp"
                app:layout_constraintBottom_toBottomOf="@+id/tv_uav_name"
                app:layout_constraintStart_toEndOf="@+id/tv_uav_name"
                app:layout_constraintTop_toTopOf="@+id/tv_uav_name" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tv_uav_number"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/uxsdk_40_dp"
                android:layout_marginStart="@dimen/uxsdk_13_dp"
                android:layout_marginEnd="@dimen/uxsdk_10_dp"
                android:gravity="center"
                android:text="无人机编号"
                android:textColor="@color/black_gray"
                android:textSize="14sp"
                android:textStyle="bold"
                app:layout_constraintBottom_toBottomOf="@+id/et_uav_number"
                app:layout_constraintEnd_toStartOf="@+id/et_uav_number"
                app:layout_constraintTop_toTopOf="@+id/et_uav_number" />

            <androidx.appcompat.widget.AppCompatEditText
                android:id="@+id/et_uav_number"
                android:layout_width="@dimen/uxsdk_350_dp"
                android:layout_height="0dp"
                android:layout_marginEnd="@dimen/uxsdk_15_dp"
                android:background="@drawable/bg_linear_choose_date_selected"
                android:gravity="center_vertical"
                android:hint="请输入无人机编号"
                android:paddingHorizontal="@dimen/uxsdk_10_dp"
                android:singleLine="true"
                android:textColor="@color/black_gray"
                android:textSize="@dimen/uxsdk_dic_text_size_18sp"
                app:layout_constraintBottom_toBottomOf="@+id/tv_uav_name"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="@+id/tv_uav_name"
                tools:text="测试测试测试测试" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tv_uav_fc_sn"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/uxsdk_40_dp"
                android:layout_marginStart="@dimen/uxsdk_35_dp"
                android:layout_marginTop="@dimen/uxsdk_15_dp"
                android:drawableStart="@drawable/ic_star"
                android:drawablePadding="@dimen/uxsdk_5_dp"
                android:gravity="center"
                android:text="序列号FCSN"
                android:textColor="@color/black_gray"
                android:textSize="14sp"
                android:textStyle="bold"
                app:layout_constraintEnd_toEndOf="@+id/tv_uav_name"
                app:layout_constraintTop_toBottomOf="@+id/tv_uav_name" />

            <androidx.appcompat.widget.AppCompatEditText
                android:id="@+id/et_uav_fc_sn"
                android:layout_width="@dimen/uxsdk_350_dp"
                android:layout_height="0dp"
                android:background="@drawable/bg_linear_choose_date_selected"
                android:layout_marginStart="@dimen/uxsdk_10_dp"
                android:gravity="center_vertical"
                android:hint="请输入序列号FCSN"
                android:paddingHorizontal="@dimen/uxsdk_10_dp"
                android:singleLine="true"
                android:textColor="@color/black_gray"
                android:textSize="@dimen/uxsdk_dic_text_size_18sp"
                app:layout_constraintBottom_toBottomOf="@+id/tv_uav_fc_sn"
                app:layout_constraintStart_toEndOf="@+id/tv_uav_fc_sn"
                app:layout_constraintTop_toTopOf="@+id/tv_uav_fc_sn"
                tools:text="测试测试测试测试" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tv_uav_fc_producer"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/uxsdk_40_dp"
                android:layout_marginStart="@dimen/uxsdk_35_dp"
                android:layout_marginEnd="@dimen/uxsdk_10_dp"
                android:drawableStart="@drawable/ic_star"
                android:drawablePadding="@dimen/uxsdk_5_dp"
                android:gravity="center"
                android:text="生产商"
                android:textColor="@color/black_gray"
                android:textSize="14sp"
                android:textStyle="bold"
                app:layout_constraintBottom_toBottomOf="@+id/et_uav_fc_producer"
                app:layout_constraintEnd_toStartOf="@+id/et_uav_fc_producer"
                app:layout_constraintTop_toTopOf="@+id/et_uav_fc_producer" />

            <androidx.appcompat.widget.AppCompatEditText
                android:id="@+id/et_uav_fc_producer"
                android:layout_width="@dimen/uxsdk_350_dp"
                android:layout_height="0dp"
                android:background="@drawable/bg_linear_choose_date_selected"
                android:gravity="center_vertical"
                android:hint="请输入生产商"
                android:paddingHorizontal="@dimen/uxsdk_10_dp"
                android:singleLine="true"
                android:textColor="@color/black_gray"
                android:textSize="@dimen/uxsdk_dic_text_size_18sp"
                app:layout_constraintBottom_toBottomOf="@+id/tv_uav_fc_sn"
                app:layout_constraintEnd_toEndOf="@+id/et_uav_number"
                app:layout_constraintTop_toTopOf="@+id/tv_uav_fc_sn"
                tools:text="测试测试测试测试" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tv_uav_model"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/uxsdk_40_dp"
                android:layout_marginStart="@dimen/uxsdk_35_dp"
                android:layout_marginTop="@dimen/uxsdk_15_dp"
                android:layout_marginEnd="@dimen/uxsdk_10_dp"
                android:drawableStart="@drawable/ic_star"
                android:drawablePadding="@dimen/uxsdk_5_dp"
                android:gravity="center"
                android:text="无人机型号"
                android:textColor="@color/black_gray"
                android:textSize="14sp"
                android:textStyle="bold"
                app:layout_constraintEnd_toStartOf="@+id/es_uav_model"
                app:layout_constraintTop_toBottomOf="@+id/tv_uav_fc_sn" />

            <dji.sampleV5.aircraft.mvvm.widget.spinner.EditSpinner
                android:id="@+id/es_uav_model"
                android:layout_width="@dimen/uxsdk_350_dp"
                android:layout_height="@dimen/uxsdk_40_dp"
                app:es_arrowImage="@drawable/uxsdk_ic_spinner_cell_expand"
                app:es_background="@drawable/bg_linear_choose_date_selected"
                app:es_dropdown_bg="@drawable/btn_bg_delete_plan_ban"
                app:es_height="@dimen/uxsdk_40_dp"
                app:es_hint="请选择无人机型号"
                app:es_input_disable="true"
                app:es_isFilterKey="true"
                app:es_textColor="@color/black_gray"
                app:es_textSize="14sp"
                app:layout_constraintBottom_toBottomOf="@+id/tv_uav_model"
                app:layout_constraintStart_toStartOf="@+id/et_uav_fc_sn"
                app:layout_constraintTop_toTopOf="@+id/tv_uav_model" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tv_site_real_video_title"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/uxsdk_10_dp"
                android:layout_marginTop="@dimen/uxsdk_20_dp"
                android:background="@drawable/bg_plan_mission_title"
                android:drawableStart="@drawable/ic_site_real_video"
                android:drawablePadding="@dimen/uxsdk_7_dp"
                android:gravity="center_vertical"
                android:paddingHorizontal="@dimen/uxsdk_10_dp"
                android:paddingVertical="@dimen/uxsdk_13_dp"
                android:text="实时视角配置"
                android:textColor="@color/black"
                android:textSize="16sp"
                android:textStyle="bold"
                app:layout_constraintTop_toBottomOf="@+id/tv_uav_model" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tv_real_video_config"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/uxsdk_40_dp"
                android:layout_marginTop="@dimen/uxsdk_15_dp"
                android:gravity="center"
                android:text="实时播流配置"
                android:textColor="@color/black_gray"
                android:textSize="14sp"
                android:textStyle="bold"
                app:layout_constraintStart_toStartOf="@+id/tv_uav_model"
                app:layout_constraintTop_toBottomOf="@+id/tv_site_real_video_title" />

            <androidx.appcompat.widget.AppCompatEditText
                android:id="@+id/et_real_video_config"
                android:layout_width="@dimen/uxsdk_350_dp"
                android:layout_height="0dp"
                android:layout_marginStart="@dimen/uxsdk_10_dp"
                android:background="@drawable/bg_linear_choose_date_selected"
                android:gravity="center_vertical"
                android:hint="请输入实时播流信息"
                android:paddingHorizontal="@dimen/uxsdk_10_dp"
                android:singleLine="true"
                android:text="play3.skysys.cn"
                android:textColor="@color/black_gray"
                android:textSize="@dimen/uxsdk_dic_text_size_18sp"
                app:layout_constraintBottom_toBottomOf="@+id/tv_real_video_config"
                app:layout_constraintStart_toEndOf="@+id/tv_real_video_config"
                app:layout_constraintTop_toTopOf="@+id/tv_real_video_config" />


            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tv_real_video_push"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/uxsdk_40_dp"
                android:layout_marginEnd="@dimen/uxsdk_10_dp"
                android:gravity="center"
                android:text="实时推流配置"
                android:textColor="@color/black_gray"
                android:textSize="14sp"
                android:textStyle="bold"
                app:layout_constraintBottom_toBottomOf="@+id/et_real_video_push"
                app:layout_constraintEnd_toStartOf="@+id/et_real_video_push"
                app:layout_constraintTop_toTopOf="@+id/et_real_video_push" />

            <androidx.appcompat.widget.AppCompatEditText
                android:id="@+id/et_real_video_push"
                android:layout_width="@dimen/uxsdk_350_dp"
                android:layout_height="0dp"
                android:layout_marginStart="@dimen/uxsdk_10_dp"
                android:background="@drawable/bg_linear_choose_date_selected"
                android:gravity="center_vertical"
                android:hint="请输入实时推流信息"
                android:paddingHorizontal="@dimen/uxsdk_10_dp"
                android:singleLine="true"
                android:text="stream3.skysys.cn"
                android:textColor="@color/black_gray"
                android:textSize="@dimen/uxsdk_dic_text_size_18sp"
                app:layout_constraintBottom_toBottomOf="@+id/tv_real_video_config"
                app:layout_constraintEnd_toEndOf="@+id/tv_site_real_video_title"
                app:layout_constraintTop_toTopOf="@+id/tv_real_video_config" />

        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.core.widget.NestedScrollView>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_bottom"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="@dimen/uxsdk_15_dp"
        android:paddingVertical="@dimen/uxsdk_10_dp"
        app:layout_constraintBottom_toBottomOf="parent">

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tv_confirm"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@drawable/selector_add_new_mission_confirm_bg"
            android:gravity="center"
            android:paddingHorizontal="@dimen/uxsdk_35_dp"
            android:paddingVertical="@dimen/uxsdk_10_dp"
            android:text="确定"
            android:textColor="@color/white"
            android:textSize="@dimen/uxsdk_dic_text_size_18sp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@+id/tv_cancel"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tv_cancel"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@drawable/selector_add_new_mission_cancel_bg"
            android:gravity="center"
            android:paddingHorizontal="@dimen/uxsdk_35_dp"
            android:paddingVertical="@dimen/uxsdk_10_dp"
            android:text="取消"
            android:textColor="@color/item_btn_start"
            android:textSize="@dimen/uxsdk_dic_text_size_20sp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@+id/tv_confirm"
            app:layout_constraintTop_toTopOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.constraintlayout.widget.ConstraintLayout>