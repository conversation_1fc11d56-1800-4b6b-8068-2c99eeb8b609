<resources>

    <string name="news_title">MSDK 5.4.0</string>
    <string name="news_description">"- Added supported aircraft: Matrice 350 RTK.
- Matrice 300 RTK added support for DJI RC Plus remote controller.

More refer to the release notes." </string>
    <string name="news_date">2023–5–18</string>
    <string name="sdk_forum_url">https://sdk-forum.dji.net/hc/en-us/community/topics</string>
    <string name="release_node_url">https://developer.dji.com/doc/mobile-sdk-tutorial/en/</string>
    <string name="tech_support_url">https://sdk-forum.dji.net/hc/en-us/requests/new</string>

    <string name="news_dialog_content_tips">MSDK has a new version update, click for details.</string>
    <string name="news_dialog_btn_left">Details</string>
    <string name="news_dialog_btn_right">Exit</string>
    <string name="release_note_tile_latest">Latest version of MSDK: %s</string>
    <string name="release_note_tile_current">Current version of MSDK: %s</string>
    <string name="release_note_loading">loading…</string>

    <string name="app_name_all">MSDK ALL</string>
    <string name="app_name_aircraft">MSDK Aircraft</string>
    <string name="app_name_handheld">MSDK Handheld</string>

    <string name="item_key_value_title">Key/Value</string>
    <string name="item_key_value_description">Demonstrate how to use the Key/Value function</string>

    <string name="item_multi_video_decoding_title">Multi-Video Decoding (VideoStreamManager - Deprecated)</string>
    <string name="item_multi_video_decoding_description">Demonstrate how to use the Multi-Video Decoding function</string>

    <string name="item_multi_camera_stream_title">Multi-Video Decoding (CameraStreamManager - New)</string>
    <string name="item_multi_camera_stream_description">Demonstrate how to use the Multi-Video Decoding function</string>
    <string name="my_app_name">光伏巡检</string>

    <string name="item_waypoint_title">Waypoint</string>
    <string name="item_waypoint_description">Demonstrate how to use the waypoint flight function</string>
    <string name="mbps4">4Mbps</string>
    <string name="mbps6">6Mbps</string>
    <string name="mbps8">8Mbps</string>
    <string name="mbps10">10Mbps</string>

    <string name="item_virtual_stick_title">Virtual Stick</string>
    <string name="item_virtual_description">Demonstrate how to use the virtual stick function</string>
    <string-array name="finished_action">
        <item>@string/finished_action0</item>
        <item>@string/finished_action1</item>
        <item>@string/finished_action2</item>
    </string-array>

    <string name="item_handheld_title">Handheld</string>
    <string name="item_handheld_description">Demonstrate how to use the handheld function</string>
    <string-array name="rtk_mode">
        <item>CMCC网络RTK</item>
        <item>自定义网络RTK</item>
    </string-array>

    <string name="item_diagnostic_title">Diagnostic</string>
    <string name="item_diagnostic_description">Demonstrate how to use the diagnostic function</string>
    <string-array name="turn_mode">
        <item>@string/turn_mode0</item>
        <item>@string/turn_mode1</item>
    </string-array>

    <string name="item_live_stream_title">Live Stream</string>
    <string name="item_live_stream_description">Demonstrate how to use the live stream function</string>

    <string name="item_flight_record_title">Flight Log</string>
    <string name="item_flight_record_description">Demonstrate how to use the Flight Log function</string>

    <string name="item_rtk_title">Network RTK</string>
    <string name="item_rtk_description">Demonstrate how to use the RTK function</string>

    <string name="item_megaphone_title">Megaphone</string>
    <string name="item_megaphone_description">Demonstrate how to use the Megaphone function</string>

    <string name="item_lte_title">LTE</string>
    <string name="item_lte_description">Demonstrate how to use the LTE function</string>

    <string name="item_fly_safe_title">Fly Safe</string>
    <string name="item_fly_safe_description">Demonstrate how to use the Fly Safe function</string>

    <string name="item_data_protection_title">Data Protection</string>
    <string name="item_data_protection_description">Demonstrate how to use the Data Protection function</string>

    <string name="btn_init_msdk">Init MSDK</string>
    <string name="btn_unInit_msdk">UnInit MSDK</string>
    <string name="btn_register_app">Register app</string>
    <string name="btn_enable_ldm">Enable LDM</string>
    <string name="btn_disable_ldm">Disable LDM</string>
    <string name="tip_ldn_choice_title">Please select the LDM exempt module</string>
    <string name="tip_ldn_choice_confirm">Confirm</string>
    <string name="tip_ldn_choice_cancel">Cancel</string>


    <string name="btn_enable_virtual_stick">Enable Virtual Stick</string>
    <string name="btn_disable_virtual_stick">Disable Virtual Stick</string>
    <string name="btn_set_virtual_stick_speed_level">Set Speed Level</string>
    <string name="btn_use_rc_stick_value">Start/Stop Use Rc Stick Value</string>
    <string name="set_virtual_stick_advanced_param">Set Virtual Stick Advanced Param</string>
    <string name="send_virtual_stick_advanced_param">Send Virtual Stick Advanced Param</string>
    <string name="enable_virtual_stick_advanced_mode">Enable Virtual Stick Advanced Mode</string>
    <string name="disable_virtual_stick_advanced_mode">Disable Virtual Stick Advanced Mode</string>

    <string name="btn_take_off">Take Off</string>
    <string name="btn_landing">Landing</string>

    <string name="btn_start_channel">Start Channel</string>
    <string name="btn_close_channel">Close Channel</string>
    <string name="btn_yuv_screen_shot">YUV Screen Shot</string>
    <string name="btn_resume_video">Resume Video</string>
    <string name="btn_start_socket">Start Socket</string>
    <string name="btn_close_socket">Close Socket</string>
    <string name="btn_start_broadcast">Start Broadcast</string>
    <string name="btn_stop_broadcast">Stop Broadcast</string>
    <string name="btn_switch_source">SwitchSource</string>

    <string name="btn_get_flight_record_path">Get Flight Record Path</string>
    <string name="btn_get_fly_compressed_path">Get Fly Compressed Log Path</string>

    <string name="start_pairing">Start Pairing</string>
    <string name="stop_pairing">Stop Pairing</string>

    <string name="n_a">N/A</string>
    <string name="video_stream_manager">Video Stream Manager</string>
    <string name="reset_all_video_channel">Reset All Video Channel</string>
    <string name="get_all_video_channel">Get All Video Channel</string>
    <string name="select_stream_source">Select Stream Source</string>
    <string name="confirm">Confirm</string>
    <string name="cancel">Cancel</string>

    <string name="popup_picker_finish">Finish</string>
    <string name="popup_picker_trigger">Trigger</string>

    <string name="no_product">No product connected</string>
    <string name="wsbridge_ip">WSBridge IP</string>
    <string name="sdk_version">Version: %1$s</string>
    <string name="product_name">Product Name: %1$s</string>
    <string name="is_sdk_debug">Is SDK Debug: %1$s</string>
    <string name="registration_status">Registration Status: %1$s</string>
    <string name="package_product_category">Package Category: %1$s</string>
    <string name="remote_controller">Remote Controller Only</string>
    <string name="unregistered">Unregistered</string>
    <string name="registered">Registered</string>
    <string name="logs">Logs</string>
    <string name="news">Notification</string>
    <string name="showcase">Showcase &amp; Tools</string>
    <string name="widget_list">Widget List</string>
    <string name="testing_tools">Testing Tools</string>
    <string name="default_layout">Default Layout</string>
    <string name="sdk_forum">SDK Forum</string>
    <string name="release_nodes">Release Nodes</string>
    <string name="tech_support">Tech Support</string>

    <!-- Data Protection -->
    <string name="switch_product_improvement">Product Improvement Project</string>
    <string name="switch_product_improvement_on">Agree</string>
    <string name="switch_product_improvement_off">Disagree</string>

    <!--Megaphone-->
    <string name="btn_start_audio_mode">Audio Record</string>
    <string name="btn_start_tts_mode">TTS</string>
    <string name="btn_file_list">File List</string>
    <string name="upload_status">Upload Status</string>
    <string name="record_btn_title">Click To Start Record</string>
    <string name="quick_play">Quick Play</string>
    <string name="btn_send_tts_data">Send TTS</string>
    <string name="title_megaphone">Megaphone</string>
    <string name="megaphone_state">Megaphone State</string>
    <string name="megaphone_control">Megaphone Control</string>
    <string name="megaphone_index">Megaphone Index</string>
    <string name="rt_transmission_status">Real-time Transmission Status:</string>
    <string name="btn_tts">TTS</string>
    <string name="btn_rt_voice">Real-time Voice</string>
    <string name="btn_audio_list">Audio List</string>
    <string name="cur_volume_value">current volume:</string>


    <string name="item_simulator_title">Simulator</string>
    <string name="item_simulator_description">Demonstrate how to use the Simulator function</string>
    <string name="btn_enable_simulator">Enable Simulator</string>
    <string name="btn_disable_simulator">Disable Simulator</string>
    <string name="btn_update_areacode">UpdateAreaCode</string>

    <string name="item_upgrade_title">Upgrade</string>
    <string name="item_upgrade_description">Demonstrate how to use the Upgrade function</string>

    <!--Network RTK-->
    <string name="btn_start_custom_network_rtk_service">Start Custom NetWork RTK</string>
    <string name="btn_stop_custom_network_rtk_service">Stop Custom NetWork RTK</string>
    <string name="btn_set_custom_network_rtk_settings">Set custom network RTK account information</string>
    <string name="btn_start_qx_network_rtk_service">Start QX NetWork RTK</string>
    <string name="btn_stop_qx_network_rtk_service">Stop QX NetWork RTK</string>
    <string name="btn_set_qx_network_rtk_coordinate_system">Set QX NetWork RTK Coordinate System</string>

    <!--CMSS RTK-->
    <string name="btn_start_cmcc_rtk_service">Start CMCC RTK</string>
    <string name="btn_stop_cmcc_rtk_service">Stop CMCC RTK</string>


    <!--PSDK-->
    <string name="item_psdk_title">PSDK Test</string>
    <string name="item_psdk_description">Demonstrate how to use PSDK related functions</string>
    <string name="btn_send_data_to_psdk">Send Data To PaySdk</string>
    <string name="btn_get_data_from_psdk">Get data From PaySdk</string>
    <string name="tv_send_data_to_psdk_hint">Please enter test data sent to PSDK</string>

    <string name="btn_mission_uploadkmz">uploadKmz</string>
    <string name="btn_mission_start">Start Mission</string>
    <string name="btn_mission_pause">Pause Misson</string>
    <string name="btn_mission_resume">Resume Mission</string>
    <string name="btn_mission_query">Query BreakPoint</string>
    <string name="btn_mission_resume_with_bp">Resume Mission(BPInfo)</string>
    <string name="btn_mission_stop">Stop Mission</string>
    <string name="btn_mission_wayline_select">Select Wayline ID</string>
    <string name="btn_mission_breakinfo_resume">Start From BreakPoint</string>
    <string name="btn_get_payload_product_name">Get Payload Product Name </string>

    <!-- StationRTK   -->
    <string name="item_station_trk_title">Station RTK Test</string>
    <string name="item_station_trk_description">Demonstrate how to use base station RTK related functions</string>
    <string name="btn_start_search_station">Start Search RTK Station </string>
    <string name="btn_stop_search_station">Stop Search RTK Station </string>
    <string name="btn_login">Login As Admin </string>
    <string name="btn_set_station_position">Set Station Position </string>
    <string name="btn_get_station_position">Get Station Position </string>
    <string name="btn_reset_station_position">Reset Station Position </string>
    <string name="btn_reset_station_password">Reset Station Password </string>
    <string name="btn_set_station_name">Set Station Name </string>
    <string name="tv_station_name">Station Name: </string>
    <string name="tv_station_id">Station Id: </string>
    <string name="tv_station_signal_level">Station Signal Level: </string>
    <string name="tv_station_battery_current">Station Battery Current: </string>
    <string name="tv_station_battery_temperature">Station Battery Temperature: </string>
    <string name="tv_station_battery_voltage">Station Battery Voltage: </string>
    <string name="tv_station_battery_capacity_percent">Station Battery Capacity Percent: </string>
    <string name="tv_station_location">Station Location：</string>
    <string name="tv_station_relative_distance">Relative Distance %1$s m</string>
    <string name="tv_station_list_tip">Please click the list below for base station connection</string>
    <string name="switch_rtk_type_tip">Switching RTK service type requires restarting the aircraft</string>
    <string name="tip_input_station_password">Enter the password, note that the password must be a string consisting of 6 numbers from 0-9, such as "012345"</string>
    <string name="tip_reset_station_password">Reset the password, note that the password must be a string consisting of 6 numbers from 0 to 9, such as "012345"</string>
    <string name="tip_input_station_location">Enter base station coordinates</string>
    <string name="tip_change_station_name">Modify the base station name, note: the base station name only takes 4 bytes under UTF-8, such as setting the name to "abcdef", the final effect is "abcd"</string>

    <!-- RTK Center   <-->
    <string name="item_trk_center_title">RTK Center</string>
    <string name="item_trk_center_description">Demonstrate how to use RTK related functions</string>
    <string name="tv_enable_rtk_hint">1.To set the RTK module on the drone to on or off, it must be set before the drone blade starts to rotate. After the start, the call is invalid.</string>
    <string name="btn_enable_rtk">Open RTK</string>
    <string name="btn_disable_rtk">Close RTK</string>
    <string name="tv_rtk_enable_hint">2. Get RTK open status: </string>
    <string name="tv_rtk_is_on">RTK is on</string>
    <string name="tv_rtk_is_off">RTK is off</string>
    <string name="tv_rtk_source">3. Select RTK service type</string>
    <string name="btn_rtk_source_network">Custom Network RTK</string>
    <string name="btn_rtk_source_qx">Chihiro RTK</string>
    <string name="btn_rtk_source_base_rtk">D-RTK2</string>
    <string name="btn_rtk_source_cmcc_rtk">CMCC</string>
    <string name="bt_open_network_rtk">Open  network RTK </string>
    <string name="bt_open_rtk_station">Open D-RTK2</string>
    <string name="bt_open_cmcc_rtk">Open CMCC RTK</string>
    <string name="tv_rtk_precision_preservation_hint">4.When the communication of the RTK module is abnormal, the current RTK state will be automatically maintained, but the accuracy will gradually decrease. If the connection is not reconnected for more than 10 minutes, the RTK will be automatically exited</string>
    <string name="bt_turn_on_rtk_precision_preservation">Turn on RTK precision preservation</string>
    <string name="bt_turn_off_rtk_precision_preservation">Turn off RTK precision preservation</string>
    <string name="tv_rtk_precision_preservation_open_status">5.RTK precision remains on:</string>
    <string name="tv_rtk_precision_preservation_turn_on">RTK precision hold is on:</string>
    <string name="tv_rtk_precision_preservation_turn_off">RTK precision remains on:</string>

    <string name="tv_rtk_location_hint">6.RTKLocationInfo information display</string>
    <string name="tv_rtk_location">The positioning strategy of RTK module：</string>
    <string name="tv_rtk_station_position">The location of the ground-side RTK Station：</string>
    <string name="tv_rtk_mobile_position">UAV position calculated by RTK module：</string>
    <string name="tv_rtk_position_std_distance">The distance between the position of the ground base station and the position of the UAV calculated by the RTK module：</string>
    <string name="tv_rtk_std_position">The standard deviation of the drone position calculated by the RTK module：</string>
    <string name="tv_rtk_head">UAV orientation data calculated by RTK module：</string>
    <string name="tv_rtk_real_head">The orientation data of the UAV calculated by the flight control fusion algorithm：</string>
    <string name="tv_rtk_real_location">The position data of the UAV calculated by the flight control fusion algorithm：</string>
    <string name="tv_rtk_systemState_info_hint">7. RTKSystemState information display: </string>
    <string name="tv_rtk_connect">Flight controller and fuselage RTK connection status: </string>
    <string name="tv_rtk_healthy">RTK module data health status：</string>
    <string name="tv_rtk_keep_status">RTK precision remains on: </string>

    <string name="tv_rtk_error">RTK module error message: </string>
    <string name="tv_rtk_antenna_1">The number of satellites searched by the antenna 1 of the UAV RTK module：</string>
    <string name="tv_rtk_antenna_2">The number of satellites searched by the antenna 2 of the UAV RTK module：</string>
    <string name="tv_rtk_station">The number of satellites searched by the RTK: </string>

    <!--Login-->
    <string name="btn_login_account">Account login with UI</string>
    <string name="btn_logout_account">Logout account</string>
    <string name="btn_user_login">Account login without UI(Only Support email login)</string>
    <string name="btn_get_login_info">Get logged info </string>
    <string name="tv_login_state">Current login status: </string>
    <string name="tv_login_account">Current login account: </string>
    <string name="tv_login_error">Login error: </string>
    <string name="tv_verification_code">Verification_code: </string>
    <string name="item_login_account_title">Login Account </string>
    <string name="item_login_account_description">Demonstrate DJI account login related functions</string>

    <!--Perception-->
    <string name="item_perception_title">Perception module</string>
    <string name="item_perception_description">Demonstrate how to use the related functions of perception</string>
    <string name="bt_set_obstacle_avoidance_sub_switch">1.Set the obstacle avoidance on state of a specific direction：</string>
    <string name="tv_sense_switch_status">Sensing switch status：</string>
    <string name="bt_set_obstacle_avoidance_upwrard_switch">Upward</string>
    <string name="bt_set_obstacle_avoidance_down_switch">Down</string>
    <string name="bt_set_obstacle_avoidance_horizontal_switch">Horizontal</string>
    <string name="tv_radar_switch_status">Radar switch status：</string>
    <string name="bt_set_radar_obstacle_avoidance_upwrard_switch">Upward</string>
    <string name="bt_set_radar_obstacle_avoidance_horizontal_switch">Horizontal</string>
    <string name="bt_set_vision_positioning_enable_tip">2.Visual positioning enabled：</string>
    <string name="bt_set_precision_landing_enable_tip">3.Precise landing enabled：</string>
    <string name="tv_other_obstacle_avoidance_tip">4.Other obstacle avoidance information display： </string>
    <string name="bt_set_obstacle_avoidance_type">Set obstacle avoidance type</string>
    <string name="bt_set_obstacle_avoidance_warning_distance">Set the alarm distance in a specific direction</string>
    <string name="bt_set_obstacle_avoidance_braking_distance">Set the stopping distance in a specific direction</string>

    <!--MOP-->
    <string name="item_mop_title">MOP</string>
    <string name="item_mop_description">Demonstrate how to use the related functions of MOP</string>
    <string name="btn_connect">Connect the MOP link</string>
    <string name="btn_disconnect">Disconnect the MOP link</string>

    <!--Security Code-->
    <string name="item_security_code_title">Security Password</string>
    <string name="item_security_code_description">Demonstrate how to use security password related features</string>
    <string name="btn_verify_password">Verify Password</string>
    <string name="btn_modify_password">Modify Password</string>
    <string name="btn_reset_password">Reset Password</string>
    <string name="btn_set_password">Set Password</string>
    <string name="tip_set_password">The length of the password should be at least 4 digits. It is recommended to use numbers, uppercase and lowercase letters to form the password.</string>
    <string name="tip_reset_password">Resetting the security password will format the SD card, please make sure the media data is backed up</string>
    <string name="tip_set_password_fail">The SD card of the current device is encrypted and does not support repeated password settings. If you need to change the password, please click the Change Password function.</string>
    <string name="tip_set_password_not_support">The current device SD does not support encryption and cannot set a password</string>
    <string name="tip_verify_password_fail">The current device SD card does not need to be decrypted</string>
    <string name="tip_modify_password_not_support">The current device SD has not set a password, please set a password first</string>
    <string name="tip_has_set_password_already">The current device SD has been set with a password and cannot continue to set the password. You can choose to change your password or reset your password</string>
    <string name="tip_device_type">MASTER means aircraft, SLAVE1 means left gimbal, SLAVE2 means right gimbal, SLAVE3 means upper gimbal</string>

    <string name="securitycode_operation_result_success">Operation success</string>
    <string name="securitycode_operation_result_running">System busy. Try again later</string>
    <string name="securitycode_operation_result_busy">System busy. Try again later</string>
    <string name="securitycode_operation_result_set_failed">Setting security code failed. Try again</string>
    <string name="securitycode_operation_result_verify_failed">Verifying security code failed</string>
    <string name="securitycode_operation_result_new_pw_repeat">New security code is the same as current security code. Try again</string>
    <string name="securitycode_operation_result_reset_failed">Disabling security code error</string>
    <string name="securitycode_operation_result_not_current_device_result">Another device error</string>
    <string name="securitycode_operation_result_unknown">Unknown error</string>
    <string name="securitycode_operation_result_user_name_format_invalid">Username contains invalid characters</string>
    <string name="securitycode_operation_result_control_not_support">Operation not supported</string>
    <string name="securitycode_operation_result_feature_not_support">Feature not supported</string>
    <string name="securitycode_operation_result_command_not_support">Command not supported</string>
    <!--UAS-->
    <string name="item_uas_title">UAS module</string>
    <string name="item_uas_description">Demonstrate how to use the related functions of uas</string>
    <string name="bt_set_eid_enable">Set electronicId enabled</string>
    <string name="bt_set_ua_registration_number">Set ua registration number</string>


    <!-- key Type-->
    <string name="key_type_battery">battery key</string>
    <string name="key_type_gimbal">gimbal key</string>
    <string name="key_type_camera">camera key</string>
    <string name="key_type_wifi">WiFi key</string>
    <string name="key_type_ble">BLE key</string>
    <string name="key_type_product">Product key</string>
    <string name="key_type_rtk_base">RTK Base Station Key</string>
    <string name="key_type_rtk_mobile">RTK Mobile Station Key</string>
    <string name="key_type_ocu_sync">OcuSync Key</string>
    <string name="key_type_radar">Radar Key</string>
    <string name="key_type_app">App Key</string>
    <string name="key_type_camera_action">action key</string>
    <string name="key_type_camera_listen">listen key</string>
    <string name="key_type_camera_get_set">set、get key</string>
    <string name="home_operate_disconnected_tips">device disconnect</string>
    <string name="operate_case_filter">Filter</string>
    <string name="operate_select_item_for_setting">Select Item</string>
    <string name="operate_listen_record_tips">push info：</string>
    <string name="operate_pls_select_key_first_tips">please select key firtst</string>
    <string name="battery">Battery</string>
    <string name="batterybox">BatteryBox</string>
    <string name="bigbattery">BigBattery</string>
    <string name="gimbal">Gimbal</string>
    <string name="camera">Camera</string>
    <string name="wifi">WiFi</string>
    <string name="ble">BLE</string>
    <string name="airlink">AIRLINK</string>
    <string name="flight_assistant">FlightAssistant</string>
    <string name="flight_control">FlightController</string>
    <string name="product">Product</string>
    <string name="app_key">AppKey</string>
    <string name="rtkbase">RTK Base</string>
    <string name="rtkmobile">RTK mobile</string>
    <string name="ocusync">OcuSync</string>
    <string name="radar">Radar</string>
    <string name="mobile_network">Mobile Network</string>
    <string name="on_board">OnBoard</string>
    <string name="payload">Payload</string>
    <string name="lidar">Lidar</string>
    <string name="intelligent_box">Intelligent Box</string>
    <string name="key">Key List</string>
    <string name="key_search">Search</string>
    <string name="commonlyused_key">Commonly Used Key</string>
    <string name="key_value_set">Set </string>

    <!-- Log Info -->
    <string name="btn_get_latest_log_info">Update Crash Log Info</string>
    <string name="item_log_info_title">Crash Log Info </string>
    <string name="item_log_info_description">Demonstrate Crash Log Info functions</string>
    <string name="btn_test_java_crash">Test Java Crash</string>
    <string name="btn_test_native_crash">Test Native Crash</string>

    <!-- Live Stream -->
    <string name="btn_get_live_stream_config">Get Live Stream Config</string>
    <string name="btn_set_live_stream_config">Set Live Stream Config</string>
    <string name="btn_start_live_stream">Start Live Stream</string>
    <string name="btn_stop_live_stream">Stop Live Stream</string>
    <string name="btn_set_live_stream_channel_type">Set Live Stream Channel Type</string>
    <string name="btn_get_live_stream_channel_type">Get Live Stream Channel Type</string>
    <string name="btn_set_live_stream_quality">Set Live Stream Quality</string>
    <string name="btn_get_live_stream_quality">Get Live Stream Quality</string>
    <string name="btn_set_live_stream_bit_rate_mode">Set Live Stream Bite Rate Mode</string>
    <string name="btn_get_live_stream_bit_rate_mode">Get Live Stream Bite Rate Mode</string>
    <string name="btn_set_live_stream_bit_rate">Set Live Stream Bite Rate</string>
    <string name="btn_get_live_stream_bit_rate">Get Live Stream Bite Rate</string>
    <string name="btn_enable_audio">Enable Audio</string>
    <string name="btn_disable_audio">Disable Audio</string>
    <string name="ad_select_live_stream_type">Select Live Stream Type</string>
    <string name="ad_select_live_stream_channel_type">Select Live Stream Channel Type</string>
    <string name="ad_select_live_stream_bit_rate_mode">Select Live Stream Bit Rate Mode</string>
    <string name="ad_select_live_stream_quality">Select Live Stream Quality</string>
    <string name="ad_set_live_stream_bit_rate">Set Live Stream bit Rate</string>

    <string name="ad_set_live_stream_rtmp_config">RTMP Config</string>
    <string name="ad_set_live_stream_rtsp_config">RTSP Config</string>
    <string name="ad_set_live_stream_gb28181_config">GB28181 Config</string>
    <string name="ad_set_live_stream_agora_config">Agora Config</string>

    <string name="ad_confirm">Confirm</string>
    <string name="ad_cancel">Cancel</string>


    <string name="msg_start_live_stream_success">Start Live Stream Success!</string>
    <string name="msg_start_live_stream_failed">Start Live Stream Failed: %1$s</string>
    <string name="msg_stop_live_stream_success">Stop Live Stream Success!</string>
    <string name="msg_stop_live_stream_failed">Stop Live Stream Failed: %1$s</string>
    <string name="index_tips">index:represent the position of the load is generally 0 \n subType:represent lens ,0 = zoom ,1 = wide , 2 = ir \n subIndex:currently only the battery box is used to indicate the location of the next level gadgets</string>

    <string name="item_media_playback_title">Media Playback</string>
    <string name="item_media_playback_description">Demonstrate how to use the media playback function</string>

    <string name="msg_download_compelete_tips">Download compelete ,average download speed</string>
    <string name="msg_download_save_tips">File is saved in</string>


    <string name="delete_files">delete file</string>
    <string name="select_files">select file</string>
    <string name="download_files">download file</string>
    <string name="set_xmp_custom_info">set xmp custom info</string>
    <string name="get_xmp_custom_info">get xmp custom info</string>
    <string name="unselect_files"> unselect file</string>
    <string name="fetch_files_list">fetch media file list</string>
    <string name="take_photo">take photo</string>
    <string name="already_download">file already download</string>

    <!-- LTE -->
    <string name="btn_update_lte_authentication_info">Update LTE Authentication Info</string>
    <string name="btn_get_lte_authentication_verification_code">Get LTE Authentication Verification Code</string>
    <string name="btn_start_lte_authentication">Start LTE Authentication</string>
    <string name="btn_set_lte_enhanced_transmission_type">Set LTE Enhanced Transmission Type</string>
    <string name="btn_get_lte_enhanced_transmission_type">Get LTE Enhanced Transmission Type</string>
    <string name="btn_set_lte_ac_privatization_server_info">Set LTE Aircraft Privatization Server Info</string>
    <string name="btn_set_lte_rc_privatization_server_info">Set LTE RemoteController Privatization Server Info</string>
    <string name="btn_clear_lte_ac_privatization_server_info">Clear LTE Aircraft Privatization Server Info</string>
    <string name="btn_clear_lte_rc_privatization_server_info">Clear LTE RemoteController Privatization Server Info</string>

    <!-- Fly Safe -->
    <string name="btn_btn_hide_or_show_all_fly_zone_info">Hide/Show Fly Zones Info</string>
    <string name="btn_get_fly_zones_in_surrounding_area">Get Fly Zones In Surrounding Area(50000M)</string>
    <string name="btn_download_fly_zone_licenses_from_server">Download Fly Zone Licenses From Server</string>
    <string name="btn_push_fly_zone_licenses_to_aircraft">Push Fly Zone Licenses To Aircraft</string>
    <string name="btn_pull_fly_zone_licenses_from_aircraft">Pull Fly Zone Licenses From Aircraft</string>
    <string name="btn_delete_fly_zone_licenses_from_aircraft">Delete Fly Zone Licenses From Aircraft</string>
    <string name="btn_set_fly_zone_licenses_enabled">Set Fly Zone Licenses Enabled</string>
    <string name="btn_unlock_authorization_fly_zone">Unlock Authorization Fly Zone</string>
    <string name="btn_unlock_enhanced_warning_fly_zone">Unlock Enhanced Warning Fly Zone</string>
    <string name="btn_unlock_all_enhanced_warning_fly_zone">Unlock All Enhanced Warning Fly Zone</string>

    <!-- Camera preview -->
    <string name="title_select_yuv_format">Select Image Format</string>
    <string name="title_select_yuv_format_ok">OK</string>
    <string name="title_select_yuv_format_cancel">Cancel</string>
    <string-array name="lb_video_format">
        <item>720P60</item>
        <item>720P50</item>
        <item>1080I60</item>
        <item>1080I50</item>
        <item>1080P60</item>
        <item>1080P50</item>
        <item>1080P30</item>
        <item>1080P24</item>
        <item>1080P25</item>
        <item>720P30</item>
        <item>720P25</item>
        <item>720P24</item>
    </string-array>

    <!-- Look At preview -->
    <string name="item_look_at_title">Look At</string>
    <string name="item_look_at_description">Demonstrate how to use look at</string>

    <string name="area_polygon">多边形</string>
    <string name="area_circle">圆形</string>
    <string name="area_line">线形</string>

    <string name="waypoint_speed">飞行速度</string>
    <string name="waypoint_altitude">飞行高度</string>
    <string name="custom_each_point_aircraft_heading">飞行器偏航角</string>
    <string name="custom_each_point_gimbal_pitch_angle">云台俯仰角度</string>

    <string name="turn_mode0">顺时针</string>
    <string name="turn_mode1">逆时针</string>
    <string name="finished_action0">自动返航</string>
    <string name="finished_action1">悬停</string>
    <string name="finished_action2">自动降落</string>

    <string name="reset_gimbal_param">云台重置参数完成</string>

    <string name="sensor">传感器</string>

    <string name="rc_connected_only">仅连接遥控器</string>
    <string name="aircraft_disconnected">无人机未连接</string>
    <string name="connected">已连接</string>
    <string name="please_check_connection">请检查连接线</string>

    <string name="stop">停止</string>
    <string name="live_connecting">连接中</string>

    <string name="live_connected">已连接</string>
    <string name="live_disconnected">推流已停止</string>

    <string name="yes">是</string>
    <string name="no">如何解决?</string>

    <string name="start_take_off">开始自动起飞</string>
    <string name="take_off_failed">自动起飞失败</string>
    <string name="right_move_fly">> 向右滑动执行飞行 ></string>
    <string name="calibration_suc">校准成功</string>
    <string name="calibration_fail">无法校准</string>
    <string name="calibration_obstructed">校准失败，环境干扰强</string>
    <string name="compass_calibration_start">将进行指南针校准,请确保飞机远离电线 铁磁性物体等.防止电磁干扰</string>
    <string name="land_gear_mode_hint">进入运输模式前,请确认是否放置在光滑,硬的平地上,且已将云台拆卸.如果无法正常进入运输模式,可以使用手动模式.</string>
    <string name="video_title">视频字幕</string>
    <string name="remove_camera">请移除相机,飞行器脚架会自动移动,进入运输模式</string>
    <string name="gimbal_roll_adjust">云台横滚轴微调</string>
    <string name="gimbal_reset">回中/朝下</string>
    <string name="dialog_cancel">取消</string>
    <string name="dialog_hint">提示</string>
    <string name="dialog_ok">确定</string>
    <string name="dialog_ok1">好的</string>
    <string name="del_suc">删除成功</string>
    <string name="good">好</string>
    <string name="bad">差</string>
    <string name="unsupported">不支持</string>
    <string name="set_success">设置成功</string>
    <string name="not_support_GPS">不支持此功能或GPS太弱</string>
    <string name="video_source1">相机画面</string>
    <string name="picture_in_picture">画中画</string>
    <string name="left_top">左上</string>
    <string name="right_top">右上</string>
    <string name="left_down">左下</string>
    <string name="right_down">右下</string>
    <string name="compass_has_Calibration">指南针需校准</string>
    <string name="compass_Calibration_ok">指南针正常</string>
    <string name="pause">暂停</string>
    <string name="finish">完成</string>

    <string name="no_fly_zone_approach">飞机正在接近禁飞区</string>
    <string name="no_fly_zone_inner">您正在禁飞区内</string>

    <string name="fly_first_route">为保证飞行安全，请先起飞无人机</string>
    <string name="fly_first_flight">请先起飞无人机</string>
    <string name="fly_auto_home">正在返航或降落，不能执行任务</string>
    <string name="set_home_first">为保证飞行安全，请等待返航点刷新</string>
    <string name="choose_mission_first">请先选择任务</string>
    <string name="unknown_name">未命名</string>
    <string name="unknown">未知</string>
    <string name="fly_area_apply_lng">经度</string>
    <string name="fly_area_apply_lat">纬度</string>
    <string name="temperature">温度</string>
    <string name="mission_complete">任务执行完毕</string>
    <string name="sensor_management">传感器管理</string>
    <string name="module_diagnostics">模块自检</string>
    <string name="imu">IMU</string>
    <string name="login_again">重新登录</string>
    <string name="fly_area_apply_draw">请绘制飞行区域</string>
    <string name="compass">指南针</string>
    <string name="channel_quality">无线信道质量</string>
    <string name="flight_mode">飞行模式</string>
    <string name="rc_mode">遥控器模式</string>
    <string name="aircraft_battery">飞行器电量</string>
    <string name="rc_battery">遥控器电量</string>
    <string name="aircraft_battery_temperature">飞行器电池温度</string>

    <string name="transport_mode_enter_exit">退出/进入运输模式</string>
    <string name="gimbal_status">云台状态</string>
    <string name="list_signal_warn">环境干扰较强,请谨慎飞行.</string>
    <string name="hz60">60HZ</string>
    <string name="hz50">50HZ</string>
    <string name="compass_dialog">成功进入校准指南针模式,请远离金属或带强
        电物体等,并在离地1.5m(4.9ft)左右,水平旋转飞行器360度</string>
    <string name="compass_dialog_horizontal">水平旋转飞行器360度</string>
    <string name="compass_dialog1_vertical">垂直旋转飞行器360度</string>
    <string name="op_fail">操作失败</string>
    <string name="gimbal_mode_follow">跟随模式</string>
    <string name="gimbal_mode_FPV">FPV模式</string>
    <string name="gimbal_mode_free">自由模式</string>
    <string name="gimbal_mode_centre">云台归中/相机向下</string>
    <string name="go_home_height">返航高度</string>
    <string name="bat_voltage">电压</string>
    <string name="flight_status_list">飞行器状态列表</string>
    <string name="hand">自定义</string>
    <string name="new_gain">新增</string>
    <string name="cancel_calibration">取消校准</string>
    <string name="finish_calibration">完成校准</string>
    <string name="fly_height">调整高度将作用于失控返航及主动返航,飞行器将上升到当前设置的最低安全返航高度.</string>
    <string name="novice">新手模式开启</string>
    <string name="novice1">新手模式</string>
    <string name="fly_restrict">飞行限制</string>
    <string name="novice_mode">新手模式下,飞行器只能在返航点的30米半径范围内飞行,飞行器飞行速度显著变慢.</string>
    <string name="off_novice">可在飞控设置中关闭新手模式</string>
    <string name="novice_restrict">为了尽快熟悉你的飞行器及操作安全,飞机高度限制为30M(100ft),距离限制为30M(100ft).</string>
    <string name="distance_limit">距离限制</string>
    <string name="plan_survey_mode">测绘模式</string>
    <string name="plan_waypoint_mode">航点模式</string>
    <string name="survey_no_camera_info">未获取到相机信息，仍然上传任务？</string>
    <string name="mission_action_hover">悬停等待</string>
    <string name="mission_action_shoot">拍照</string>
    <string name="mission_action_start_record">开始录影</string>
    <string name="mission_action_stop_record">停止录影</string>
    <string name="mission_action_change_heading">调整飞行器偏航角</string>
    <string name="mission_action_change_gimbal">调整云台俯仰角</string>
    <string name="action_gimbal_angle">飞行器偏航角</string>
    <string name="action_finish">任务完成动作</string>
    <string name="setting_general">通用设置</string>
    <string name="setting_single">单点设置</string>
    <string name="setting_base">基础设置</string>
    <string name="info_not_complete">该航线不满足飞行要求，无法保存</string>
    <string name="info_not_complete1">信息填写不完整</string>
    <string name="choose_camera_first">请选择相机</string>
    <string name="na">N/A</string>
    <string name="fly_time">飞行时间</string>
    <string name="battery_detail">电池详细信息</string>
    <string name="serialnumber">设备序列号</string>
    <string name="did">设备编号</string>
    <string name="temperature_unit">℃</string>
    <string name="v">V</string>
    <string name="mah">mAH</string>
    <string name="battery_remaining">当前电量</string>
    <string name="battery_capacity">电池容量</string>
    <string name="battery_cycle_time">循环次数</string>
    <string name="low_battery_warning">低电量警报</string>
    <string name="severity">严重低电量警报</string>
    <string name="severity1">电量低于严重低电量警告值时,飞机将强制降落.</string>
    <string name="voltage_show">主屏显示电压</string>
    <string name="smart_home">低电量智能返航</string>
    <string name="smart_home_particular">系统计算出电量仅够返回返航点时,飞行器将强制返航.</string>
    <string name="voltage_time">开始自放电时间</string>
    <string name="remote_set_function_mode">系统默认遥控为美国手,更换遥控模式会改变遥控器对飞机的控制方式,
        如果您不熟悉您所选的操作的模式,可能导致飞机炸机,请谨慎操作</string>
    <string name="day">天</string>
    <string name="sdcard_unmounted">SD卡未插入</string>
    <string name="japan">日本手</string>
    <string name="usa">美国手</string>
    <string name="china">中国手</string>
    <string name="remote_calibration1">1.推动所有通道到最大工作范围并重复几次</string>
    <string name="remote_calibration2">2.拨动左边的拨轮</string>
    <string name="start_calibration">开始校准</string>
    <string name="signal">信号干扰较少</string>
    <string name="signal1">信号干扰较多</string>
    <string name="signal2">当前信号</string>
    <string name="signal4">自动选择</string>
    <string name="hd_code">图传码率</string>
    <string name="hd_bind_width">带宽分配</string>
    <string name="P720">720P帧率</string>
    <string name="frequency">工作频段</string>
    <string name="out_of_control">失控行为</string>
    <string name="auxiliary_light">下视辅助照明</string>
    <string name="app_output_mode">App输出模式</string>
    <string name="video_output_mode">视频输出模式</string>
    <string name="video_output_format">视频输出格式</string>
    <string name="app_hdm">APP与HDMI同时输出画面</string>
    <string name="hdmi_sdi_out">HDMI/SDI视频输出</string>
    <string name="he_ext_open">开启EXT端口</string>
    <string name="hdmi_osd">显示OSD</string>
    <string name="margin_top">上边距</string>
    <string name="margin_left">左边距</string>
    <string name="margin_down">下边距</string>
    <string name="margin_right">右边距</string>
    <string name="hdmi">HDMI输出公制单位</string>
    <string name="dbm">噪声(dBm)</string>
    <string name="detected_no_video">检测到没图像</string>
    <string name="radar_obstacle_avoidance">启用s视觉避障系统</string>
    <string name="radar_obstacle_upwards">顶部避障</string>
    <string name="radar_obstacle_avoidance1">飞行器在前视视野中检测到障碍物时,会自动悬停.(前视觉感知系统工作时,最大飞行速度限制为15m/s)</string>
    <string name="radar_obstacle_avoidance2">开启视觉避障系统将会自动开启返航障碍物检测.</string>
    <string name="radar_obstacle_avoidance3">前视感知摄像头的可视角为水平70度,垂直54度,超出视野范围的障碍物将无法被检测.</string>
    <string name="radar_obstacle_avoidance4">注意:前视感知摄像头在光线不足时将无法正常工作;障碍物检测精度与障碍物大小有关,障碍物检测范围为0.7m-30m.</string>
    <string name="radar_obstacle_avoidance_advance">启动下视定位</string>
    <string name="radar_obstacle_avoidance_advance4">降落保护</string>
    <string name="radar_obstacle_avoidance_advance5">打开地面降落保护后,飞机会在降落前检测地面情况.</string>
    <string name="radar_obstacle_avoidance_advance_set">视觉高级设置</string>
    <string name="smart_battery_info">智能电池信息</string>
    <string name="radar_obstacle_avoidance_advance1">下视定位系统帮助你在GPS信号不佳的地方稳定悬停,并对精准降落,平整检测等功能提供支持.</string>
    <string name="radar_obstacle_avoidance_advance2">返航障碍物检测</string>
    <string name="radar_obstacle_avoidance_advance3">光线等条件允许时,飞行器在返航过程中遇到障碍物时会主动刹车并自动升高避开.开启后,即使"启用视觉避障系统"这一设置
    处于关闭状态,飞行器仍将开启返航障碍物检测功能.注意,在自动下降过程中,返航障碍物检测功能将关闭.</string>
    <string name="login_empty">不能为空</string>
    <string name="password_not_equal">两次密码输入不一致</string>
    <string name="channel">信道</string>
    <string name="camera_type">相机型号</string>
    <string name="camera_orientation">相机朝向</string>
    <string name="set_mission_name_first">请填写任务名称</string>
    <string name="quit_task_suc">退出任务成功</string>
    <string name="quit_task_signal">无信号退出</string>
    <string name="shoot_finished">拍摄完成</string>
    <string name="info_camera">相机信息</string>
    <string name="rc_calibration">遥控器校准</string>
    <string name="coach_mode">打开教练模式</string>
    <string name="coach_mode1">打开教练模式后,从机摇杆也可控制飞机飞行.</string>
    <string name="coach_mode2">遥控器自定义按键</string>
    <string name="action_none">无动作</string>
    <string name="action_count_suffix">个动作</string>
    <string name="aircraft_rotate_orientation">飞行器旋转方向</string>
    <string name="action_waypoint">航点动作</string>
    <string name="warning_choose_point">请选择一个航点</string>

    <string name="wp_num_too_many">航线点超出最大限制，请缩小航线范围</string>

    <string name="showLine">显示航线</string>
    <string name="time_time">时间段</string>
    <string name="reasons_refusal">拒绝理由</string>
    <string name="unit">单位</string>
    <string name="english_system">英制</string>
    <string name="km_system">公制(Km)</string>
    <string name="m_system">公制(M)</string>
    <string name="common_setting">通用设置</string>
    <string name="aircraft_param_setting">飞机参数设置</string>
    <string name="rtk_setting">RTK设置</string>
    <string name="battery_set_pager">智能电池信息</string>
    <string name="aircraft_obstacle">感知设置</string>
    <string name="gimbal_setting">云台</string>
    <string name="hd_set_pager">图传设置</string>
    <string name="remote_set_pager">遥控功能设置</string>
    <string name="gimbal_calibration">请保持您的飞机在地面上,并保持水平,按确定后云台将会开始自动校准</string>
    <string name="gimbal_calibration_ok">云台校准成功!</string>
    <string name="gimbal_calibrating">云台正在校准!</string>
    <string name="gimbal_calibration_speed">云台俯仰轴最大速度(1–100)</string>
    <string name="gimbal_calibration_speed1">云台偏航轴最大速度(1–100)</string>
    <string name="gimbal_calibration_speed3">俯仰缓启/停(0–30)</string>
    <string name="gimbal_calibration_speed4">云台偏航缓启/停(0–30)</string>
    <string name="gimbal_calibration_roll">云台Roll轴微调</string>
    <string name="gimbal_calibration_auto">云台自动校准</string>
    <string name="advanced_setting">高级设置</string>
    <string name="advanced">高级</string>
    <string name="replacement">重置所有参数</string>
    <string name="empty" />
    <string name="fpv_setting_beginner_mode_note">为安全起见，飞行器起飞后不允许打开新手模式。</string>
    <string name="flight_mode_change">允许切换飞行模式</string>
    <string name="flight_mode_change_p">P 模式(定位),使用GPS或视觉定位系统实现精准悬停;</string>
    <string name="flight_mode_change_A">A 模式(姿态),不使用GPS和视觉定位系统,不能精准悬停;</string>
    <string name="flight_mode_change_F">F 模式(功能),和P模式类似,可使用智能飞行模式.</string>
    <string name="rocker_pairing_dialog_6">对频完成</string>
    <string name="disconnect">连接断开</string>
    <string name="rocker_mode">摇杆模式</string>
    <string name="rocker_pairing">遥控器对频</string>
    <string name="rocker_pairing_dialog_1">确认对频吗?</string>
    <string name="rocker_pairing_dialog_2">请点击飞行器的对频按钮完成对频</string>
    <string name="rocker_pairing_dialog_3">正在对频，%1$d 秒后超时</string>
    <string name="rocker_pairing_dialog_5">对频超时</string>
    <string name="calibrating">校准</string>
    <string name="failed">校准失败</string>
    <string name="data_exception">数据异常</string>
    <string name="warming_up">准备</string>
    <string name="motion">移动</string>
    <string name="normal">正常</string>
    <string name="abnormal">异常</string>
    <string name="bias_medium">偏差小</string>
    <string name="bias_large">偏差大</string>
    <string name="not_calibration">无法校准</string>
    <string name="slant">倾斜</string>
    <string name="percent">%</string>

    <string name="go_home_location_dialog1">飞行器将设置返航点到当前位置,如果需要时,返航时飞机将会适当调整高度并以不低于%dm的高度返航,请留意地图返航位置更新</string>
    <string name="go_home_location_dialog3">设置当前飞机位置为返航点</string>
    <string name="go_home_location_dialog_rc_gps">设置当前您的位置为返航点</string>
    <string name="gps_signal_weak">GPS太弱,无法获取位置</string>
    <string name="dji_activate_hint">根据大疆创新相关规定，在使用第三方（非大疆官方）应用前，需先登录DJI账号，以激活应用\n否则您将被限飞（限远50M，限高30M）且图传被禁用\n点击确认，我们将指引您进行登录</string>
    <string name="dji_bind_need_network">您的无人机处于未绑定状态，如果您已绑定该无人机，请打开网络连接以同步您最新的无人机绑定信息\n否则您将被限飞（限远50M，限高30M）且图传被禁用</string>
    <string name="dji_bind_need_bind">您的无人机处于未绑定状态，请您先将无人机连接至DJI GO 4/DJI GO以进行绑定，再使用本应用\n否则您将被限飞（限远50M，限高30M）且图传被禁用\n感谢您的配合</string>
    <string name="take_off_tips">当前环境无法精确定位,飞行器将自动起飞到1.2M高度,请确保飞行器处于地面,请勿在狭窄空间,人群中或建筑物旁起飞,手切勿触碰螺旋桨.</string>
    <string name="size">大小:</string>

    <string name="camera_sensor_width">传感器宽度:</string>
    <string name="camera_sensor_height">传感器高度:</string>
    <string name="overlap_rate">重叠率 :</string>
    <string name="gimbal_angle">云台向上角度:</string>
    <string name="camera_focus">焦距 :</string>
    <string name="camera_shoot_time_internal">相机拍照间隔(s)</string>
    <string name="camera_mode_conform">请确认是否是F模式</string>
    <string name="name">名称</string>
    <string name="location">位置</string>
    <string name="sensor_resolution_wide">分辨率（宽）</string>
    <string name="resolution">分辨率</string>
    <string name="sensor_resolution_high">分辨率（高）</string>
    <string name="sensor_size_wide">尺寸（宽）</string>
    <string name="sensor_size_high">尺寸（高）</string>
    <string name="focal_length">焦距</string>
    <string name="focal_length_35">35毫米等效焦距</string>
    <string name="distortion_parameter">畸变参数</string>
    <string name="distortion_parameter_wide">宽</string>
    <string name="distortion_parameter_high">高</string>

    <string name="not_support">功能不支持</string>

    <string name="locating">正在定位</string>

    <string name="select">选择</string>
    <string name="all">全部数据</string>
    <string name="image">图片</string>
    <string name="video">视频</string>
    <string name="not_image">SD卡为空</string>
    <string name="close_novice_tips">关闭新手模式后,飞行速度和灵敏度都将大幅提升,请注意飞行安全.</string>
    <string name="delete_ok">确认删除？</string>
    <string name="position">position</string>
    <string name="message_waiting">正在加载.....</string>

    <string name="compass_error">指南针错误,请校准.</string>
    <string name="attitude_mode">以切换姿态模式,飞行器将不会悬停,请谨慎飞行</string>
    <string name="limit_height">已达最大限高</string>
    <string name="limit_distance">已达最大限远</string>
    <string name="limit_distance_1">限远</string>
    <string name="sdcard_full">已满</string>
    <string name="go_home_point_set">返航点设置</string>
    <string name="advance_high_limit">最大高度限制</string>

    <string name="battery_low_warning">严重低电量，系统强制降落，短按遥控器上的返航按钮停止降落</string>
    <string name="battery_only_go_home">当前电量仅够返回返航点，请尽快返航。</string>
    <string name="battery_only_land">智能低电量</string>
    <string name="battery_overheating">电池过热</string>

    <string name="go_home_update">返航点已刷新</string>

    <string name="take_off">起飞</string>

    <string name="landing_gear_state1">脚架下降完成</string>
    <string name="landing_gear_state2">脚架正在下降</string>
    <string name="landing_gear_state3">脚架展开完成</string>
    <string name="landing_gear_state4">脚架正在展开</string>

    <string name="setting_ui_vision_radar">显示雷达图</string>


    <string name="gimbal_calibrate_tips1">云台校准中 %1$s%%，请不要移动飞机</string>
    <string name="gimbal_calibrate_failed">云台校准失败！</string>

    <string name="deselect">取消选择</string>
    <string name="playback_select_tip">请选择文件</string>

    <string name="request_failed">请求失败</string>
    <string name="request_time_out">请求超时</string>
    <string name="request_no_net">请求失败，请检查网络设置</string>

    <string name="change_drop_hint">您的改动将不会保存，确认退出？</string>
    <string name="gimbal_choose">云台选择</string>
    <string name="gimbal1">云台1</string>
    <string name="gimbal2">云台2</string>
    <string name="more">更多</string>
    <string name="no_fly_search">禁飞区查询</string>
    <string name="search">搜索</string>

    <string name="fpv_show">显示FPV</string>

    <string name="no_fly_area_type_0">危险区</string>
    <string name="no_fly_area_type_1">限制区</string>
    <string name="no_fly_area_type_2">自由飞行区</string>
    <string name="no_fly_area_type_3">民航机场</string>
    <string name="no_fly_area_type_4">通航机场</string>
    <string name="no_fly_area_type_5">直升机机场</string>
    <string name="no_fly_area_type_6">农化林场</string>
    <string name="no_fly_area_type_7">护林机场</string>
    <string name="device_brand_other">其他</string>

    <string name="mission_create_success">创建成功</string>
    <string name="mission_update_success">修改成功</string>

    <string name="shp_file_incomplete">不完整的 SHP 文件，请检查</string>
    <string name="prj_file_lost">未检测到 .prj 坐标系文件，无法完成转换，请检查</string>
    <string name="shp_area_select">请选择绘制区域</string>
    <string name="total_fly_time">总飞行时间</string>
    <string name="total_fly_length">总飞行距离</string>
    <string name="fly_length">飞行距离</string>
    <string name="type">类型</string>
    <string name="area">面积</string>
    <string name="fly_param">飞行参数</string>
    <string name="fly_route_detail">航线详情</string>

    <string name="fly_mission_start_succeed">任务开始成功</string>
    <string name="fly_mission_start_failed">任务开始失败</string>
    <string name="fly_mission_pause_succeed">任务暂停成功</string>
    <string name="fly_mission_pause_failed">任务暂停失败</string>
    <string name="fly_mission_resume_succeed">任务恢复成功</string>
    <string name="fly_mission_resume_failed">任务恢复失败</string>
    <string name="fly_mission_stop_succeed">任务停止成功</string>
    <string name="fly_mission_stop_failed">任务停止失败</string>
    <string name="fly_mission_upload_started">任务上传开始</string>
    <string name="fly_mission_upload_finished">任务上传完毕</string>
    <string name="fly_mission_prepare">正在准备任务，请稍后</string>
    <string name="fly_mission_upload_failed">任务上传失败</string>

    <string name="open_failed">打开文件失败</string>
    <string name="import_big_file">数据文件较大，绘制需要较长时间，是否继续？</string>
    <string name="open_location_permission">请打开定位权限</string>
    <string name="import_offline_mission">打开农普任务包</string>
    <string name="offline_mission_title">新建农业普查航线</string>
    <string name="offline_mission_name">当前任务包</string>
    <string name="offline_mission_change">更改</string>
    <string name="offline_mission_file_choose_tips">请选择数据包根目录下的 CONFIG.xml 文件</string>
    <string name="offline_mission_file_invalid_tips">当前任务包数据不完整，请检查</string>
    <string name="setting_channel">版本设置</string>
    <string name="low_battery_setup_failed">设置失败</string>
    <string name="delete_plan_mission">是否删除该任务？</string>
    <string name="taking_photos">正在定时拍照</string>
    <string name="rtk_module">RTK 模块</string>
    <string name="rtk_description">当RTK模块异常时，手动关闭切换回GNSS模式使用（若起飞后再开启RTK开关，导航系统将继续使用GNSS）</string>
    <string name="rtk_location">定位</string>
    <string name="altitude">海拔</string>
    <string name="gps">GPS</string>
    <string name="bei_dou">Beidou</string>
    <string name="glonass">Glonass</string>
    <string name="action_add">添加动作</string>
    <string name="action_modify">修改动作</string>
    <string name="fly_history">历史轨迹</string>
    <string name="routine_point_num">规划路径航点数:</string>
    <string name="mission_start">执行</string>
    <string name="mission_upload">保存到航线库</string>
    <string name="mission_execute">执行</string>
    <string name="save_history_start">开始记录历史轨迹</string>
    <string name="save_history_stop">停止记录历史轨迹</string>
    <string name="receive_onboard_data">接受传感器数据</string>
    <string name="send_onboard_data">发送传感器数据</string>

    <string name="no_action">尚未添加动作</string>
    <string name="shoot_number">拍照点个数:</string>
    <string name="value_temperature_unit">温度单位</string>
    <string name="value_flight_data_unit">飞行数据单位</string>
    <string name="rtk_param">RTK 参数</string>

    <string name="signal_bandwidth_10">10Mhz</string>
    <string name="signal_bandwidth_20">20Mhz</string>
    <string name="image_trans_mode">图传模式</string>
    <string name="downlink_bandwidth">下行带宽</string>

    <string name="up_down">上传下载中</string>
    <string name="downloading_aircraft">正在从无人机下载</string>
    <string name="downloading_warning">下载过程中请勿做其他操作</string>

    <string name="pull_to_refresh_pull_label">Pull to refresh&#8230;</string>
    <string name="pull_to_refresh_release_label">Release to refresh&#8230;</string>
    <string name="pull_to_refresh_refreshing_label">Loading&#8230;</string>

    <!-- Just use standard Pull Down String when pulling up. These can be set for languages which require it -->
    <string name="pull_to_refresh_from_bottom_pull_label">Pull to load more&#8230;</string>
    <string name="pull_to_refresh_from_bottom_release_label">Release to load more&#8230;</string>
    <string name="pull_to_refresh_from_bottom_refreshing_label">@string/pull_to_refresh_refreshing_label</string>
    <string name="yaw_angle_str">偏航角 %1$2s°</string>
    <string name="gps_level_str">GPS等级 %1$2s</string>
    <string name="up_signal_str">上行信号 %1$2d%%</string>
    <string name="down_signal_str">下行信号 %1$2d%%</string>
    <string name="gimbal_pitch_str">云台俯仰角 %1$.0f°</string>

    <string name="app_update_latest_version">Currently the latest version!</string>
    <string name="app_update_start_download">Start download</string>
    <string name="app_update_start_download_hint">You can check the download progress later</string>
    <string name="app_update_start_downloading">Downloading new version</string>
    <string name="app_update_download_completed">Download completed</string>
    <string name="app_update_click_hint">Click to install</string>
    <string name="app_update_download_error">Download error</string>
    <string name="app_update_continue_downloading">Click to continue downloading</string>
    <string name="app_update_background_downloading">Downloading new version in the background…</string>
    <string name="app_update_dialog_new">Version：%s </string>
    <string name="app_update_dialog_new_size">New version size：%s</string>
    <string name="app_update_update">Update</string>
    <string name="app_update_close">Close</string>
    <string name="search_location_pop_hint_str" translatable="false">搜索场站名</string>
    <string name="search_location_pop_now_str" translatable="false">您的位置</string>
    <string name="search_location_pop_more_str" translatable="false">更多场站</string>
</resources>