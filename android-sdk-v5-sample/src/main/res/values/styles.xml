<resources>

    <style name="full_screen_theme" parent="Theme.AppCompat.Light.NoActionBar">
        <item name="android:windowFullscreen">true</item>
        <item name="android:windowContentOverlay">@null</item>
    </style>

    <style name="translucent_theme" parent="Theme.AppCompat.Light.NoActionBar">
        <item name="android:windowFullscreen">true</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowActionBar">false</item>
        <item name="android:windowBackground">@color/transparent</item>
        <item name="android:colorBackgroundCacheHint">@null</item>
        <item name="android:windowIsTranslucent">true</item>
    </style>

    <style name="activity_theme" parent="Theme.AppCompat.NoActionBar">
        <item name="android:windowFullscreen">true</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowActionBar">false</item>
    </style>

    <style name="main_fragment_btn">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">match_parent</item>
        <item name="android:layout_marginStart">3dp</item>
        <item name="android:layout_marginEnd">3dp</item>
        <item name="android:layout_marginTop">0dp</item>
        <item name="android:gravity">center</item>
        <item name="android:color">@color/white</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:autoSizeMaxTextSize">14dp</item>
        <item name="android:autoSizeMinTextSize">9dp</item>
        <item name="android:autoSizeTextType">uniform</item>
    </style>

    <style name="main_fragment_text">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:gravity">center</item>
        <item name="android:padding">8dp</item>
        <item name="android:layout_marginLeft">8dp</item>
        <item name="android:layout_marginTop">8dp</item>
        <item name="android:layout_marginRight">8dp</item>
        <item name="android:enabled">true</item>
        <item name="android:textColor">@color/selector_enable_button</item>
        <item name="android:background">@color/white</item>
        <item name="android:textSize">14sp</item>
    </style>

    <style name="MSDKDialog" parent="@android:style/Theme.Dialog">
        <item name="android:windowFrame">@null</item>
        <item name="android:windowBackground">@color/transparent</item>
        <item name="android:backgroundDimEnabled">true</item>
    </style>

    <style name="left_button_list_button">
        <item name="android:minHeight">@dimen/left_button_list_button_height</item>
        <item name="android:layout_width">@dimen/left_button_list_button_width</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:paddingLeft">@dimen/left_button_list_button_padding_left</item>
        <item name="android:paddingRight">@dimen/left_button_list_button_padding_right</item>
        <item name="android:layout_marginLeft">@dimen/left_button_list_button_margin_left</item>
        <item name="android:layout_marginTop">@dimen/left_button_list_button_margin_top</item>
        <item name="android:background">@color/button_normal</item>
        <item name="android:textSize">@dimen/left_button_list_button_text_size</item>
        <item name="android:textColor">@color/black</item>
        <item name="android:textAllCaps">false</item>
    </style>

    <style name="SwitchTextAppearance" parent="@android:style/TextAppearance.Holo.Small">
        <item name="android:textColor">@color/white</item>
        <item name="android:textSize">@dimen/left_button_list_button_text_size</item>
    </style>

    <style name="AppTheme" parent="Theme.AppCompat.Light.NoActionBar">
        <!-- Customize your theme here. -->
        <item name="colorPrimary">@color/colorPrimary</item>
        <item name="colorPrimaryDark">@color/colorPrimaryDark</item>
        <item name="colorAccent">@color/colorAccent</item>
        <item name="android:statusBarColor">@color/colorPrimary</item>
        <item name="android:navigationBarColor">@android:color/black</item>
    </style>

    <style name="NoTitle">
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowFullscreen">true</item>
    </style>

    <style name="SplashTheme" parent="Base.Theme.AppCompat">
        <item name="android:windowBackground">@drawable/splash_back</item>
        <item name="android:windowFullscreen">true</item>
        <item name="windowNoTitle">true</item>
    </style>


    <style name="AppTheme_StartActivity" parent="Theme.AppCompat.Light.NoActionBar">
        <!-- Customize your theme here. -->
        <item name="colorPrimary">@color/colorPrimary</item>
        <item name="colorPrimaryDark">@color/white</item>
        <item name="colorAccent">@color/colorAccent</item>
        <item name="android:windowLightStatusBar">true</item>
    </style>


    <style name="AppTheme_Dialog" parent="android:Theme.Holo.Light.Dialog">
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowCloseOnTouchOutside">true</item>
        <item name="android:windowBackground">@android:color/transparent</item>
    </style>

    <style name="text13">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textSize">@dimen/text_size_13</item>
        <item name="android:textColor">@color/white</item>
    </style>

    <style name="text14">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textSize">@dimen/text_size_14</item>
        <item name="android:textColor">@color/white</item>
    </style>

    <style name="text12">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textSize">@dimen/text_size_12</item>
        <item name="android:textColor">@color/white</item>
    </style>

    <style name="AircraftStatusList1" parent="text12">
        <item name="android:gravity">center</item>
        <item name="android:padding">@dimen/space_10</item>
        <item name="android:drawablePadding">@dimen/space_5</item>
    </style>

    <!--相机设置__图片设置-->
    <style name="cameraImageView">
        <item name="android:layout_width">@dimen/space_32</item>
        <item name="android:layout_centerVertical">true</item>
        <item name="android:scaleType">fitCenter</item>
        <item name="android:layout_height">@dimen/space_32</item>
        <item name="android:layout_alignParentRight">true</item>
    </style>
    <!--相机设置__图片设置-->
    <style name="cameraStandardImageView">
        <item name="android:layout_width">@dimen/space_30</item>
        <item name="android:scaleType">centerInside</item>
        <item name="android:layout_height">@dimen/space_30</item>
    </style>
    <!--相机设置___文字设置-->
    <style name="cameraTextView" parent="text13">
        <item name="android:padding">@dimen/space_10</item>
    </style>
    <!--相机设置___标准文字设置-->
    <style name="CameraStandardTextView" parent="text14">
        <item name="android:padding">@dimen/space_10</item>
    </style>
    <!--飞机菜单设置___标准文字设置-->
    <style name="AircraftStandardTextView" parent="text14">
        <item name="android:padding">@dimen/gap_big</item>
    </style>
    <!--飞机菜单设置___说明文字设置-->
    <style name="AircraftParticularsTextView" parent="text12">
        <item name="android:textColor">@color/gray</item>
        <item name="android:padding">@dimen/gap_big</item>
    </style>

    <!--HD信号道设置-->
    <style name="signal" parent="text12">
        <item name="android:textSize">@dimen/text_size_8</item>
        <item name="android:gravity">center_vertical</item>
    </style>
    <!--seekbar-->
    <style name="seekbar">
        <item name="android:paddingRight">@dimen/space_10</item>
        <item name="android:paddingLeft">@dimen/space_10</item>
        <item name="android:maxHeight">@dimen/space_2</item>
        <item name="android:minHeight">@dimen/space_2</item>
        <item name="android:progressDrawable">@drawable/po_seekbar</item>
        <item name="android:layout_width">@dimen/space_210</item>
        <item name="android:layout_height">wrap_content</item>
    </style>

    <style name="AircraftSetPadding">
        <item name="android:paddingTop">@dimen/space_5</item>
        <item name="android:paddingBottom">@dimen/space_5</item>
    </style>
    <!--设置界面图片的大小-->
    <style name="SetImage">
        <item name="android:scaleType">centerInside</item>
        <item name="android:layout_weight">1</item>
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">@dimen/space_32</item>
    </style>

    <style name="AddPlanDialog" parent="android:style/Theme.Dialog">
        <item name="android:windowFrame">@null</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowBackground">@color/super_transparent</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:windowContentOverlay">@null</item>
    </style>

    <style name="IMUCalibrationDialog" parent="android:style/Theme.Dialog">
        <item name="android:windowFrame">@null</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowBackground">@color/black</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:windowContentOverlay">@null</item>
    </style>

    <style name="AppCompatAlertDialog" parent="Theme.AppCompat.Light.Dialog.Alert">
        <item name="android:colorAccent">@color/colorAccent</item>
    </style>

    <style name="MyCheckBox" parent="Theme.AppCompat.Light">
        <item name="colorControlNormal">@color/gray</item>
        <item name="colorControlActivated">@color/colorAccent</item>
    </style>

    <style name="SiteList" parent="android:Theme">
        <item name="android:textViewStyle">@style/SiteListText</item>
    </style>
    <style name="SiteListText" parent="android:Widget.TextView">
        <item name="android:textSize">12sp</item>
        <item name="android:textColor">#D9000000</item>
    </style>

    <style name="loadingDialogTheme" parent="@android:style/Theme.Holo.Light.Dialog">
        <item name="android:windowIsFloating">true</item><!-- 是否浮现在activity之上 -->
        <item name="android:windowIsTranslucent">false</item><!-- 半透明 -->
        <item name="android:windowNoTitle">true</item><!-- 无标题 -->
        <item name="android:backgroundDimEnabled">false</item><!-- 无遮罩层 -->
        <!--透明背景-->
        <item name="android:background">@android:color/transparent</item>
        <!--窗口背景透明-->
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowFrame">@null</item>
    </style>

    <style name="EditTextBase" parent="android:Widget.Holo.Light.EditText">
        <item name="android:textAlignment">viewStart</item>
        <item name="android:gravity">start|center_vertical</item>
        <item name="android:textDirection">locale</item>
    </style>

    <!--基础输入框样式-->
    <style name="EditText" parent="EditTextBase">
        <item name="android:layout_height">@dimen/space_36</item>
        <item name="android:textSize">@dimen/text_size_14</item>
        <item name="android:paddingStart">@dimen/space_12</item>
        <item name="android:paddingEnd">@dimen/space_12</item>
        <item name="android:textColor">@color/xui_config_color_black</item>
        <item name="android:textColorHint">@color/xui_config_color_edittext_hint_text</item>
        <item name="android:singleLine">true</item>
        <!--华为手机光标问题解决-->
        <item name="android:imeOptions">normal</item>
    </style>


    <!--EditSpinner-->
    <style name="EditSpinner" parent="EditText">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">@dimen/space_36</item>
        <item name="android:textSize">@dimen/space_14</item>
        <item name="android:textColor">@color/item_location_color</item>
        <item name="android:background">@drawable/xui_config_bg_edittext</item>
    </style>

    <style name="DialogAnimationSlideBottom" parent="android:Animation">
        <item name="android:windowEnterAnimation">@anim/uxsdk_fade_in</item>
        <item name="android:windowExitAnimation">@anim/uxsdk_fade_out</item>
    </style>

    <style name="AppUpdate.DialogActivity" parent="Theme.AppCompat">
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowFrame">@null</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:windowIsTranslucent">true</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:backgroundDimEnabled">false</item>
    </style>

    <style name="AppUpdate.UpdateDialog" parent="AppUpdate.DialogActivity">
        <item name="android:backgroundDimEnabled">true</item>
    </style>

    <declare-styleable name="AppUpdate.NumberProgressBar">
        <attr name="progress_unreached_color" format="color" />
        <attr name="progress_reached_color" format="color" />

        <attr name="progress_text_size" format="dimension" />
        <attr name="progress_text_color" format="color" />
    </declare-styleable>

    <style name="PopupWindowAnimation">
        <item name="android:windowEnterAnimation">@anim/slide_top_in_333</item>
        <item name="android:windowExitAnimation">@anim/slide_top_out_333</item>
        <item name="android:alpha">0.5</item>
        <item name="backgroundColor">@color/black</item>
        <!--        <item name="android:windowFullscreen">true</item>-->
    </style>

    <style name="myTableStyle">
        <item name="android:textSize">@dimen/text_size_14</item>
        <item name="android:textStyle">bold</item>
        <item name="android:textAllCaps">false</item>
    </style>
</resources>