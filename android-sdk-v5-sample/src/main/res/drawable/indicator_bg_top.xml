<?xml version="1.0" encoding="utf-8"?>
<shape xmlns:android="http://schemas.android.com/apk/res/android"
    android:shape="rectangle" >

    <solid android:color="#40000000" />

    <!--
    I know the android:radius is useless here but it's needed to fix an old bug:
    http://code.google.com/p/android/issues/detail?id=939
    -->
    <corners
        android:bottomLeftRadius="@dimen/indicator_corner_radius"
        android:bottomRightRadius="@dimen/indicator_corner_radius"
        android:radius="1dp"
        android:topLeftRadius="0dp"
        android:topRightRadius="0dp" />

</shape>