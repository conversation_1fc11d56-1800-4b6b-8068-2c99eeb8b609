<?xml version="1.0" encoding="utf-8"?>
<!-- 默认按钮样式 -->
<selector xmlns:android="http://schemas.android.com/apk/res/android">

    <!-- 按压状态 -->
    <item android:state_pressed="true"
        android:drawable="@drawable/ic_realtime_recover_task_click">
    </item>

    <!-- 焦点状态 -->
    <item android:state_focused="true"
        android:drawable="@drawable/ic_realtime_recover_task_click">
    </item>

    <!-- 默认状态 -->
    <item android:drawable="@drawable/ic_realtime_recover_task">
    </item>

</selector>