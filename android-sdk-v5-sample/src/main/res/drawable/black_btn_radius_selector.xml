<?xml version="1.0" encoding="utf-8"?>
<!-- 默认按钮样式 -->
<selector xmlns:android="http://schemas.android.com/apk/res/android">

    <!-- 禁用状态 -->
    <item android:state_enabled="false">
        <shape android:shape="rectangle">
            <corners android:radius="@dimen/uxsdk_4_dp" />
            <gradient
                android:endColor="@color/start_flight_control_color_disable"
                android:startColor="@color/start_flight_control_color_disable"/>
        </shape>
    </item>

    <!-- 按压状态 -->
    <item android:state_pressed="true">
        <shape android:shape="rectangle">
            <corners android:radius="@dimen/uxsdk_4_dp" />
            <gradient
                android:endColor="@color/end_flight_control_color_pressed"
                android:startColor="@color/start_flight_control_color_pressed"/>
        </shape>
    </item>

    <!-- 焦点状态 -->
    <item android:state_focused="true">
        <shape android:shape="rectangle">
            <corners android:radius="@dimen/uxsdk_4_dp" />
            <gradient
                android:endColor="@color/end_flight_control_color_pressed"
                android:startColor="@color/start_flight_control_color_pressed"/>
        </shape>
    </item>

    <!-- 默认状态 -->
    <item>
        <shape android:shape="rectangle">
            <corners android:radius="@dimen/uxsdk_4_dp" />
            <gradient
                android:endColor="@color/end_flight_control_color"
                android:startColor="@color/start_flight_control_color"/>
        </shape>
    </item>

</selector>