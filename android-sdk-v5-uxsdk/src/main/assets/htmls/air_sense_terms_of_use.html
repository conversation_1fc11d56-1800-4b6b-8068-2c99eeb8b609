<!--
  ~ Copyright (c) 2018-2020 DJI
  ~
  ~ Permission is hereby granted, free of charge, to any person obtaining a copy
  ~ of this software and associated documentation files (the "Software"), to deal
  ~ in the Software without restriction, including without limitation the rights
  ~ to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
  ~ copies of the Software, and to permit persons to whom the Software is
  ~ furnished to do so, subject to the following conditions:
  ~
  ~ The above copyright notice and this permission notice shall be included in all
  ~ copies or substantial portions of the Software.
  ~
  ~ THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
  ~ IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
  ~ FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
  ~ AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
  ~ LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
  ~ OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
  ~ SOFTWARE.
  ~
  -->

<!DOCTYPE html>
<!-- saved from url=(0048)http://djistatic.com/agreement/dji-go-4-tos.html -->
<html lang="en"><head><meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
	
	<title>DJI AirSense Warnings</title>
	<meta name="viewport" content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
	<meta name="description" content="DJI Pilot App Terms of Use">
	<meta name="keywords" content="djigo4,goapp,dji">
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<meta http-equiv="cleartype" content="on">

	<style> *{margin:0;padding:0}body{font-size:14px;font-family:sans-serif;color:#555}h1{text-align:center;margin:30px 20px;color:#000;font-size:20px}a{color:#000}p{line-height:20px;margin:0 0 20px;word-break:break-all;font-size:14px}strong{font-weight:bold;color:#000}.content{width:80%;margin:0 auto}ol{padding-left:20px}li{margin-bottom:25px;line-height:20px}dt{font-weight:bold;margin-bottom:10px;color:#000}h3{margin-bottom:15px;color:#000}h4{font-weight:bold;color:#333;margin-bottom:10px}ul{padding-left:20px}@media (max-width:640px){.content{width:90%}}
</style>
</head>
<body>

	<h1>DJI AirSense Warnings</h1>
	<div class="content">

		
		<ul>
			
			<li>
				DJI AirSense can detect nearby civil aircrafts and send out warnings only under certain circumstances；it will NOT control DJI aircraft to avoid other aircraft automatically. Make sure to fly with your aircraft within visual line of sight at all times, and always fly with caution. After receiving warnings, lower your aircraft to safe height. In addition, DJI AirSense has the following limitations:

			</li>
			
		</ul>
		<ol>
			<li>
				<dl>
					<dt>
                        DJI AirSense can only receive messages sent from civil aircraft equipped with an ADS-B out device under 1090ES (RTCA DO-260) or UAT (RTCA Do-282) standards. For civil aircraft without ADS-B outs or with malfunctioning ADS-B outs, DJI AirSense cannot receive related broadcasted messages or send out warnings.
					</dt>
				</dl>
			</li>
			<li>
				<dl>
					<dt>
                        When there are obstacles in between a civil aircraft and DJI aircraft, DJI AirSense will fail to receive ADS-B messages sent from civil aircraft or to send out warnings.
					</dt>
				</dl>
			</li>
			<li>
				<dl>
					<dt>
                        DJI AirSense may fail to receive ADS-B messages sent from civil aircraft or send out warnings due to ever changing circumstances and interference. It is highly recommended to fly with caution and stay aware of your surroundings during flight.
					</dt>
				</dl>
			</li>
			<li>
				<dl>
					<dt>
                        DJI AirSense cannot send out warnings when the DJI aircraft cannot accurately determine its location.
					</dt>
				</dl>
			</li>
			<li>
				<dl>
					<dt>
                        DJI AirSense cannot receive ADS-B messages sent from civil aircraft or send out warnings when it is disabled or misconfigured.
					</dt>
				</dl>
			</li>
			
		</ol>
		            
      
	</div>


</body></html>