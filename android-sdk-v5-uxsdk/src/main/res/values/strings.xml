<!--
  ~ Copyright (c) 2018-2020 DJI
  ~
  ~ Permission is hereby granted, free of charge, to any person obtaining a copy
  ~ of this software and associated documentation files (the "Software"), to deal
  ~ in the Software without restriction, including without limitation the rights
  ~ to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
  ~ copies of the Software, and to permit persons to whom the Software is
  ~ furnished to do so, subject to the following conditions:
  ~
  ~ The above copyright notice and this permission notice shall be included in all
  ~ copies or substantial portions of the Software.
  ~
  ~ THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
  ~ IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
  ~ FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
  ~ AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
  ~ LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
  ~ OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
  ~ SOFTWARE.
  ~
  -->

<resources>
    <string name="uxsdk_app_name">DJIUXSDK Beta</string>
    <string name="uxsdk_app_yes">Yes</string>
    <string name="uxsdk_app_no">No</string>
    <string name="uxsdk_app_ok">OK</string>
    <string name="uxsdk_app_save">Save</string>
    <string name="uxsdk_app_cancel">Cancel</string>
    <string name="uxsdk_app_unsetting">Unset</string>
    <string name="uxsdk_app_skip">Skip</string>
    <string name="uxsdk_app_ip_limit">Input is empty or invalid. Please retype.</string>
    <string name="uxsdk_error">Error</string>
    <string name="uxsdk_alert">Alert</string>
    <string name="uxsdk_tips">Tips</string>
    <string name="uxsdk_success">Success</string>
    <string name="uxsdk_enable">Enable</string>
    <string name="uxsdk_disable">Disable</string>
    <string name="uxsdk_not_a_num">N/A</string>

    <string name="uxsdk_user_login_widget_logout">Logout</string>
    <string name="uxsdk_user_login_widget_login">Login</string>
    <string name="uxsdk_user_login_widget_refresh">Refresh</string>
    <string name="uxsdk_user_login_widget_token">Token out of date</string>
    <string name="uxsdk_user_login_widget_not_logged_in">Not Logged In.</string>
    <string name="uxsdk_user_login_widget_logged_in">Logged in</string>
    <string name="uxsdk_user_login_widget_logged_in_not_authorized">Logged in but not authorized to unlock flyzones.</string>

    <string name="uxsdk_string_default_value">N/A</string>

    <string name="uxsdk_battery_percent">%1$d%%</string>
    <string name="uxsdk_battery_voltage_unit">%.2f V</string>

    <string name="uxsdk_gps_rtk_enabled">R</string>

    <string name="uxsdk_home_location_letter">H</string>


    <!-- Telemetry widgets -->
    <string name="uxsdk_unit_meters">m</string>
    <string name="uxsdk_unit_feet">ft</string>
    <string name="uxsdk_value_meters">%1$s m</string>
    <string name="uxsdk_value_feet">%1$s ft</string>
    <string name="uxsdk_unit_meter_per_second">m/s</string>
    <string name="uxsdk_unit_km_per_hr">km/h</string>
    <string name="uxsdk_unit_mile_per_hr">mph</string>
    <string name="uxsdk_altitude_title">H</string>
    <string name="uxsdk_agl_title">H AGL</string>
    <string name="uxsdk_amsl_altitude_title">H AMSL</string>
    <string name="uxsdk_distance_home_title">D</string>
    <string name="uxsdk_distance_rc_title">DRC</string>
    <string name="uxsdk_vps_title">VPS</string>
    <string name="uxsdk_horizontal_velocity_title">H.S.</string>
    <string name="uxsdk_vertical_velocity_title">V.S.</string>

    <string name="uxsdk_video_time_hours">%1$02d:%2$02d:%3$02d</string>
    <string name="uxsdk_video_time">%1$02d:%2$02d</string>

    <!-- picture format -->
    <string name="uxsdk_camera_picture_format_raw">RAW</string>
    <string name="uxsdk_camera_picture_format_jpeg">JPEG</string>
    <string name="uxsdk_camera_picture_format_jpegraw">JPEG+RAW</string>
    <string name="uxsdk_camera_picture_format_tiff">TIFF</string>
    <string name="uxsdk_camera_picture_format_radiometic_jpeg">Radiometric JPEG</string>
    <string name="uxsdk_camera_picture_format_low_tiff">TIFF Linear Low</string>
    <string name="uxsdk_camera_picture_format_high_tiff">TIFF Linear High</string>
    <string name="uxsdk_camera_picture_format_jr">J+R</string>

    <string name="uxsdk_message_air_sense_warning_title">Another aircraft is nearby. Fly with caution.</string>
    <string name="uxsdk_message_air_sense_warning_content">Another aircraft is too close, please descend to a safer altitude.</string>
    <string name="uxsdk_message_air_sense_dangerous_content">Another aircraft is dangerously close, please descend to a safer altitude.</string>
    <string name="uxsdk_air_sense_terms_content">Please make sure you have read and understood the DJI AirSense Warnings.</string>
    <string name="uxsdk_air_sense_never_show">Don\'t show again</string>

    <string name="uxsdk_visual_radar_avoidance_disabled_message_post">Obstacle Avoidance Disabled. Fly with caution.</string>

    <string name="uxsdk_ei_title">EI</string>

    <string name="uxsdk_max_flight_height_limit">Max Flight Altitude: %1$s</string>

    <!-- System Status List -->
    <string name="uxsdk_list_item_flight_mode_title">Flight Mode</string>
    <string name="uxsdk_list_item_aircraft_battery_temp">Aircraft Battery Temperature</string>
    <string name="uxsdk_list_item_radio_quality">Radio Channel Quality</string>
    <string name="uxsdk_celsius_unit">%1$.1f°C</string>
    <string name="uxsdk_fahrenheit_unit">%1$.1f°F</string>
    <string name="uxsdk_kelvins_unit">%1$.1fK</string>
    <string name="uxsdk_list_item_travel_mode">Travel Mode</string>
    <string name="uxsdk_enter_travel_mode_success">The aircraft will automatically retract the landing gear and enter Travel Mode.</string>
    <string name="uxsdk_enter_travel_mode_failed">Failed to enter Travel Mode. %1$s</string>
    <string name="uxsdk_exit_travel_mode_failed">Failed to exit Travel Mode. %1$s</string>
    <string name="uxsdk_travel_mode_enter">Enter</string>
    <string name="uxsdk_travel_mode_exit">Exit</string>
    <string name="uxsdk_travel_mode_enter_confirmation">The aircraft will enter Travel Mode for easier transportation. Place the aircraft on a flat, hard surface and remove the gimbal. If the aircraft does not enter Travel Mode, adjust the landing gear manually.</string>

    <string name="uxsdk_list_item_rc_battery">Remote Controller Battery</string>
    <string name="uxsdk_rc_battery_percent">%1$d%%</string>

    <string name="uxsdk_list_item_rc_stick_mode">Control Stick Mode</string>
    <string name="uxsdk_rc_stick_mode_jp">JP</string>
    <string name="uxsdk_rc_stick_mode_usa">USA</string>
    <string name="uxsdk_rc_stick_mode_ch">CH</string>
    <string name="uxsdk_rc_stick_mode_custom">Custom</string>

    <string name="uxsdk_obstacle_action_type">Obstacle Action</string>
    <string name="uxsdk_obstacle_action_type_close">Close</string>
    <string name="uxsdk_obstacle_action_type_stop">Stop</string>
    <string name="uxsdk_obstacle_action_type_avoid">Avoid</string>

    <string name="uxsdk_list_item_sd_card">SD Card Remaining Capacity</string>
    <string name="uxsdk_list_item_format_button">Format</string>
    <string name="uxsdk_list_item_emmc">eMMC Remaining Capacity</string>

    <string name="uxsdk_storage_status_not_supported">Not Supported</string>
    <string name="uxsdk_storage_status_usb_connected">USB Connected</string>
    <string name="uxsdk_storage_status_missing">Not Inserted</string>
    <string name="uxsdk_storage_status_full">Full</string>
    <string name="uxsdk_storage_status_slow">Slow, %1$s</string>
    <string name="uxsdk_storage_status_write_protect">Write Protected</string>
    <string name="uxsdk_storage_status_not_formatted">Not Formatted</string>
    <string name="uxsdk_storage_status_formatting">Formatting&#8230;</string>
    <string name="uxsdk_storage_status_invalid">Invalid</string>
    <string name="uxsdk_storage_status_busy">Busy</string>
    <string name="uxsdk_storage_status_unknown_error">Unknown Error</string>
    <string name="uxsdk_storage_status_initial">Initializing</string>
    <string name="uxsdk_storage_status_recover_file">Repairing Video Files</string>
    <string name="uxsdk_storage_status_write_slow">Slow Write Speed</string>
    <string name="uxsdk_storage_status_formatting_recommended">Formatting Recommended</string>
    <string name="uxsdk_storage_status_needs_formatting">Formatting Required</string>
    <string name="uxsdk_storage_status_remaining_space_mb">%1$d MB</string>
    <string name="uxsdk_storage_status_remaining_space_gb">%1$.2f GB</string>
    <string name="uxsdk_storage_status_file_indices">No file indices</string>
    <string name="uxsdk_sd_card_format_confirmation">Are you sure you want to format the SD card?</string>
    <string name="uxsdk_sd_card_format_complete">SD card formatting completed.</string>
    <string name="uxsdk_sd_card_dialog_title">SD Card Format</string>
    <string name="uxsdk_sd_card_format_error">Error formatting SD card. %1$s</string>

    <string name="uxsdk_emmc_dialog_title">eMMC Storage Format</string>
    <string name="uxsdk_emmc_format_error">Error formatting internal storage (eMMC). %1$s</string>
    <string name="uxsdk_emmc_format_confirmation">Are you sure you want to format the internal storage (eMMC)?</string>
    <string name="uxsdk_emmc_format_complete">Internal storage (eMMC) formatting completed.</string>

    <string name="uxsdk_list_item_max_flight_distance">Max Flight Distance</string>
    <string name="uxsdk_list_item_max_flight_altitude">Max Flight Altitude</string>
    <string name="uxsdk_limit_required_error">Setting failed. Flight altitude exceeds max limit (400 ft/ 120 m)</string>
    <string name="uxsdk_limit_high_notice">Altering the maximum altitude setting could violate local laws and regulations (a 400 ft / 120 m flight limit is set by the FAA).\nYou are solely responsible and liable for the operation of the aircraft after altering these settings.\nDJI and its affiliates shall not be liable for any damages, whether in contract, tort (including negligence), or any other legal or equitable theory.</string>
    <string name="uxsdk_list_item_value_out_of_range">Failed to set. Input number in range.</string>
    <string name="uxsdk_limit_return_home_warning">Failsafe RTH altitude cannot exceed maximum flight altitude. Failsafe RTH altitude will be set to %1$d ft / %2$d m.</string>

    <string name="uxsdk_list_item_max_return_to_home_altitude">Return-to-Home Altitude</string>
    <string name="uxsdk_rth_error_dialog_message">Return to home altitude cannot exceed maximum flight altitude %1$d.</string>
    <string name="uxsdk_list_rth_dialog_title">Return to Home Altitude</string>
    <string name="uxsdk_rth_success_dialog_message">The return altitude has been changed, please pay attention to flight safety.</string>

    <string name="uxsdk_novice_mode_altitude_meters">30m</string>
    <string name="uxsdk_novice_mode_altitude_feet">100ft</string>
    <string name="uxsdk_novice_mode_distance_meters">30m</string>
    <string name="uxsdk_novice_mode_distance_feet">100ft</string>
    <string name="uxsdk_altitude_range_meters">(%1$d~%2$dm)</string>
    <string name="uxsdk_altitude_range_feet">≈ (%1$d~%2$dft)</string>

    <string name="uxsdk_system_status_disabled">Disabled</string>
    <string name="uxsdk_system_status_normal">Normal</string>


    <!-- System Status List -->
    <string name="uxsdk_system_status_list_title">System Status List</string>

    <string name="uxsdk_list_item_overview_status">Overall Status</string>

    <string name="uxsdk_list_item_novice_mode">Beginner Mode</string>

    <string name="uxsdk_novice_mode_disabled_message">Flight speed and sensitivity will be substantially increased when you turn off Beginner mode. Fly with caution.</string>

    <string name="uxsdk_novice_mode_enabled_message">Beginner mode enabled. Altitude and distance will both be limited to 100ft / 30m.</string>

    <string name="uxsdk_list_item_unit_mode">Unit Mode</string>
    <string name="uxsdk_list_item_unit_mode_imperial">Imperial</string>
    <string name="uxsdk_list_item_unit_mode_metric">Metric</string>
    <string name="uxsdk_dialog_unit_change_notice">Imperial unit mode set successfully. Measurement values will now follow imperial unit system. Since the aircraft supports metric unit system, the values seen are approximate conversions.</string>
    <string name="uxsdk_dialog_unit_change_example">Example: 10m ≈  32ft - 34ft</string>

    <string name="uxsdk_list_item_ssd">SSD Remaining Capacity</string>
    <string name="uxsdk_ssd_not_found">Not Inserted</string>
    <string name="uxsdk_ssd_saving">Saving</string>
    <string name="uxsdk_ssd_formatting">Formatting</string>
    <string name="uxsdk_ssd_initializing">Initializing</string>
    <string name="uxsdk_ssd_error">Error</string>
    <string name="uxsdk_ssd_full">Full</string>
    <string name="uxsdk_ssd_poor_connection">Poor Connection</string>
    <string name="uxsdk_ssd_switching_license">Switching License</string>
    <string name="uxsdk_ssd_formatting_required">Formatting Required</string>
    <string name="uxsdk_ssd_not_initialized">Not Initialized</string>
    <string name="uxsdk_ssd_dialog_title">SSD Storage Format</string>
    <string name="uxsdk_ssd_format_error">Error formatting SSD. %1$s</string>
    <string name="uxsdk_ssd_format_confirmation">Are you sure you want to format the SSD?</string>
    <string name="uxsdk_ssd_format_complete">SSD formatting completed.</string>

    <string name="uxsdk_location_default">N/A, N/A</string>
    <string name="uxsdk_location_coordinates">%1$s, %2$s</string>

    <!-- Auto Exposure Lock -->
    <string name="uxsdk_auto_exposure_lock_widget_display_string">AE</string>

    <!-- Camera Setting Menu -->
    <string name="uxsdk_camera_setting_menu">MENU</string>
    <!-- Focus Mode -->
    <string name="uxsdk_widget_focus_mode_manual">MF</string>
    <string name="uxsdk_widget_focus_mode_auto">AF</string>
    <string name="uxsdk_widget_focus_mode_afc">AFC</string>
    <string name="uxsdk_widget_focus_mode_separator">/</string>

    <string name="uxsdk_camera_exposure_mode_p">AUTO</string>
    <string name="uxsdk_camera_exposure_mode_s">S</string>
    <string name="uxsdk_camera_exposure_mode_a">A</string>
    <string name="uxsdk_camera_exposure_mode_m">M</string>
    <string name="uxsdk_camera_exposure_iso_title">ISO</string>
    <string name="uxsdk_camera_ei">EI</string>

    <!-- Take Off Widget -->
    <string name="uxsdk_take_off_header">Take Off?</string>
    <string name="uxsdk_take_off_message">Ensure that conditions are safe for takeoff. Aircraft will go to an altitude of %1$s and hover.</string>
    <string name="uxsdk_take_off_atti_message">Positioning failed. The aircraft will automatically fly to %1$s above the ground. Do not take off in a narrow space or near crowd or buildings. Do not touch propellers.</string>
    <string name="uxsdk_precision_takeoff">Precisely record takeoff point</string>
    <string name="uxsdk_precision_takeoff_message">With Precision Takeoff Mode enabled, the aircraft will automatically ascend to %1$s. This allows the aircraft to return to home point more accurately with VPS images. Ensure there is sufficient ambient light and that the takeoff route isn\'t blocked by trees, power lines or other objects. Moving the sticks during takeoff may affect image acquisition. In an emergency, use the control sticks to throttle downward.</string>
    <string name="uxsdk_take_off_action">Slide to Take Off</string>

    <string name="uxsdk_land_header">Land?</string>
    <string name="uxsdk_land_message">Aircraft will land at its current location. Check landing area is clear.</string>
    <string name="uxsdk_land_action">Slide to Land</string>

    <string name="uxsdk_land_confirmation_header">Continue Landing?</string>
    <string name="uxsdk_land_confirmation_message">Aircraft has descended to %1$s.</string>
    <string name="uxsdk_land_confirmation_message_in2">Aircraft is hovering low. Ensure the landing point is safe and push the stick downward to land.</string>

    <string name="uxsdk_unsafe_to_land_header">Not suitable for landing.</string>
    <string name="uxsdk_unsafe_to_land_message">Area is not suitable for landing. Please move to a safer location.</string>
    <string name="uxsdk_unsafe_to_land_action">Slide to Force Landing</string>

    <!-- Return Home Widget -->
    <string name="uxsdk_return_to_home_header">Return home and land?</string>
    <string name="uxsdk_return_to_home_action">Slide to Return Home</string>
    <string name="uxsdk_return_home_at_current_altitude">The distance between the aircraft and Home Point is less than 65.6 ft (20 m). The aircraft will return home at its current altitude if RTH is initiated.</string>
    <string name="uxsdk_return_home_inner_desc">The aircraft is less than 20 m (65.6 ft) from the Home Point. The aircraft will land if Return to Home is initiated.</string>
    <string name="uxsdk_return_home_near_nfz">Approaching a No-Fly Zone. Current RTH route may be distorted. Monitor the aircraft\'s flight path on the map or visually to ensure safety.</string>
    <string name="uxsdk_return_home_wifi">Aircraft will head to home point and set RTH route. Use virtual joystick to adjust flight direction. Tap Cancel RTH button to stop RTH.</string>
    <string name="uxsdk_return_home_below_desc">Aircraft will adjust its nose direction to face the home point, and its altitude to %1$s. Current aircraft altitude is %2$s. Press Return to Home button directly on your remote to cancel this procedure, or take over control sticks.</string>

    <!-- RTK Widget -->
    <string name="uxsdk_rtk_panel_antenna_1">Antenna 1</string>
    <string name="uxsdk_rtk_panel_antenna_2">Antenna 2</string>
    <string name="uxsdk_rtk_panel_base_station">Base Station</string>
    <string name="uxsdk_rtk_panel_satellite_num">Satellites#</string>
    <string name="uxsdk_rtk_panel_gps_num">GPS:</string>
    <string name="uxsdk_rtk_panel_beidou_num">BeiDou:</string>
    <string name="uxsdk_rtk_panel_glonass_num">GLONASS:</string>
    <string name="uxsdk_rtk_panel_galileo_num">Galileo:</string>

    <string name="uxsdk_rtk_panel_aircraft_location">Aircraft</string>
    <string name="uxsdk_rtk_panel_base_station_location">Base Station</string>
    <string name="uxsdk_rtk_panel_lat">Latitude: </string>
    <string name="uxsdk_rtk_panel_coordinate_value">%1$.9f</string>
    <string name="uxsdk_rtk_panel_altitude_value">%1$.3f</string>
    <string name="uxsdk_rtk_panel_course_angle_value">%1$.2f</string>
    <string name="uxsdk_rtk_panel_lng">Longitude:</string>
    <string name="uxsdk_rtk_panel_height">Ellipsoidal Height:</string>
    <string name="uxsdk_rtk_panel_antenna_angle">Course Angle:</string>
    <string name="uxsdk_rtk_panel_orientation_status_text">Orientation:</string>
    <string name="uxsdk_rtk_panel_positioning_status_text">Positioning:</string>
    <string name="uxsdk_rtk_standard_deviation">Standard Deviation:</string>
    <string name="uxsdk_rtk_introduction_txt">Real-Time Kinematic positioning is based on the phase of the signal\'s carrier phase and outputs centimeter-level 3D positioning on particular coordinates. Heading is determined by the two antennas of the rover.</string>

    <string name="uxsdk_rtk_enabled_title_rtk_switch">RTK Positioning</string>
    <string name="uxsdk_rtk_enabled_desc">When RTK module malfunctions, manually disable RTK and switch back to GPS mode. (If you enable RTK after takeoff, the GPS will continually be used.)</string>
    <string name="uxsdk_rtk_enabled_motors_running">Motors are running. Stop them and try again.</string>

    <string name="uxsdk_rtk_solution_none">NONE</string>
    <string name="uxsdk_rtk_solution_single">SINGLE</string>
    <string name="uxsdk_rtk_solution_float">FLOAT</string>
    <string name="uxsdk_rtk_solution_fixed">FIX</string>
    <string name="uxsdk_rtk_solution_unknown">UNKNOWN</string>
    <string name="uxsdk_rtk_state_connect">RTK connected. RTK data in use</string>
    <string name="uxsdk_rtk_state_connect_not_healthy">RTK connected. RTK data not in use</string>
    <string name="uxsdk_rtk_state_disconnect">Not connected</string>
    <string name="uxsdk_rtk_state_not_bind">Account Not Registered</string>
    <string name="uxsdk_rtk_state_not_active">Account not activated</string>
    <string name="uxsdk_rtk_state_pause">Suspending account…</string>
    <string name="uxsdk_rtk_state_account_expire">Time verification failed</string>
    <string name="uxsdk_rtk_state_connecting">Connecting...</string>
    <string name="uxsdk_rtk_state_coordinate_fialed">Setting network RTK coordinates failed</string>
    <string name="uxsdk_rtk_state_network_err">Network unavailable</string>
    <string name="uxsdk_rtk_state_auth_failed">Verification failed</string>
    <string name="uxsdk_rtk_state_unknown">Unknown error</string>

    <string name="uxsdk_rtk_nrtk_state_inner_error">Internal error</string>
    <string name="uxsdk_rtk_nrtk_state_unknown">Unknown error</string>
    <string name="uxsdk_rtk_nrtk_account_error">Account error</string>
    <string name="uxsdk_rtk_nrtk_connecting">Connecting to server…</string>
    <string name="uxsdk_rtk_nrtk_invalid_request">Request rejected by server</string>
    <string name="uxsdk_rtk_nrtk_server_not_reachable">Connecting to server failed</string>
    <string name="uxsdk_rtk_type_nrtk">Network RTK</string>
    <string name="uxsdk_rtk_type_rtk_mobile_station">D-RTK 2 Mobile Station</string>
    <string name="uxsdk_rtk_type_rtk_base_station">Base Station</string>
    <string name="uxsdk_rtk_type_custom_rtk">Custom Network RTK</string>
    <string name="uxsdk_rtk_type_cmcc_rtk">CMCC RTK</string>
    <string name="uxsdk_rtk_type_unknown_rtk">Unknown RTK</string>
    <string name="uxsdk_rtk_status_desc">%1$s Status:</string>

    <!--RTK keep Status-->
    <string name="uxsdk_rtk_keep_status_mode">Maintain Positioning Accuracy Mode</string>
    <string name="uxsdk_rtk_keep_status_hint">In case of an RTK module communication error, the aircraft will automatically maintain the current RTK status with gradually decreasing accuracy. The aircraft will exit RTK if the connection is not re-established within 10 minutes.</string>


    <string name="uxsdk_fly_zone_unlock">Unlock</string>
    <string name="uxsdk_fly_zone_unlock_zone">Unlock %1$s</string>
    <string name="uxsdk_fly_zone_unlock_confirmation">By tapping Unlock, you agree that you have the authorization to operate in this fly zone. A record of this request will be linked to your DJI account. Tap Cancel to abort the operation.</string>
    <string name="uxsdk_fly_zone_unlock_end_time">Currently unlocked and expires at %1$s</string>
    <string name="uxsdk_fly_zone_unlock_failed_unauthorized">Failed to unlock. You are not authorized to unlock this zone.</string>
    <string name="uxsdk_fly_zone_unlock_failed">Failed to unlock. %1$s</string>
    <string name="uxsdk_fly_zone_login_failed">Failed to login. You are not authorized to perform this action.</string>
    <string name="uxsdk_fly_zone_login_failed_error">Something went wrong. Failed to login. Please try again later.</string>
    <string name="uxsdk_fly_zone_warning">Warning</string>
    <string name="uxsdk_fly_zone_enhanced_warning">Enhanced warning</string>
    <string name="uxsdk_fly_zone_authorized">Authorized</string>
    <string name="uxsdk_fly_zone_restricted">Restricted</string>
    <string name="uxsdk_fly_zone_self_unlock">Self Unlock</string>
    <string name="uxsdk_fly_zone_custom_unlock">Custom Unlock</string>
    <string name="uxsdk_self_fly_zone_login_requirement">In order to unlock fly zones you must login with your DJI account. Tap Log in to continue logging in</string>
    <string name="uxsdk_fly_zone_custom_unlock_aircraft">Custom Unlock on Aircraft</string>
    <string name="uxsdk_fly_zone_custom_unlock_enabled">Custom Unlock Enabled</string>
    <string name="uxsdk_custom_fly_zone_duplicate">%1$s is currently enabled. Do you wish you disable it and enable %2$s</string>

    <string name="uxsdk_map">map</string>

    <!--Map Widget Activity-->
    <string name="uxsdk_provider_google_maps">Google Maps</string>
    <string name="uxsdk_provider_here_maps">Here Maps</string>
    <string name="uxsdk_provider_mapbox">Mapbox</string>
    <string name="uxsdk_error_map_not_initialized">The map has not been initialized yet</string>
    <string name="uxsdk_home_direction">Home Direction</string>
    <string name="uxsdk_auto_frame">Auto Frame</string>
    <string name="uxsdk_unlock_flyzones">Unlock Fly Zones</string>
    <string name="uxsdk_show_flight_path">Show Flight Path</string>
    <string name="uxsdk_show_home_point">Show Home Point</string>
    <string name="uxsdk_show_gimbal_yaw">Show Gimbal Yaw</string>
    <string name="uxsdk_clear_flight_path">Clear Flight Path</string>
    <string name="uxsdk_replace_icon">Replace Icon</string>
    <string name="uxsdk_fly_zone">Fly Zone</string>
    <string name="uxsdk_fly_zone_legend">Fly Zone Legend</string>
    <string name="uxsdk_dji_account_indicator">DJI Account Login Indicator</string>
    <string name="uxsdk_all">All</string>
    <string name="uxsdk_auth">Authorization</string>
    <string name="uxsdk_warning">Warning</string>
    <string name="uxsdk_enhanced_warning">Enhanced Warning</string>
    <string name="uxsdk_restricted">Restricted</string>
    <string name="uxsdk_maximum_height">Maximum Height</string>
    <string name="uxsdk_self_unlocked">Self-Unlocked</string>
    <string name="uxsdk_custom_unlocked">Custom Unlocked</string>
    <string name="uxsdk_new_color">New Color</string>
    <string name="uxsdk_sync">Sync</string>
    <string name="uxsdk_map_settings">Map Settings</string>
    <string name="uxsdk_center_map">Center Map</string>
    <string name="uxsdk_map_overlay">Map Overlay</string>
    <string name="uxsdk_fly_zone_settings">Fly Zone Settings</string>
    <string name="uxsdk_flight_settings">Flight Settings</string>
    <string name="uxsdk_other_settings">Other Settings</string>
    <string name="uxsdk_icon_change_settings">Icon Change Settings</string>
    <string name="uxsdk_line_color_settings">Line Color Settings</string>
    <string name="uxsdk_marker_drag_started">Marker %1$d drag started</string>
    <string name="uxsdk_marker_drag_ended">Marker %1$d drag ended</string>
    <string name="uxsdk_marker_clicked">Marker %1$d clicked</string>
    <string-array name="uxsdk_iconArray">
        <item>Aircraft</item>
        <item>Home</item>
        <item>Gimbal Yaw</item>
        <item>Locked Zone</item>
        <item>Unlocked Zone</item>
    </string-array>
    <string-array name="uxsdk_mapTypeArray">
        <item>Standard</item>
        <item>Satellite</item>
        <item>Hybrid</item>
    </string-array>
    <string-array name="uxsdk_lineTypeArray">
        <item>Home Direction</item>
        <item>Flight Path</item>
        <item>Fly Zone Border</item>
    </string-array>

    <string name="uxsdk_designed_aspect_ratio">Designed Aspect Ratio: </string>
    <string name="uxsdk_current_widget_size">Current Widget Size: </string>
    <string name="uxsdk_pinch_to_resize">* Pinch to resize widget</string>

    <!-- Widget List Strings -->
    <string name="uxsdk_altitude_widget_title">Altitude Widget</string>
    <string name="uxsdk_auto_exposure_lock_widget_title">Auto Exposure Lock Widget</string>
    <string name="uxsdk_battery_widget_title">Battery Widget</string>
    <string name="uxsdk_camera_capture_widget_title">Camera Capture Widget</string>
    <string name="uxsdk_camera_config_aperture_widget_title">Camera Config Aperture Widget</string>
    <string name="uxsdk_camera_config_ev_widget_title">Camera Config EV Widget</string>
    <string name="uxsdk_camera_config_iso_widget_title">Camera Config ISO Widget</string>
    <string name="uxsdk_camera_config_shutter_widget_title">Camera Config Shutter Widget</string>
    <string name="uxsdk_camera_config_ssd_widget_title">Camera Config SSD Widget</string>
    <string name="uxsdk_camera_config_storage_widget_title">Camera Config Storage Widget</string>
    <string name="uxsdk_camera_config_wb_widget_title">Camera Config WB Widget</string>
    <string name="uxsdk_camera_controls_widget_title">Camera Controls Widget</string>
    <string name="uxsdk_camera_settings_menu_indicator_widget_title">Camera Settings Menu Indicator Widget</string>
    <string name="uxsdk_compass_widget_title">Compass Widget</string>
    <string name="uxsdk_telemetry_widget_title">Telemetry Panel</string>
    <string name="uxsdk_distance_home_widget_title">Distance Home Widget</string>
    <string name="uxsdk_distance_rc_widget_title">Distance RC Widget</string>
    <string name="uxsdk_exposure_settings_indicator_widget_title">Exposure Settings Indicator Widget</string>
    <string name="uxsdk_focus_exposure_switch_widget_title">Focus Exposure Switch Widget</string>
    <string name="uxsdk_focus_mode_widget_title">Focus Mode Widget</string>
    <string name="uxsdk_fpv_widget_title">FPV Widget</string>
    <string name="uxsdk_fpv_interaction_widget_title">FPV Interaction Widget</string>
    <string name="uxsdk_horizontal_velocity_widget_title">Horizontal Velocity Widget</string>
    <string name="uxsdk_record_video_widget_title">Record Video Widget</string>
    <string name="uxsdk_remote_control_signal_widget_title">Remote Control Signal Widget</string>
    <string name="uxsdk_photo_video_switch_widget_title">Photo Video Switch Widget</string>
    <string name="uxsdk_shoot_photo_widget_title">Shoot Photo Widget</string>
    <string name="uxsdk_simulator_indicator_control_widgets_title">Simulator Indicator and Control Widgets</string>
    <string name="uxsdk_system_status_panel_title">System Status Panel</string>
    <string name="uxsdk_system_status_widget_title">System Status Widget</string>
    <string name="uxsdk_top_bar_panel_title">Top Bar Panel</string>
    <string name="uxsdk_user_account_login_widget_title">User Account Login Widget</string>
    <string name="uxsdk_vertical_velocity_widget_title">Vertical Velocity Widget</string>
    <string name="uxsdk_vision_widget_title">Vision Widget</string>
    <string name="uxsdk_vps_widget_title">VPS Widget</string>
    <string name="uxsdk_flight_mode_widget_title">Flight Mode Widget</string>
    <string name="uxsdk_video_signal_widget_title">Video Signal Widget</string>
    <string name="uxsdk_gps_signal_widget_title">GPS Signal Widget</string>
    <string name="uxsdk_air_sense_widget_title">Air Sense Widget</string>
    <string name="uxsdk_connection_widget_title">Connection Widget</string>
    <string name="uxsdk_rtk_widget_title">RTK Widget</string>
    <string name="uxsdk_rtk_enabled_widget_title">RTK Enabled Widget</string>
    <string name="uxsdk_rtk_keep_status_widget_title">RTK Keep Status Widget</string>
    <string name="uxsdk_rtk_satellite_status_widget_title">RTK Satellite Status Widget</string>
    <string name="uxsdk_rtk_type_switch_widget_title">RTK Type Switch Widget</string>
    <string name="uxsdk_remaining_flight_time_widget_title">Remaining Flight Time Widget</string>
    <string name="uxsdk_exposure_setting_panel_title">Exposure Setting Panel</string>
    <string name="uxsdk_rtk_rtk_station_connect_widget_title">RTK Station Connect Widget</string>
    <string name="uxsdk_exposure_mode_setting_widget_title">Exposure Mode Setting Widget</string>
    <string name="uxsdk_iso_and_ei_setting_widget_title">ISO And EI Setting Widget</string>
    <string name="uxsdk_horizontal_situation_indicator_widget_title">Horizontal Situation Indicator Widget</string>
    <string name="uxsdk_speed_display_widget_title">Speed Display Widget</string>
    <string name="uxsdk_attitude_display_widget_title">Attitude Display Widget</string>
    <string name="uxsdk_primary_flight_display_widget_title">Primary Flight Display Widget</string>
    <string name="uxsdk_exposure_meter_widget_title">Exposure Metering Widget</string>
    <string name="uxsdk_focal_zoom_widget_title">Focal Zoom Widget</string>
    <string name="uxsdk_camera_ndvi_panel_widget_title">Camera NDVI Panel Widget</string>
    <string name="uxsdk_camera_visible_panel_widget_title">Camera Visible Panel Widget</string>
    <string name="uxsdk_device_health_and_status_widget_title">Device Health and Status Widget</string>
    <string name="uxsdk_gimbal_fine_tune_widget_title">Gimbal Fine Tune Widget</string>
    <string name="uxsdk_gimbal_setting_widget_title">Gimbal Setting Widget</string>
    <string name="uxsdk_flyc_gohome_mode_widget_title">FC Return Home Mode Widget</string>
    <string name="uxsdk_flyc_lost_action_widget_title">FC Lost Action Widget</string>
    <string name="uxsdk_flyc_home_set_widget_title">FC Home Point  Widget</string>
    <string name="uxsdk_flyc_home_distance_height_limit_widget_title">FC Distance Height Limit Widget</string>
    <string name="uxsdk_flyc_imu_state_widget_title">FC IMU Status Widget</string>
    <string name="uxsdk_flyc_compass_state_widget_title">FC Compass Status Widget</string>
    <string name="uxsdk_flyc_fpa_widget_title"> FC Flight Mode Widget</string>

    <string name="uxsdk_battery_info_widget_title">Battery Info Widget</string>
    <string name="uxsdk_battery_setting_widget_title">Battery Setting Widget</string>
    <string name="uxsdk_battery_alert_widget_title">Battery Alert Widget</string>
    <string name="uxsdk_frequency_setting_widget_title">HD Frequency Mode Widget</string>
    <string name="uxsdk_sdr_info_widget_title">HD SDR Info Widget</string>
    <string name="uxsdk_sdr_band_width_select_title">HD SDR Band Width Select Widget</string>
    <string name="uxsdk_sdr_band_width_title">HD SDR Band Width Widget</string>
    <string name="uxsdk_sdr_video_rate_title">HD SDR Video Rate Widget</string>
    <string name="uxsdk_sdr_channel_mode_title">HD SDR Channel Mode Widget</string>
    <string name="uxsdk_sdr_freq_title">HD SDR Frequency Widget</string>
    <string name="uxsdk_hdmi_setting_title">HD HDMI Setting Widget</string>
    <string name="uxsdk_common_about_widget_title">Common About Widget</string>
    <string name="uxsdk_common_device_name_widget_title">Common Device Name Widget</string>
    <string name="uxsdk_common_led_widget_title">Common Led Widget</string>
    <string name="uxsdk_gimbal_caling">Gimbal calibration at %1$d%%. Please don\'t move the aircraft.</string>
    <string name="uxsdk_gimbal_gimbal_reset_params_success">Reset successful</string>
    <string name="uxsdk_gimbal_gimbal_reset_params_fail">Reset failed</string>
    <string name="uxsdk_gimbal_cali_success">Calibrated gimbal successfully.</string>
    <string name="uxsdk_gimbal_cali_fail">Gimbal Calibration Failed</string>
    <string name="uxsdk_avoidance_short_widget_title">Perception Avoidance Type Widget</string>
    <string name="uxsdk_avoidance_vision_position_widget_title">Perception Vision Widget</string>
    <string name="uxsdk_avoidance_precision_landing_widget_title">Perception Precision Landing Widget</string>
    <string name="uxsdk_remote_contorller_check_frequency_widget_title">RC Pairing Widget</string>
    <string name="uxsdk_remote_contorller_calibration_widget_title">RC Calibration Widget</string>

    <!-- Simulator Widget -->
    <string name="uxsdk_simulator_zero_string">0</string>
    <string name="uxsdk_simulator_null_string">N/A</string>
    <string name="uxsdk_simulator_widget_title">Simulator Status</string>
    <string name="uxsdk_simulator_widget_lat">Latitude</string>
    <string name="uxsdk_simulator_widget_lng">Longitude</string>
    <string name="uxsdk_simulator_widget_world_x">World-X</string>
    <string name="uxsdk_simulator_widget_world_y">World-Y</string>
    <string name="uxsdk_simulator_widget_world_z">World-Z</string>
    <string name="uxsdk_simulator_widget_pitch">Pitch</string>
    <string name="uxsdk_simulator_widget_yaw">Yaw</string>
    <string name="uxsdk_simulator_widget_roll">Roll</string>
    <string name="uxsdk_simulator_widget_flying_state">Flying State</string>
    <string name="uxsdk_simulator_widget_motor_started">Motor Started</string>
    <string name="uxsdk_simulator_widget_satellite">Satellite Count</string>
    <string name="uxsdk_simulator_widget_frequency">Frequency</string>
    <string name="uxsdk_simulator_position_title">Position</string>
    <string name="uxsdk_simulator_attitude_title">Attitude</string>
    <string name="uxsdk_simulator_aircraft_status_title">Aircraft</string>
    <string name="uxsdk_simulator_aircraft_wind_title">Wind Simulation</string>
    <string name="uxsdk_simulator_input_val_error">Enter the values to simulate</string>
    <string name="uxsdk_simulator_save_preset">Save Preset</string>
    <string name="uxsdk_simulator_preset_name_hint">Enter preset name</string>
    <string name="uxsdk_simulator_preset_name_empty_error">Preset name cannot be empty</string>
    <string name="uxsdk_simulator_preset_error">Error loading preset.</string>
    <string name="uxsdk_simulator_save_preset_list">Preset List</string>
    <string name="uxsdk_simulator_save_preset_delete">Are you sure you want to delete %1$s?</string>
    <string name="uxsdk_simulator_save_preset_list_empty">No saved presets</string>
    <string name="uxsdk_simulator_preset">Preset</string>
    <string name="uxsdk_simulator_wind_speed_x">Wind Speed X</string>
    <string name="uxsdk_simulator_wind_speed_y">Wind Speed Y</string>
    <string name="uxsdk_simulator_wind_speed_z">Wind Speed Z</string>
    <string name="uxsdk_simulator_widget_lat_hint">Lat range -90 to 90</string>
    <string name="uxsdk_simulator_widget_lng_hint">Lng range -180 to 180</string>

    <!-- white balance -->
    <string name="uxsdk_camera_wb_auto">Auto</string>
    <string name="uxsdk_camera_wb_outdoor">Sunny</string>
    <string name="uxsdk_camera_wb_indoor">Cloudy</string>
    <string name="uxsdk_camera_wb_water">Water</string>
    <string name="uxsdk_camera_wb_tungsten">Incandescent</string>
    <string name="uxsdk_camera_wb_neon">Neon</string>
    <string name="uxsdk_camera_wb_custom">Custom</string>
    <string name="uxsdk_camera_wb_neutral">Neutral</string>

    <!-- video resolution -->
    <string name="uxsdk_camera_video_resolution_640_480p">640x480</string>
    <string name="uxsdk_camera_video_resolution_640_512p">640x512</string>
    <string name="uxsdk_camera_video_resolution_1280_720p">1280x720</string>
    <string name="uxsdk_camera_video_resolution_1920_1080p">1920x1080</string>
    <string name="uxsdk_camera_video_resolution_2704_1520p">2704x1520</string>
    <string name="uxsdk_camera_video_resolution_2720_1530p">2720x1530</string>
    <string name="uxsdk_camera_video_resolution_3840x1572p">3840x1572</string>
    <string name="uxsdk_camera_video_resolution_3840_2160p">3840x2160</string>
    <string name="uxsdk_camera_video_resolution_4096_2160p">4096x2160</string>
    <string name="uxsdk_camera_video_resolution_4608x2160p">4608x2160</string>
    <string name="uxsdk_camera_video_resolution_4608x2592p">4608x2592</string>
    <string name="uxsdk_camera_video_resolution_5280x2160p">5280x2160</string>
    <string name="uxsdk_camera_video_resolution_MaxResolution">Max</string>
    <string name="uxsdk_camera_video_resolution_None">Not Recorded</string>
    <string name="uxsdk_camera_video_resolution_5760x3240p">5760x3240</string>
    <string name="uxsdk_camera_video_resolution_6016x3200p">6016x3200</string>
    <string name="uxsdk_camera_video_resolution_2048x1080p">2048x1080</string>
    <string name="uxsdk_camera_video_resolution_5280x2972p">5280x2972</string>
    <string name="uxsdk_camera_video_resolution_336x256p">336x256</string>
    <string name="uxsdk_camera_video_resolution_3712x2088p">3712x2088</string>
    <string name="uxsdk_camera_video_resolution_3944x2088p">3944x2088</string>
    <string name="uxsdk_camera_video_resolution_2688x1512p">2688x1512</string>
    <string name="uxsdk_camera_video_resolution_hq">HQ</string>
    <string name="uxsdk_camera_video_resolution_full_fov">Full FOV</string>
    <string name="uxsdk_slow_tag">SLOW</string>

    <!-- video frame rate value-->
    <string name="uxsdk_camera_video_frame_rate_value_24">23.976</string>
    <string name="uxsdk_camera_video_frame_rate_value_true_24">24.000</string>
    <string name="uxsdk_camera_video_frame_rate_value_25">25.000</string>
    <string name="uxsdk_camera_video_frame_rate_value_30">29.970</string>
    <string name="uxsdk_camera_video_frame_rate_value_true_30">30.000</string>
    <string name="uxsdk_camera_video_frame_rate_value_48">47.950</string>
    <string name="uxsdk_camera_video_frame_rate_value_true_48">48.000</string>
    <string name="uxsdk_camera_video_frame_rate_value_50">50.000</string>
    <string name="uxsdk_camera_video_frame_rate_value_60">59.940</string>
    <string name="uxsdk_camera_video_frame_rate_value_true_60">60.000</string>
    <string name="uxsdk_camera_video_frame_rate_value_96">96.000</string>
    <string name="uxsdk_camera_video_frame_rate_value_100">100.000</string>
    <string name="uxsdk_camera_video_frame_rate_value_120">119.880</string>
    <string name="uxsdk_camera_video_frame_rate_value_240">240.000</string>

    <!-- SSD Color -->
    <string name="uxsdk_camera_ssd_color_standard">Standard</string>
    <string name="uxsdk_camera_ssd_color_dlog">DLog</string>
    <string name="uxsdk_camera_ssd_color_rec709">Rec.709</string>
    <string name="uxsdk_camera_ssd_color_cine_like">CineLike</string>
    <string name="uxsdk_camera_ssd_color_raw_color">RAW</string>
    <string name="uxsdk_camera_ssd_color_unknown">Unknown</string>

    <!-- Storage -->
    <string name="uxsdk_storage_title_time">TIME</string>
    <string name="uxsdk_storage_title_capacity">CAPACITY</string>
    <string name="uxsdk_storage_title_status">STATUS</string>
    <string name="uxsdk_storage_value_full">Full</string>
    <string name="uxsdk_storage_remaining_space">%1$d MB</string>

    <!-- SD Status -->
    <string name="uxsdk_sd_card_usb_connected">USB Connected</string>
    <string name="uxsdk_sd_card_missing">No SD Card</string>
    <string name="uxsdk_sd_card_full">SD Card Full</string>
    <string name="uxsdk_sd_card_slow">Slow SD Card</string>
    <string name="uxsdk_sd_card_write_protect">SD Card Write Protected</string>
    <string name="uxsdk_sd_card_not_formatted">SD Card Not Formatted</string>
    <string name="uxsdk_sd_card_formatting">SD Card is formatting&#8230;</string>
    <string name="uxsdk_sd_card_invalid">SD Card Invalid</string>
    <string name="uxsdk_sd_card_busy">SD Card Busy</string>
    <string name="uxsdk_sd_card_unknown_error">SD Card Unknown Error</string>
    <string name="uxsdk_sd_card_initial">SD Card Initializing</string>
    <string name="uxsdk_sd_card_recover_file">Repairing Video Files</string>
    <string name="uxsdk_sd_card_write_slow">Slow Write Speed</string>
    <string name="uxsdk_sd_card_needs_formatting">SD Card Formatting is Recommended</string>

    <!-- Internal Status -->
    <string name="uxsdk_internal_storage_missing">No Internal Storage</string>
    <string name="uxsdk_internal_storage_full">Internal Storage Full</string>
    <string name="uxsdk_internal_storage_slow">Slow Internal Storage</string>
    <string name="uxsdk_internal_storage_write_protect">Internal Storage Write Protected</string>
    <string name="uxsdk_internal_storage_not_formatted">Internal Storage Not Formatted</string>
    <string name="uxsdk_internal_storage_formatting">Internal Storage is Formatting&#8230;</string>
    <string name="uxsdk_internal_storage_invalid">Internal Storage Invalid</string>
    <string name="uxsdk_internal_storage_busy">Internal Storage Busy</string>
    <string name="uxsdk_internal_storage_unknown_error">Internal Storage Unknown Error</string>
    <string name="uxsdk_internal_storage_initial">Internal Storage Initializing</string>

    <!-- SSD Status -->
    <string name="uxsdk_ssd_status_error_nossd">No SSD Available</string>
    <string name="uxsdk_camera_ssd_saving">Saving to SSD</string>
    <string name="uxsdk_ssd_status_formatting">Formatting…</string>
    <string name="uxsdk_ssd_status_init">SSD initializing…</string>
    <string name="uxsdk_ssd_status_recognize_failed">SSD recognition failed.</string>
    <string name="uxsdk_ssd_status_verify_failed">SSD Verification Failed</string>
    <string name="uxsdk_ssd_status_full">SSD Full</string>
    <string name="uxsdk_ssd_status_poor_connection">Connection Error</string>
    <string name="uxsdk_ssd_status_switching_mode">Switching mode…</string>
    <string name="uxsdk_ssd_status_need_format">Formatting is required.</string>

    <!-- SSD License -->
    <string name="uxsdk_camera_ssd_video_license_cdng">CinemaDNG</string>
    <string name="uxsdk_camera_ssd_video_license_422hq">ProRes 422HQ</string>
    <string name="uxsdk_camera_ssd_video_license_4444xq">ProRes 4444XQ</string>

    <!-- SD Color -->
    <string name="uxsdk_camera_filter_none">None</string>
    <string name="uxsdk_camera_filter_art">Art</string>
    <string name="uxsdk_camera_filter_reminiscence">Reminiscence</string>
    <string name="uxsdk_camera_filter_inverse">Inverse</string>
    <string name="uxsdk_camera_filter_blackandwhite">Black &amp; White</string>
    <string name="uxsdk_camera_filter_bright">Bright</string>
    <string name="uxsdk_camera_filter_dcinelike">D-CineLike</string>
    <string name="uxsdk_camera_filter_Portrait">Portrait</string>
    <string name="uxsdk_camera_filter_M31">M31</string>
    <string name="uxsdk_camera_filter_delta">Delta</string>
    <string name="uxsdk_camera_filter_kdx">Kdx</string>
    <string name="uxsdk_camera_filter_dk79">DK79</string>
    <string name="uxsdk_camera_filter_prismo">Prismo</string>
    <string name="uxsdk_camera_filter_jugo">Jugo</string>
    <string name="uxsdk_camera_filter_vision4">Vision4</string>
    <string name="uxsdk_camera_filter_vision6">Vision6</string>
    <string name="uxsdk_camera_filter_solarize">Solarize</string>
    <string name="uxsdk_camera_filter_posterize">Posterize</string>
    <string name="uxsdk_camera_filter_whiteboard">Whiteboard</string>
    <string name="uxsdk_camera_filter_blackboard">Blackboard</string>>
    <string name="uxsdk_camera_filter_aqua">Aqua</string>
    <string name="uxsdk_camera_filter_truecolor">TrueColor</string>
    <string name="uxsdk_camera_filter_dlog">D-Log</string>
    <string name="uxsdk_camera_filter_hlg">HLG</string>
    <string name="uxsdk_camera_filter_truecolor_ext">TrueColorExt</string>
    <string name="uxsdk_camera_filter_film_a">Film A</string>
    <string name="uxsdk_camera_filter_film_b">Film B</string>
    <string name="uxsdk_camera_filter_film_c">Film C</string>
    <string name="uxsdk_camera_filter_film_d">Film D</string>
    <string name="uxsdk_camera_filter_film_e">Film E</string>
    <string name="uxsdk_camera_filter_film_f">Film F</string>
    <string name="uxsdk_camera_filter_film_g">Film G</string>
    <string name="uxsdk_camera_filter_film_h">Film H</string>
    <string name="uxsdk_camera_filter_film_i">Film I</string>
    <string name="uxsdk_camera_filter_rec_709">Rec.709</string>

    <!-- Camera Config Widgets -->
    <string name="uxsdk_aperture_title">F</string>
    <string name="uxsdk_aperture_integer">%1$d</string>
    <string name="uxsdk_aperture_integer_with_decimal">%1$d.%2$d</string>
    <string name="uxsdk_shutter_title">SHUTTER</string>
    <string name="uxsdk_ev_title">EV</string>
    <string name="uxsdk_exposure_auto_iso_title">AUTO ISO</string>
    <string name="uxsdk_exposure_locked_iso_title">LOCKED ISO</string>
    <string name="uxsdk_exposure_iso_title">ISO</string>
    <string name="uxsdk_white_balance_title">WB %1$s</string>
    <string name="uxsdk_white_balance_temp">%1$dK</string>

    <!--RTK Coordinate widget-->
    <string name="uxsdk_rtk_coordinate_title">RTK Coordinates</string>
    <string name="uxsdk_rtk_type_switch_title">Select RTK Service Type</string>
    <string name="uxsdk_rtk_base_gps_input_desc">Make sure D-RTK 2 Mobile Station is in open area and do not adjust position after setup. Unable to connect to mobile station after starting aircraft propellers</string>
    <string name="uxsdk_rtk_coordinate_c2000">C2000</string>
    <string name="uxsdk_rtk_coordinate_wgs84">WGS84</string>
    <string name="uxsdk_rtk_setting_menu_setting_success">Set successfully</string>
    <string name="uxsdk_rtk_setting_menu_setting_fail_no_network">Setting failed. Check network connection</string>
    <string name="uxsdk_rtk_setting_menu_setting_fail_settings_invalid">Setting failed. Invalid parameters误</string>
    <string name="uxsdk_rtk_setting_menu_setting_fail_incorrect_reference_station_source">RTK service error. Restart DJI Pilot or aircraft and try again</string>
    <string name="uxsdk_rtk_setting_menu_type_rtk_none">NONE</string>
    <string name="uxsdk_rtk_setting_menu_type_nrtk">Network RTK</string>
    <string name="uxsdk_rtk_setting_menu_type_rtk_station">D-RTK2 Mobile Station</string>
    <string name="uxsdk_rtk_setting_menu_type_custom_rtk">Custom Network RTK</string>
    <string name="uxsdk_rtk_setting_menu_type_cmcc_rtk">CMCC RTK</string>
    <string name="uxsdk_rtk_setting_menu_type_rtk_station_selected">Select D-RTK 2 Mobile Station</string>
    <string name="uxsdk_rtk_setting_menu_type_qx_rtk">Qianxun Network RTK</string>
    <string name="uxsdk_rtk_channel_b_not_support_net_rtk">Network RTK not supported by Controller B</string>
    <string name="uxsdk_rtk_channel_b_not_support_net_custom_rtk">Custom network RTK not supported by Controller B</string>
    <string name="uxsdk_rtk_setting_menu_esc_beeping_tip">Motors are running. Stop them and try again.</string>
    <string name="uxsdk_rtk_setting_menu_switch_des_info">Restart aircraft after switching RTK services</string>
    <string name="uxsdk_rtk_setting_menu_base_gps_input_desc">Make sure D-RTK 2 Mobile Station is in open area and do not adjust position after setup. Unable to connect to mobile station after starting aircraft propellers</string>
    <string name="uxsdk_rtk_setting_menu_station_net_rtk_desc">Receiving RTK signal. Ensure network connection is stable</string>
    <string name="uxsdk_rtk_setting_menu_settting">Save</string>
    <string name="uxsdk_rtk_setting_menu_rtk_setting_success">Set successfully</string>
    <string name="uxsdk_rtk_setting_menu_setting_fail">Setting failed</string>
    <string name="uxsdk_rtk_setting_menu_rtk_setting_fail_settings_invalid">Setting failed. Invalid parameters</string>
    <string name="uxsdk_rtk_setting_menu_customer_rtk_save_failed_tips">Setting failed,possible reasons:\n1. Input parameter incorrect. Check input parameter\n2. Account expired. Check if account is valid\n3. Not network connection. Check network connection status\n4. Server error. Contact RTK supplier to check server status</string>


    <!--Lens Control Widget-->
    <string name="uxsdk_lens_type_wide">WIDE</string>
    <string name="uxsdk_lens_type_zoom">ZOOM</string>
    <string name="uxsdk_lens_type_ir">IR</string>
    <string name="uxsdk_lens_type_ndvi">NDVI</string>
    <string name="uxsdk_lens_type_rgb">RGB</string>
    <string name="uxsdk_lens_type_point_cloud">PointCloud</string>

    <!--RTK Connect Widget-->
    <string name="uxsdk_rtk_problem_detection">Check the following</string>
    <string name="uxsdk_rtk_signal_search_again">Retry</string>
    <string name="uxsdk_rtk_base_station_not_found">No D-RTK 2 Mobile Stations found</string>
    <string name="uxsdk_rtk_base_station_not_found_reason">1. Check whether the D-RTK 2 Mobile Station is powered on\n2. Ensure the D-RTK 2 Mobile Station is placed in an open area free of obstructions\n3. Check whether the D-RTK 2 Mobile Station is set in Broadcast mode</string>
    <string name="uxsdk_rtk_connect_description">View D-RTK 2 connection guide</string>
    <string name="uxsdk_rtk_base_station_connect_fail">Connection failed</string>
    <string name="uxsdk_rtk_base_station_search_false_and_try_again">Request failed. Check and try again</string>

    <!--rtk guidance-->
    <string name="uxsdk_rtk_guidance_reset_title">Reset Password</string>
    <string name="uxsdk_rtk_guidance_step1_title">Power On</string>
    <string name="uxsdk_rtk_guidance_step2_title">Switch Modes</string>
    <string name="uxsdk_rtk_guidance_step3_title">Connect to DJI Pilot</string>
    <string name="uxsdk_rtk_guidance_reset_content">Press and hold the linking button for 3 seconds while pressing the M button simultaneously. The indicator will blink red several times to indicate that the password has been reset to 123456</string>
    <string name="uxsdk_rtk_guidance_step1_content">Press and hold the power button for 3 seconds. The indicator will display solid green to indicate initialization is complete</string>
    <string name="uxsdk_rtk_guidance_step2_content">Press and hold the operating mode button for two seconds until the indicator blinks yellow. Then, press the same button to switch mobile station modes.</string>
    <string name="uxsdk_rtk_guidance_step3_content">Go to RTK Settings and select the current D-RTK 2 mobile station. Linking is now complete</string>
    <string name="uxsdk_rtk_guidance_step2_image_desc">The device automatically restarts after switching to and from Broadcast mode. Restarting takes around 10s</string>
    <string name="uxsdk_rtk_guidance_btn_previous">Back</string>
    <string name="uxsdk_rtk_guidance_btn_next">Next</string>
    <string name="uxsdk_rtk_guidance_btn_finish">Complete</string>
    <string name="uxsdk_rtk_guidance_replay">Replay</string>
    <string name="uxsdk_rtk_guidance_reset_tip">Reset Success</string>
    <string name="uxsdk_rtk_guidance_step2_tip">Tap to switch modes</string>
    <string name="uxsdk_rtk_guidance_five_mode">Broadcast mode is indicated by the indicator blinking five times in a row</string>

    <!--GpsSignalWidget-->
    <string name="uxsdk_fpv_top_bar_rtk_title">RTK Positioning</string>
    <string name="uxsdk_fpv_top_bar_rtk_state_title">RTK Status</string>
    <string name="uxsdk_setting_menu_rtk_state_disconnect">Not Connected</string>
    <string name="uxsdk_fpv_top_bar_gps_title">GNSS Positioning</string>
    <string name="uxsdk_fpv_top_bar_gps_signal_title">Signal</string>
    <string name="uxsdk_fpv_top_bar_gps_signal_state_strong">Strong</string>
    <string name="uxsdk_fpv_top_bar_gps_signal_state_normal">Normal</string>
    <string name="uxsdk_fpv_top_bar_gps_signal_state_weak">Weak</string>
    <string name="uxsdk_fpv_top_bar_rtk_state_fixed">Converged</string>
    <string name="uxsdk_fpv_top_bar_rtk_state_not_fixed">Not Converged</string>
    <string name="uxsdk_fpv_top_bar_satellite_count">Satellites</string>
    <string name="uxsdk_checklist_manual_rtk_not_open">Disabled</string>
    <string name="uxsdk_checklist_rtk_status_converging">Converging...</string>
    <string name="uxsdk_checklist_rtk_status_connected">Connected</string>
    <string name="uxsdk_abnormal">Abnormal</string>
    <string name="uxsdk_normal">Normal</string>

    <!-- MasterSlaver-->
    <string name="uxsdk_rc_master_connecting">Connect</string>
    <string name="uxsdk_rc_master_connected">Connected</string>
    <string name="uxsdk_rc_master_id">Master ID: %1$s</string>
    <string name="uxsdk_rc_slaver_id">Slave ID: %1$s</string>
    <string name="uxsdk_rc_conn_status">Connection Status: %1$s</string>
    <string name="uxsdk_rc_fre_point">Frequency: %1$s</string>
    <string name="uxsdk_rc_riss_num">RISS: %1$s</string>
    <string name="uxsdk_rc_send_speed">Transmitting Rate: %1$s</string>
    <string name="uxsdk_rc_recv_speed">Receiving Rate: %1$s</string>
    <string name="uxsdk_rc_auth_code_tips">Authorization Code:</string>
    <string name="uxsdk_rc_auth_code">Authorization Code: %1$s</string>
    <string name="uxsdk_rc_master_slaver_title">Master and Slave</string>
    <string name="uxsdk_rc_master_slaver_v3_title">Master and Assistant</string>
    <string name="uxsdk_rc_master_slaver_v4_title">Remote Controller Channel(s)</string>
    <string name="uxsdk_rc_setting_mode">Set Remote Controller Status</string>
    <string name="uxsdk_rc_search_master">Search Master</string>
    <string name="uxsdk_rc_master_tip">Master Remote Controller</string>
    <string name="uxsdk_rc_scan_tip">Scan</string>
    <string name="uxsdk_rc_detail_tile">Master and Slave Information</string>
    <string name="uxsdk_rc_input_auth_code">Please enter master (%1$s) authorization code.</string>
    <string name="uxsdk_rc_input_auth_code_empty">Authorization code not entered, please enter again.</string>
    <string name="uxsdk_rc_connected_to_master_success">Connection successful.</string>
    <string name="uxsdk_rc_connected_to_master_failure">Failed to connect.</string>
    <string name="uxsdk_rc_connected_to_master_timeout">Connection error, please check that the authorization code is correct.</string>
    <string name="uxsdk_rc_search_master_title">Search Master</string>
    <string name="uxsdk_rc_setting_confirm_title">Settings</string>
    <string name="uxsdk_rc_setting_confirm_content">Change remote controller function?</string>
    <string name="uxsdk_dialog_message_rc_cannot_setting_motorup">Aircraft in flight. Unable to switch remote controller status</string>
    <string name="uxsdk_rc_setting_success">Set successfully.</string>
    <string name="uxsdk_rc_setting_failure">Failed to set, please try again.</string>
    <string name="uxsdk_rc_auth_code_length_warning">Input 6 digits</string>
    <string name="uxsdk_rc_get_master_slaver_info_error">Master and slave information error.</string>
    <string name="uxsdk_rc_request_gibal_control">Request Gimbal Control</string>
    <string name="uxsdk_rc_release_gibal_control">Release Gimbal Control</string>
    <string name="uxsdk_rc_request_timeout">Response Timeout</string>
    <string name="uxsdk_rc_has_permission">Current controller already has gimbal control.</string>
    <string name="uxsdk_rc_request_success">Requested gimbal control successfully.</string>
    <string name="uxsdk_rc_request_failure">Failed to request gimbal control.</string>
    <string name="uxsdk_rc_release_success">Released gimbal control successfully.</string>
    <string name="uxsdk_rc_release_failure">Failed to release gimbal control.</string>
    <string name="uxsdk_rc_host_refuse">Master Refused</string>
    <string name="uxsdk_rc_slaver_request_gimbal_control_title">Slave Requests Gimbal Control</string>
    <string name="uxsdk_rc_slaver_request_gimbal_control_content">Slave Name：%1$s</string>
    <string name="uxsdk_rc_master_name">Remote Controller Name</string>
    <string name="uxsdk_rc_master_password">Password</string>
    <string name="uxsdk_rc_pass_word_length_warning">Password must be 4 digits in length.</string>
    <string name="uxsdk_rc_request_refuse">Master Refused</string>
    <string name="uxsdk_rc_input_psw_code">Please enter master (%1$s) password.</string>
    <string name="uxsdk_rc_has_gimbal_control">Current controller already has gimbal control.</string>
    <string name="uxsdk_rc_master_name_length">Remote controller name must be less than 6 characters in length.</string>
    <string name="uxsdk_rc_master_slaver_no_gimbal_control">Please turn on gimbal control in remote controller settings.</string>
    <string name="uxsdk_rc_master_slaver_no_flyc_control">Aircraft flight control locked companion controller. Current RC does not have permissions for modifying flight controller settings</string>
    <string name="uxsdk_rc_master_slaver_no_rtk_control">Aircraft flight control locked by companion controller. Current RC does not have permissions for modifying RTK settings</string>
    <string name="uxsdk_rc_master_slaver_no_battery_control">Aircraft flight control locked by companion controller. Current RC does not have permissions for modifying low battery settings</string>
    <string name="uxsdk_rc_master_slaver_no_perception_control">Aircraft flight control locked by companion controller. Current RC does not have permissions for modifying obstacle avoidance settings.</string>
    <string name="uxsdk_rc_master_slaver_no_control_permission">Aircraft flight control locked by companion controller. Unable to modify settings</string>

    <!--Setting menu title-->
    <string name="uxsdk_setting_menu_title_flyc">Flight Controller Settings</string>
    <string name="uxsdk_setting_menu_title_perception">Obstacle Sensing Settings</string>
    <string name="uxsdk_setting_menu_title_gimbal">Gimbal Settings</string>
    <string name="uxsdk_setting_menu_title_rtk">RTK</string>
    <string name="uxsdk_setting_menu_title_common">Common Settings</string>
    <string name="uxsdk_setting_menu_title_rc">Remote Controller Settings</string>
    <string name="uxsdk_setting_menu_title_hd">Image Transmission Settings</string>
    <string name="uxsdk_setting_menu_title_battery">Aircraft Battery</string>
    <string name="uxsdk_payload_setting_title">Payload Settings</string>



    <string name="uxsdk_dialog_loading">Loading...</string>

    <!--MapView-->
    <string name="uxsdk_map_provider_init_failed">Map Loading failed , depend Map lib not found!</string>

    <!--NVDI 系列 Widget-->
    <string name="uxsdk_stream_switcher_sbs">SBS</string>
    <string name="uxsdk_stream_ndvi_vegetation_index">VI</string>
    <string name="uxsdk_stream_ms_lens">Multi Spectral Camera</string>
    <string name="uxsdk_switch_stream_unsupported">Unable to switch VI or multi spectral lens in current shooting mode</string>

    <!--FpvWarningMessageWidget-->
    <string name="uxsdk_fpv_message_box_empty_content_v2">Normal</string>
    <string name="uxsdk_fpv_tip_remote_disconnect">Aircraft not connected</string>

    <string name="uxsdk_setting_menu_flyc_set_altitude_rth">Preset</string>
    <string name="uxsdk_setting_menu_flyc_smart_rth">Optimal</string>

    <string name="uxsdk_setting_ui_flyc_fs_gohome">Return To Home</string>
    <string name="uxsdk_setting_ui_flyc_fs_landing">Land</string>
    <string name="uxsdk_setting_ui_flyc_fs_hover">Hover</string>

    <!--HD-->
    <string name="uxsdk_setting_menu_title_hd_hdmi_system_setting_hdmi_decoder_summary">Video Output Type</string>

    <string name="uxsdk_setting_ui_flyc_cali_begin">Starting Calibraition</string>
    <string name="uxsdk_setting_ui_flyc_cali_failed"> Calibraition Failed ,Please retry</string>

    <string name="uxsdk_setting_menu_flyc_smart_rth_preset_altitude_des">When lighting is sufficient and environment is suitable for vision system to work properly, aircraft will automatically fly to open area, ascend to preset altitude, and fly an optimal RTH route</string>
    <string name="uxsdk_setting_menu_flyc_smart_rth_smart_altitude_des">When lighting is sufficient and environment is suitable for vision system to work properly, aircraft will automatically plan an optimal RTH route and altitude. Fly with caution</string>

    <!--RcCheckFrequency Widget-->
    <string name="uxsdk_setting_ui_rc_stop_pair">StopPairing</string>
    <string name="uxsdk_setting_ui_rc_start_pair">StartPairing</string>
    <string name="uxsdk_setting_ui_rc_pairing_finish">Linking Complete</string>


    <!-- APAS -->
    <string name="uxsdk_fpv_setting_safe_obstacle_avoidance_behavior_list">Obstacle Avoidance</string>
    <string name="uxsdk_fpv_setting_safe_obstacle_avoidance_behavior_off_btn">Off</string>
    <string name="uxsdk_fpv_setting_safe_obstacle_avoidance_behavior_off_description">When enabled, aircraft will not avoid obstacles</string>
    <string name="uxsdk_fpv_setting_safe_obstacle_avoidance_behavior_brake_btn">Brake</string>
    <string name="uxsdk_fpv_setting_safe_obstacle_avoidance_behavior_brake_description">When enabled, aircraft will brake when obstacles are detected</string>
    <string name="uxsdk_fpv_setting_safe_obstacle_avoidance_behavior_detour_btn">Avoid</string>
    <string name="uxsdk_fpv_setting_safe_obstacle_avoidance_behavior_detour_description">When enabled, aircraft will bypass obstacles or hover</string>
    <string name="uxsdk_setting_menu_perception_break_description">Brake: Aircraft will automatically brake when obstacles are detected. Enable obstacle avoidance in corresponding directions required</string>
    <string name="uxsdk_setting_menu_perception_apas_description">Avoid: Aircraft will automatically bypass obstacles or hover when in Normal mode</string>
    <string name="uxsdk_setting_menu_perception_close_description">Off: Aircraft will not automatically bypass obstacles or brake. Alerts still received for detected obstacles. Fly with caution</string>
    <string name="uxsdk_setting_menu_perception_apas_off_dialog_content">When disabled, horizontal, upward, and downward obstacle avoidance are unavailable. Aircraft will not automatically brake when obstacles are detected. Alerts still received for detected obstacles. Continue?</string>
    <string name="uxsdk_setting_menu_perception_break_s_mode">Aircraft in %1$s mode. Unable to brake. Switch to N mode</string>
    <string name="uxsdk_setting_menu_perception_apas_s_mode">Aircraft in %1$s mode. Unable to bypass. Switch to N mode</string>




    <!--General Widget-->
    <string name="uxsdk_setting_ui_general_battery">Battery</string>
    <string name="uxsdk_fpv_top_bar_battery_left_battery">Left Battery</string>
    <string name="uxsdk_fpv_top_bar_battery_right_battery">Right Battery</string>
    <string name="uxsdk_setting_ui_gimbal_calibration_tip">Stop motor running before gimbal auto calibration.</string>
    <string name="uxsdk_setting_menu_title_rc_check_frequency">Link to Aircraft</string>
    <string name="uxsdk_dialog_message_rc_cannot_frequency_motorup">The remote controller cannot be linked when the aircraft motors are spinning.</string>
    <string name="uxsdk_setting_ui_omni_perception_desc">Disabling the Vision Positioning System will have the following effects on the aircraft:\n1. Unable to hover in place at low altitude indoors.\n2. Unable to engage obstacle braking indoors.\n3. Unable to engage landing speed protection. Landing at high speed may damage the aircraft. Pay attention to the landing speed.</string>
    <string name="uxsdk_battery_check_battery_detail_button_txt">Detalles</string>
    <string name="uxsdk_setting_ui_gimbal_auto_calibration_tip">Check that aircraft is level and nothing is obstructing the gimbal’s range of motion.
        begin auto calibration.</string>

    <string name="uxsdk_setting_menu_flyc_gohome_altitude_title">RTH Altitude</string>
    <string name="uxsdk_setting_menu_flyc_max_height_title">Max Altitude</string>
    <string name="uxsdk_setting_menu_flyc_max_radius_title">Distance Limit</string>
    <string name="uxsdk_setting_menu_flyc_max_radius_title_limit">Max Flight Distance</string>
    <string name="uxsdk_setting_menu_setting_success">Saved successfully</string>
    <string name="uxsdk_setting_menu_setting_fail">Setting failed</string>

    <string name="uxsdk_setting_menu_flyc_gohome_altitude_limit">Failsafe RTH altitude cannot exceed maximum flight altitude.</string>
    <string name="uxsdk_setting_menu_flyc_flight_altitude_limit">Failed to set. Input the number in the range</string>
    <string name="uxsdk_setting_menu_flyc_flight_altitude_limit_doi">Failed to set. Input the number in the range</string>
    <string name="uxsdk_setting_menu_flyc_gohome_altitude_desc">When returning to home from more than 50 m away, the aircraft flies to the pre-set RTH altitude and returns to home. When returning to home from within 50 m of the home point, the aircraft maintains its current altitude and returns to home. If the sensor system is working normally, the aircraft ascends or hovers when obstacles are detected</string>
    <string name="uxsdk_checklist_manual_flight_mode_title">Flight Mode</string>
    <string name="uxsdk_setting_menu_flyc_fpa">Enable Multiple Flight Mode</string>
    <string name="uxsdk_setting_menu_flyc_mode_nosport">P (Positioning) Mode - Satellite and Vision Positioning sensors

        enabled for precise positioning.\n\nA (Attitude) Mode - Satellite and Vision Positioning sensors disabled. Only

        barometer is used for altitude control.\n\nF (Function) Mode - similar to P mode and allows access to

        Intelligent Flight Modes.</string>

    <string name="uxsdk_setting_ui_flyc_mode_spt">T/P/S</string>
    <string name="uxsdk_setting_ui_flyc_mode_snt">T/S/N</string>
    <string name="uxsdk_setting_ui_flyc_mode_sna">A/S/N</string>
    <string name="uxsdk_setting_ui_flyc_mode_tns">T/N/S</string>
    <string name="uxsdk_setting_ui_flyc_mode_spa">A/P/S</string>
    <string name="uxsdk_setting_ui_flyc_mode_ans">A/N/S</string>

    <string name="uxsdk_checklist_manual_compass_title">Compass</string>
    <string name="uxsdk_checklist_manual_imu_title">IMU</string>
    <string name="uxsdk_setting_menu_flyc_mode">Sport (S) Mode - Aircraft mobility and flight speed increases. Satellite and Vision Positioning systems enabled. \n\nPosition (P) Mode - Satellite and Vision Positioning systems enabled. \n\nTripod (T) Mode - Max flight speed of 1 m/s. Flight maneuverability is reduced to help fine-tune compositions smoothly. GPS and downward vision positioning systems enabled.</string>
    <string name="uxsdk_setting_menu_flyc_mode_atti">Sport (S) Mode - Aircraft mobility and flight speed increases. Satellite and Vision Positioning systems enabled.\nPosition (P) Mode - Satellite and Vision Positioning systems enabled.\nAttitude (A) Mode - Forward and Backward Obstacle Sensing, Satellite and Downward Vision Positioning systems disabled. Precise hovering unavailable.</string>
    <string name="uxsdk_setting_menu_flyc_mode_atti_n_mode">Attitude (A) Mode - Forward and Backward Obstacle Sensing, Satellite and Downward Vision Positioning systems disabled. Precise hovering unavailable.\nSport (S) Mode - Aircraft mobility and flight speed increases. Satellite and Vision Positioning systems enabled.\nNormal (N) Mode - Satellite and Vision Positioning systems enabled.</string>
    <string name="uxsdk_setting_menu_flyc_mode_atti_n_mode_rc511">Attitude (A) Mode - Forward and Backward Obstacle Sensing, Satellite and Downward Vision Positioning systems disabled. Precise hovering unavailable.\nNormal (N) Mode - Satellite and Vision Positioning systems enabled.\nSport (S) Mode - Aircraft mobility and flight speed increases. Satellite and Vision Positioning systems enabled.</string>
    <string name="uxsdk_setting_menu_flyc_mode_m300">Tripod (T) Mode: Max flight speed is limited for smoother shooting while maintaining the positioning settings of P mode.\nPosition (P) Mode: Satellite and downward vision positioning systems enabled.\nSport (S) Mode: Aircraft mobility and flight speed increases. Satellite and downward vision positioning systems enabled.</string>
    <string name="uxsdk_setting_menu_flyc_mode_n">Tripod (T) Mode: Max flight speed is limited for smoother shooting while maintaining the positioning settings of N mode.\n\nSport (S) Mode: Aircraft mobility and flight speed increases. Satellite and downward vision positioning systems enabled.\n\nNormal (N) Mode: Satellite and downward vision positioning systems enabled.</string>
    <string name="uxsdk_setting_menu_flyc_mode_n_rc511">Tripod (T) Mode: Max flight speed is limited for smoother shooting while maintaining the positioning settings of N mode.\n\nSport (S) Mode: Aircraft mobility and flight speed increases. Satellite and downward vision positioning systems enabled.\n\nNormal (N) Mode: Satellite and downward vision positioning systems enabled.</string>
    <string name="uxsdk_setting_menu_flyc_mode_atti_dialog_title">Flight Mode Configuration Switched to A/P/S</string>
    <string name="uxsdk_setting_menu_flyc_mode_atti_dialog_title_n_mode_rc511">Flight Mode Configuration Switched to A/N/S</string>
    <string name="uxsdk_setting_menu_flyc_mode_atti_dialog_title_n_mode">Flight Mode Configuration Switched to A/S/N</string>
    <string name="uxsdk_setting_menu_title_sensors_state">Sensor Status</string>
    <string name="uxsdk_setting_menu_title_device_name">Device Name</string>
    <string name="uxsdk_setting_menu_title_about">About</string>

    <string name="uxsdk_vision_flash_light_auto">AUTO</string>
    <string name="uxsdk_vision_flash_light_on">ON</string>
    <string name="uxsdk_vision_flash_light_off">OFF</string>
    <string name="uxsdk_setting_menu_title_led">LED Settings</string>
    <string name="uxsdk_setting_ui_rc_custom_navigation_led">Navigation Beacon On/Off</string>
    <string name="uxsdk_setting_common_leds_hide_mode">Hide Mode</string>
    <string name="uxsdk_setting_menu_top_auxiliary_light">Top Auxiliary Light</string>
    <string name="uxsdk_setting_menu_bottom_auxiliary_light">Bottom Auxiliary Light</string>
    <string name="uxsdk_setting_menu_beacon_led">Beacons</string>
    <string name="uxsdk_setting_menu_arm_led">Frame Arm LEDs</string>
    <string name="uxsdk_setting_menu_status_led">Status LEDs</string>

    <string name="uxsdk_setting_ui_redundancy_sensor_imu_desc_cur_use">In Use</string>
    <string name="uxsdk_setting_ui_redundancy_sensor_imu_desc_good">Excellent</string>
    <string name="uxsdk_setting_ui_redundancy_sensor_imu_desc_normal">Good</string>
    <string name="uxsdk_setting_ui_redundancy_sensor_imu_desc_bad">Poor</string>
    <string name="uxsdk_setting_ui_redundance_compass_cal_tip">Motors Spinning. Cannot Calibrate Compass.</string>
    <string name="uxsdk_setting_ui_compass_cal_in_sim_tip">Unable to calibrate compass in simulator</string>
    <string name="uxsdk_setting_ui_redundancy_sensor_imu_calc">Calibrate IMU</string>
    <string name="uxsdk_setting_ui_redundancy_sensor_compass_calc">Calibrate Compass</string>
    <string name="uxsdk_setting_ui_redundancy_sensor_imu_acc_label">Accelerometer</string>
    <string name="uxsdk_setting_ui_redundancy_sensor_imu_gryo_label">Gyroscope</string>
    <string name="uxsdk_setting_ui_redundancy_sensor_imu_bias">Bias</string>
    <string name="uxsdk_setting_ui_redundancy_sensor_compass_vector">Interference</string>
    <string name="uxsdk_setting_ui_redundancy_sensor_compass_remark">Note</string>
    <string name="uxsdk_setting_ui_redundancy_sensor_compass">Compass</string>
    <string name="uxsdk_setting_ui_redundancy_sensor_compass1_label">Compass 1</string>
    <string name="uxsdk_setting_ui_redundancy_sensor_compass2_label">Compass 2</string>
    <string name="uxsdk_setting_ui_redundancy_sensor_compass3_label">Compass 3</string>

    <string name="uxsdk_setting_ui_a3sensor_imu0">IMU1</string>
    <string name="uxsdk_setting_ui_a3sensor_imu1">IMU2</string>
    <string name="uxsdk_setting_ui_a3sensor_imu2">IMU3</string>

    <string name="uxsdk_setting_ui_a3sensor_acc0">ACC1</string>
    <string name="uxsdk_setting_ui_a3sensor_acc1">ACC2</string>
    <string name="uxsdk_setting_ui_a3sensor_acc2">ACC3</string>

    <string name="uxsdk_setting_ui_a3sensor_gyro0">GYRO1</string>
    <string name="uxsdk_setting_ui_a3sensor_gyro1">GYRO2</string>
    <string name="uxsdk_setting_ui_a3sensor_gyro2">GYRO3</string>

    <string name="uxsdk_setting_ui_redundancy_sensor_imu_stat_0">Unknown Status</string>
    <string name="uxsdk_setting_ui_redundancy_sensor_imu_stat_1">Disconnected</string>
    <string name="uxsdk_setting_ui_redundancy_sensor_imu_stat_2">Calibrating…</string>
    <string name="uxsdk_setting_ui_redundancy_sensor_imu_stat_3">Calibration Failed</string>
    <string name="uxsdk_setting_ui_redundancy_sensor_imu_stat_4">Data Error</string>
    <string name="uxsdk_setting_ui_redundancy_sensor_imu_stat_5">Warming Up</string>
    <string name="uxsdk_setting_ui_redundancy_sensor_imu_stat_6">Moving</string>

    <string name="uxsdk_setting_ui_redundancy_sensor_compass_stat_0">Unknown Status</string>
    <string name="uxsdk_setting_ui_redundancy_sensor_compass_stat_1">Disconnected</string>
    <string name="uxsdk_setting_ui_redundancy_sensor_compass_stat_2">Calibrating…</string>
    <string name="uxsdk_setting_ui_redundancy_sensor_compass_stat_3">Not Calibrated</string>
    <string name="uxsdk_setting_ui_redundancy_sensor_compass_stat_4">Data Error</string>
    <string name="uxsdk_setting_ui_redundancy_sensor_compass_stat_empty"/>
    <string name="uxsdk_setting_ui_redundancy_sensor_compass_stat_8">Calibration Failed</string>
    <string name="uxsdk_setting_ui_redundancy_sensor_compass_stat_9">Direction Error</string>
    <string name="uxsdk_setting_ui_redundancy_sensor_compass_stat_action_0">Restart Aircraft</string>
    <string name="uxsdk_setting_ui_redundancy_sensor_compass_stat_action_1">Disconnected</string>
    <string name="uxsdk_setting_ui_redundancy_sensor_compass_stat_action_3">Calibrate Compass</string>
    <string name="uxsdk_setting_ui_redundancy_sensor_compass_stat_action_4">Contact DJI Support if the problem persists after restarting.</string>
    <string name="uxsdk_setting_ui_redundancy_sensor_compass_stat_action_7">Move away from interference or recalibrate the compass.</string>
    <string name="uxsdk_setting_ui_redundancy_sensor_compass_stat_action_8">Move away from interference or recalibrate the compass.</string>
    <string name="uxsdk_setting_ui_redundancy_sensor_compass_stat_action_9">Check that the compass is installed in the correct direction or if there is

        electro-magnetic interference.</string>
    <string name="uxsdk_setting_ui_imu_tip">Motors Spinning. Cannot Calibrate IMU.</string>
    <string name="uxsdk_app_check_aircraft_connection">Please check if the aircraft is connected.</string>
    <string name="uxsdk_setting_menu_title_sensors_state_na_info">Aircraft uses visual orientation instead of compass orientation when the ambient light is good (such as daytime). Interference to the
 compass can be ignored</string>
    <string name="uxsdk_setting_menu_flyc_failSafe_title">Signal Lost Action</string>
    <string name="uxsdk_setting_menu_common_gimbal_version">Gimbal %1$s</string>
    <string name="uxsdk_setting_menu_common_camera_version">%1$s Camera</string>
    <string name="uxsdk_setting_menu_common_serial">SN</string>
    <string name="uxsdk_hms_carepage_maintenance_highvoltage_title">Store at High Battery Level</string>
    <string name="uxsdk_hms_carepage_maintenance_highvoltage_info">Store at High Battery Level means storing battery when it has 90% or higher battery level. If high battery level storage time in total is less than 120 days in 12 months, battery can be charged for up to 400 cycles. Change Time to Battery Self-Discharge to adjust time of battery storage at high level</string>
    <string name="uxsdk_hms_carepage_maintenance_highvoltage_need_upgrade_info">Aircraft firmware version too early. Failed to obtain high battery level storage time. Firmware update recommended</string>
    <string name="uxsdk_setting_ui_battery_product_date">Production Date:</string>
    <string name="uxsdk_setting_ui_battery_serial_number">Serial Number:</string>
    <string name="uxsdk_setting_ui_battery_discharge_day">%1$d Days</string>
    <string name="uxsdk_setting_ui_battery_history_normal_status">Connection OK</string>
    <string name="uxsdk_setting_ui_battery_abnormal_state">Error</string>
    <string name="uxsdk_setting_ui_battery_history_invalid_status">Invalid Battery</string>
    <string name="uxsdk_setting_ui_battery_history_exception_status">Battery Connection Broken</string>
    <string name="uxsdk_setting_ui_battery_history_firstlevel_current">Overcurrent During Discharge</string>
    <string name="uxsdk_setting_ui_battery_history_secondlevel_current">Overcurrent During Discharge</string>
    <string name="uxsdk_setting_ui_battery_history_firstlevel_over_temperature">Overheating During Discharge</string>
    <string name="uxsdk_setting_ui_battery_history_secondlevel_overt_temperature">Overheating During Discharge</string>
    <string name="uxsdk_setting_ui_battery_history_firstlevel_low_temperature">Low Temperature During Discharge</string>
    <string name="uxsdk_setting_ui_battery_history_secondlevel_low_temperature">Low Temperature During Discharge</string>
    <string name="uxsdk_setting_ui_battery_history_short_circuit">Short Circuit During Discharge</string>
    <string name="uxsdk_setting_ui_battery_history_under_voltage">Low Voltage Protection</string>
    <string name="uxsdk_setting_ui_battery_history_invalid">Cell Broken</string>
    <string name="uxsdk_setting_ui_battery_history_discharge">Self-Discharging During Storage</string>
    <string name="uxsdk_celsius">°C</string>
    <string name="uxsdk_fahrenheit">°F</string>
    <string name="uxsdk_kelvins">K</string>
    <string name="uxsdk_checklist_manual_serious_low_battery_percent">Critically Low: %1$s</string>
    <string name="uxsdk_checklist_manual_low_battery_percent">Low: %1$s</string>
    <string name="uxsdk_checklist_manual_avoidance_warning">Alert: %1$s</string>
    <string name="uxsdk_checklist_manual_avoidance_stop">Brake: %1$s</string>
    <string name="uxsdk_setting_common_device_name_input_tip">Input DJI Device Name</string>
    <string name="uxsdk_setting_common_device_name_illegal">Folder name must not contain the following characters: &lt; &gt; : \" / \\ | ? * . _</string>
    <string name="uxsdk_setting_common_device_name_save_success">Saving successful.</string>
    <string name="uxsdk_setting_common_device_name_save_fail">Failed to Save</string>

    <string name="uxsdk_setting_ui_hd_sdr_channel_select">Image Transmission Channel</string>
    <string name="uxsdk_setting_ui_hd_sdr_channel_select_des">Select no more than two image transmission channels</string>
    <string name="uxsdk_setting_ui_hd_sdr_signal_good">Good</string>
    <string name="uxsdk_setting_ui_hd_sdr_signal_normal">Average</string>
    <string name="uxsdk_setting_ui_hd_sdr_signal_bad">Weak</string>
    <string name="uxsdk_setting_ui_hd_sdr_channel_state_title">Signal Strength</string>
    <string name="uxsdk_setting_ui_hd_sdr_channel_state_summary">\nSignal Strength: %1$s\n\nDownlink Bandwidth: %2$s\n\nImage Transmission Bit Rate: %3$s\n</string>
    <string name="uxsdk_setting_ui_hd_lb_channel_state_summary">\nSignal Strength: %1$s\n\nChannel: %2$s\n\nImage Transmission Bit Rate: %3$s\n</string>

    <string name="uxsdk_setting_ui_hd_sdr_channel_select_title">Image channel selection</string>
    <string name="uxsdk_setting_ui_hd_sdr_channel_choose">Please select</string>
    <string name="uxsdk_setting_ui_hd_sdr_channel_0">Signal 0</string>
    <string name="uxsdk_setting_ui_hd_sdr_channel_1">Signal 1</string>

    <!--region imu calibration -->
    <string name="uxsdk_setting_ui_imu_title">IMU Calibration</string>
    <string name="uxsdk_setting_ui_imu_start">Start</string>
    <string name="uxsdk_setting_ui_imu_prepare_desc1">Dismount Propellers first.</string>
    <string name="uxsdk_setting_ui_imu_prepare_desc2">Follow the instruction and indication to calibrate. Do not move the aircraft unless to overturn the aircraft as indicated.</string>
    <string name="uxsdk_setting_ui_imu_prepare_desc3">Ensure the aircraft is powered on. Do not start motor.</string>
    <string name="uxsdk_setting_ui_imu_process_desc1">Place the aircraft on dry and plain ground as indicated.</string>
    <string name="uxsdk_setting_ui_imu_process_desc2">Ensure the aircraft is powered on. Do not start motor.</string>
    <string name="uxsdk_setting_ui_imu_success">IMU Calibration Success</string>
    <string name="uxsdk_setting_ui_imu_back">Back to Camera View</string>
    <string name="uxsdk_setting_ui_imu_restart">Restart aircraft</string>
    <string name="uxsdk_setting_ui_imu_fail">IMU Calibration Failed. Please retry.</string>
    <string name="uxsdk_setting_ui_imu_fail_reason">No %1$d IMU Calibration error code is %2$d</string>
    <string name="uxsdk_setting_ui_imu_retry">Retry</string>
    <string name="uxsdk_setting_ui_imu_exit_tip">IMU Calibrating. Aircraft cannot work now. Exit now?</string>
    <string name="uxsdk_setting_ui_imu_calibrating">IMU Calibrating. Please wait.</string>

    <!--endregion-->

    <string name="uxsdk_setting_menu_flyc_home_point">Home Point Settings</string>
    <string name="uxsdk_fpv_toast_homepoint_setting_drone">Aircraft location set as Home Point</string>
    <string name="uxsdk_fpv_toast_homepoint_setting_current_rc">Remote controller location set as Home Point</string>
    <string name="uxsdk_fpv_toast_homepoint_setting_partner_rc">Companion controller location set as Home Point</string>
    <string name="uxsdk_fpv_toast_homepoint_setting_pin">PinPoint set as Home Point</string>
    <string name="uxsdk_fpv_toast_homepoint_gps_weak">GNSS signal weak. Unable to obtain location info</string>
    <string name="uxsdk_fpv_toast_homepoint_setting_failed">Failed to set Home Point</string>

    <!-- Rc Calibration -->
    <string name="uxsdk_setting_ui_rc_calibration">Remote Controller Calibration</string>
    <string name="uxsdk_setting_ui_rc_cal">Calibrate</string>
    <string name="uxsdk_setting_ui_rc_finish_tip">Push all sticks throughout their maximum range and repeat several times. Then press Finish to complete the calibration.</string>
    <string name="uxsdk_setting_ui_rc_start">Start</string>
    <string name="uxsdk_setting_ui_rc_finish">Done</string>
    <string name="uxsdk_setting_ui_rc_cele_tip">Center both control sticks before pressing Start. Follow the prompts on screen to calibrate.</string>
    <string name="uxsdk_setting_ui_rc_cele">Calibrate</string>
    <string name="uxsdk_setting_ui_rc_limit_tip">Move each stick through its complete range of motion several times. Then press Finish to complete calibration.</string>
    <string name="uxsdk_setting_ui_rc_middle_desc">Center both control sticks, then press Start to begin calibration.</string>
    <string name="uxsdk_setting_ui_rc_calibration_time_out">The last remote controller calibration has been time out, the calibration state will be reset.</string>
    <string name="uxsdk_setting_ui_setting_fail_disconnect">Setting failed. Check the device connection and retry.</string>
    <string name="uxsdk_setting_ui_rc_tip_disconnect">Disconnected</string>
    <string name="uxsdk_setting_ui_rc_calibration_1">1.Stick Calibration: Move the sticks through their entire range of motion repeatedly.</string>
    <string name="uxsdk_setting_ui_rc_calibration_2">2.Left Dial Calibration: Turn the left dial up to its limit repeatedly.</string>
    <string name="uxsdk_setting_ui_rc_pro_calibration_2">2.Dial Calibration: Turn the dials up to their limits repeatedly.</string>
    <string name="uxsdk_setting_ui_rc_pro_calibration_3">3.Lever Calibration: Turn levers up to their limits repeatedly.</string>
    <string name="uxsdk_setting_ui_rc_calibration_hint_start">Tap Calibrate and follow instructions.</string>
    <string name="uxsdk_setting_ui_rc_calibration_status_finish">Remote Controller Calibration Completed</string>
    <string name="uxsdk_setting_ui_rc_calibration_hint_wheel_calibration">Turn the left dial left or right to the as indicated. Repeat for the right dial.</string>
    <string name="uxsdk_setting_ui_rc_calibration_status_wheel">Left Dial Calibration</string>
    <string name="uxsdk_setting_ui_rc_calibration_hint_calibration">Push the stick as indicated. Repeat.</string>
    <string name="uxsdk_setting_ui_rc_calibration_continue">Next</string>
    <string name="uxsdk_setting_ui_rc_calibration_tip">Power off the aircraft, then calibrate the remote controller.</string>
    <string name="uxsdk_setting_ui_rc_cele_in_progress_hint">Follow the instructions to proceed.</string>
    <string name="uxsdk_setting_ui_rc_calibration_stick_label">Control Sticks</string>
    <string name="uxsdk_setting_ui_rc_calibration_dial_label">Dial</string>
    <string name="uxsdk_setting_ui_rc_calibration_left_wheel_label">Left Dial Calibration</string>
    <string name="uxsdk_setting_ui_rc_calibration_right_wheel_label">Right Dial Calibration</string>
    <string name="uxsdk_setting_ui_rc_calibration_left_label">Left Dial</string>
    <string name="uxsdk_setting_ui_rc_calibration_right_label">Right Dial</string>
    <string name="uxsdk_setting_ui_rc_calibration_stick_desc">Move control sticks gently to fill out the square with the ball\'s trajectories.</string>
    <string name="uxsdk_setting_ui_rc_calibration_wheel_desc">Turn the left and right dials to the maximum repeatedly.</string>
    <string name="uxsdk_setting_ui_rc_plus_calibration_1">Slowly move both control sticks through their entire range of motion</string>



    <string name="uxsdk_fpv_checklist_calibration_success">Compass Calibrated.</string>
    <string name="uxsdk_fpv_checklist_calibration_fail">Compass Calibration Failed</string>
    <string name="uxsdk_fpv_checklist_compass_tip_1">Calibrating compass. Stay clear of magnets or metal objects. Lift the aircraft to about 1.5 m (4.9 ft) above the ground and rotate for 360°.</string>
    <string name="uxsdk_fpv_checklist_compass_tip_2">Calibrating compass. Stay clear of magnets or metal objects. Lift the aircraft to about 1.5 m (4.9 ft) above the ground and rotate for 360° vertically.</string>
    <string name="uxsdk_fpv_checklist_compass_tip_1_desc">Rotate the aircraft horizontally for 360°</string>
    <string name="uxsdk_fpv_checklist_compass_tip_2_desc">Rotate the aircraft 360° vertically</string>
    <string name="uxsdk_fpv_checklist_compass_820_tip_2_desc">As shown in the figure, hold Arm 3 and 6, and then spin a circle in place.</string>
    <string name="uxsdk_fpv_checklist_cancel_cele">Cancel Calibration</string>
    <string name="uxsdk_fpv_compass_adjust_complete">Compass Calibration Complete</string>
    <string name="uxsdk_fpv_compass_adjust_fail">Intensive electromagnetic interference nearby. Ensure no magnets or metal objects are near the compass and keep the aircraft 1 meter above ground for recalibration.</string>
    <string name="uxsdk_fpv_low_battery_back_home_tip">The remaining battery is only enough for RTH. Return home now.</string>
    <string name="uxsdk_setting_ui_compass_tip">Cannot calibrate compass while the motors are rotating.</string>
    <string name="uxsdk_app_operator_fail">Operate Failed ,Please Retry</string>
    <string name="uxsdk_setting_menu_desc_omni_perception_downwards">Vision positioning systems help aircraft hover and support landing protection when GPS signal is weak. Disabling downward vision system will disable landing protection</string>

    <string name="uxsdk_setting_menu_flyc_limit_high_notice">Flying above 120 m (400 ft) may violate local laws and regulations. To ensure flight safety, make sure you understand relevant regulations before flying</string>
    <string name="uxsdk_setting_menu_flyc_limit_high_notice_above_500">Flying above 500 m (1,640 ft) may violate local laws and regulations. To ensure flight safety, make sure you understand relevant regulations before flying. Max altitude settings will revert to default after restarting aircraft</string>
    <string name="uxsdk_setting_menu_flyc_mode_atti_dialog_content">In this configuration, Tripod (T) mode becomes Attitude (A) mode. The current flight mode is not affected. \n In A mode, the aircraft is easily disturbed and drifts horizontally as a result. Aircraft is unable to hover or engage Active Braking. Control the aircraft to hover manually.</string>
    <string name="uxsdk_setting_menu_flyc_smart_rth_set_altitude">When aircraft is farther than 50 m from Home Point, aircraft will automatically calculate RTH route, fly to open area, ascend to preset altitude, and return to home\nWhen aircraft is 5 to 50 m from Home Point, aircraft will fly planned route and return to home at current altitude\nWhen aircraft is less than 5 m from Home Point, aircraft will land automatically\nIf lighting is insufficient or environment is not suitable for vision system to work properly, such as direct sunlight at dusk or weak or no light at night, aircraft will fly to Home Point in straight line at preset altitude</string>
    <string name="uxsdk_setting_menu_flyc_smart_rth_smart_altitude">Aircraft will automatically calculate optimal RTH route. If lighting is insufficient or environment is not suitable for vision system to work properly, such as direct sunlight at dusk or weak or no light at night, aircraft will fly to Home Point in straight line at preset altitude</string>
    <string name="uxsdk_setting_menu_close_bottom_aux_tips">Turning off the bottom auxiliary light disables the downward vision sensing system when landing in low ambient light. Fly with caution.</string>

    <string name="uxsdk_fpv_gimbal_roll_finetune">Horizontal</string>
    <string name="uxsdk_fpv_gimbal_yaw_finetune">Yaw</string>
    <string name="uxsdk_fpv_gimbal_pitch_finetune">Tilt</string>
    <string name="uxsdk_setting_menu_title_gimbal_reset_param">Reset Gimbal Parameters</string>
    <string name="uxsdk_setting_menu_title_gimbal_calibration">Gimbal Calibration</string>

    <string name="uxsdk_link_frequency_band_dual">Dual Band</string>
    <string name="uxsdk_link_frequency_band_three">Three Bands</string>
    <string name="uxsdk_link_frequency_band_2_dot_4">2.4G</string>
    <string name="uxsdk_link_frequency_band_5">5G</string>
    <string name="uxsdk_link_frequency_band_5_dot_7">5.7G</string>
    <string name="uxsdk_link_frequency_band_1_dot_4">840M&amp;1.4G</string>

    <string name="uxsdk_setting_ui_hd_channel_auto">Auto</string>
    <string name="uxsdk_setting_ui_hd_channel_custom">Personalizado</string>
    <string name="uxsdk_hdmi_system_setting_mode_same">Duplicar pantalla</string>
    <string name="uxsdk_hdmi_system_setting_mode_fpv">Vista de cámara</string>
    <string name="uxsdk_hdmi_system_setting_mode_camera">Vista de cámara limpia</string>

    <string name="uxsdk_assistant_video_empty_text">View after take off</string>
    <string name="app_done">Done</string>
    <string name="setting_menu_title_gimbal_fine_tune">Adjust Gimbal</string>
    <string name="setting_ui_hd_sdr_custom_selected_tip">*Select Custom mode with caution, as you will be required to choose a transmission channel yourself. Choosing a channel with strong interference may lower image transmission quality.</string>
    <string name="setting_ui_hd_sdr_mincolor_desc">Minor interference</string>
    <string name="setting_ui_hd_sdr_maxcolor_desc">Strong interference</string>
    <string name="setting_ui_hd_sdr_channel_select_tip">Select the channel under weak interference. Select 10MHZ Channel when in strong interference.</string>
    <string name="checklist_manual_drone_battery_title">Customize Battery Warning</string>
    <string name="setting_menu_common_aircraft_version">Aircraft Version</string>
    <string name="setting_menu_common_rc_version">Remote Controller</string>
    <string name="setting_menu_common_flyc_serial">Flight Controller SN</string>
    <string name="setting_menu_rtk_not_a_num">N/A</string>
    <string name="setting_menu_common_rc_serial">Remote Controller SN</string>
    <string name="setting_menu_common_rtk_module_serial">RTK Module Serial Number</string>
    <string name="setting_menu_flyc_rth_mode">Return to Home</string>
    <string name="setting_menu_flyc_smart_rth_set_altitude">When aircraft is farther than 50 m from Home Point, aircraft will automatically calculate RTH route, fly to open area, ascend to preset altitude, and return to home\nWhen aircraft is 5 to 50 m from Home Point, aircraft will fly planned route and return to home at current altitude\nWhen aircraft is less than 5 m from Home Point, aircraft will land automatically\nIf lighting is insufficient or environment is not suitable for vision system to work properly, such as direct sunlight at dusk or weak or no light at night, aircraft will fly to Home Point in straight line at preset altitude</string>
    <string name="setting_common_device_name">Device Name</string>
    <string name="setting_common_save">Save</string>
    <string name="setting_ui_redundancy_sensor_compass_vector">Interference</string>
    <string name="setting_menu_title_hd_frequency">Work Frequency</string>
    <string name="fpv_setting_safe_obstacle_avoidance_behavior_list">Obstacle Avoidance</string>
    <string name="setting_menu_desc_perception_precision_landing">When the aircraft takes off vertically to a height of at least 7 m, it automatically collects information for the area around the takeoff point. When the aircraft finishes collecting information, precision landing will be available during RTH.</string>
    <string name="setting_ui_hd_sdr_bandwidth_desc">Downlink Bandwidth</string>
    <string name="setting_menu_title_hd_channel_mode">Channel Mode</string>
    <string name="setting_ui_hd_sdr_channel_state_title">Signal Strength</string>
    <string name="setting_menu_title_hd_data_rate">Image Transmission Code Rate</string>
    <string name="setting_ui_imu_title">IMU Calibration</string>
    <string name="setting_ui_imu_calibrating">IMU Calibrating. Please wait.</string>
    <string name="setting_ui_imu_start">Start</string>
    <string name="setting_ui_imu_success">IMU Calibration Success</string>
    <string name="setting_ui_imu_back">Back to Camera View</string>
    <string name="setting_ui_imu_restart">Restart aircraft</string>
    <string name="setting_ui_imu_prepare_desc2">Follow the instruction and indication to calibrate. Do not move the aircraft unless to overturn the aircraft as indicated.</string>
    <string name="setting_ui_rc_calibration_stick_label">Control Sticks</string>
    <string name="setting_ui_rc_calibration_dial_label">Dial</string>
    <string name="setting_ui_rc_cal">Calibrate</string>
    <string name="setting_ui_rc_calibration_stick_desc">Move control sticks gently to fill out the square with the ball\'s trajectories.</string>
    <string name="setting_ui_rc_calibration_left_wheel_label">Left Dial Calibration</string>
    <string name="setting_ui_battery_charge_times">Cycle Counts</string>
    <string name="setting_ui_battery_volume_percent_txt">Power</string>
    <string name="setting_ui_battery_temperature">Temperature</string>
    <string name="setting_ui_battery_voltage_desc">Voltage</string>
    <string name="hms_carepage_maintenance_highvoltage_title">Stored at High Battery Level</string>
    <string name="setting_ui_battery_total_volume_percent_txt">Total Power</string>
    <string name="setting_ui_battery_fly_time">Flight Time</string>
    <string name="setting_menu_summary_perception_precision_landing">Precision Landing</string>
    <string name="setting_menu_title_rc_check_frequency">Link to Aircraft</string>
    <string name="setting_menu_summary_omni_perception_downwards">Enable Vision Positioning</string>

    <string name="uxsdk_camera_exposure_aperture_title">Aperture</string>
    <string name="uxsdk_camera_exposure_shutter_title">Shutter</string>
    <string name="uxsdk_camera_exposure_shutter_underexposed_title">Shutter (Underexposed)</string>
    <string name="uxsdk_camera_exposure_shutter_overexposed_title">Shutter (Overexposed)</string>

    <string name="uxsdk_app_tip">Tips</string>
    <string name="uxsdk_camera_exposure_ev_title">EV</string>

    <string name="uxsdk_rc_lost_action_continue">Continue Execution</string>
    <string name="uxsdk_rc_lost_action_exit">End Task</string>

    <!--返航-->
    <string name="uxsdk_rc_lost_action_return_to_home">Return to Home</string>
    <!--降落-->
    <string name="uxsdk_rc_lost_action_land">Land</string>
    <!--悬停-->
    <string name="uxsdk_rc_lost_action_hover">Hover</string>

    <string name="uxsdk_checklist_go_home_low_battery_percent">Go Home Value：%1$s</string>
    <string name="uxsdk_checklist_hover_low_battery_percent">Hover Value：%1$s</string>

    <string name="uxsdk_setting_hd_model_SD">SD</string>
    <string name="uxsdk_setting_hd_model_HD">HD</string>
    <string name="uxsdk_setting_hd_model_FULL_HD">FHD</string>
    <string name="uxsdk_setting_menu_title_hd_hdmi_system_setting_hdmi_mode">Video output mode</string>
</resources>
