<!--
  ~ Copyright (c) 2018-2020 DJI
  ~
  ~ Permission is hereby granted, free of charge, to any person obtaining a copy
  ~ of this software and associated documentation files (the "Software"), to deal
  ~ in the Software without restriction, including without limitation the rights
  ~ to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
  ~ copies of the Software, and to permit persons to whom the Software is
  ~ furnished to do so, subject to the following conditions:
  ~
  ~ The above copyright notice and this permission notice shall be included in all
  ~ copies or substantial portions of the Software.
  ~
  ~ THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
  ~ IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
  ~ FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
  ~ AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
  ~ LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
  ~ OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
  ~ SOFTWARE.
  ~
  -->

<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:width="18dp"
    android:height="23dp"
    android:viewportWidth="18"
    android:viewportHeight="23">
  <path
      android:pathData="M9,3C11.7614,3 14,5.3452 14,8.3263C14,11.131 9,19 9,19C9,19 4,11.131 4,8.3263C4,5.3452 6.2386,3 9,3ZM9,5.5263C7.6193,5.5263 6.5,6.6574 6.5,8.0526C6.5,9.4479 7.6193,10.5789 9,10.5789C10.3807,10.5789 11.5,9.4479 11.5,8.0526C11.5,6.6574 10.3807,5.5263 9,5.5263Z"
      android:strokeAlpha="0.6"
      android:strokeWidth="1"
      android:fillColor="#FFFFFF"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"
      android:fillAlpha="0.6"/>
</vector>
