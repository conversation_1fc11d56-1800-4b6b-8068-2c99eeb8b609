<!--
  ~ Copyright (c) 2018-2020 DJI
  ~
  ~ Permission is hereby granted, free of charge, to any person obtaining a copy
  ~ of this software and associated documentation files (the "Software"), to deal
  ~ in the Software without restriction, including without limitation the rights
  ~ to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
  ~ copies of the Software, and to permit persons to whom the Software is
  ~ furnished to do so, subject to the following conditions:
  ~
  ~ The above copyright notice and this permission notice shall be included in all
  ~ copies or substantial portions of the Software.
  ~
  ~ THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
  ~ IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
  ~ FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
  ~ AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
  ~ LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
  ~ OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
  ~ SOFTWARE.
  ~
  -->

<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:width="20dp"
    android:height="20dp"
    android:viewportWidth="20"
    android:viewportHeight="20">
  <path
      android:pathData="M8.4901,7.3732C9.8752,5.4085 10.9901,7.3732 10.9901,7.3732L10.9901,7.3732L10.811,9.688L16.9941,10.0952L16.9941,11.0899L10.74,10.607L10.6206,12.1664L10.3933,13.044L9.8105,13.5L9.1595,13.044C9.1595,13.044 8.9028,12.1557 8.9025,12.1501L8.9025,12.1501L8.749,10.376L8.7499,10.5726L3.1714,11.1737L3.1714,11.0529L2.962,11.17L2.9939,13.7458C2.9953,13.8627 2.9412,13.9625 2.8738,13.9719L2.6776,13.9993C2.6099,14.0083 2.5464,13.9247 2.5359,13.8092L2.077,8.828L0.6872,8.9928C0.629,8.9996 0.5657,8.962 0.536,8.9031L0.5206,8.8552L0.5021,8.7363C0.4885,8.6488 0.542,8.5744 0.6208,8.5652L0.6208,8.5652L2.038,8.398L2.005,8.0335C1.9596,7.5385 2.0433,7.083 2.1952,7.0619L2.6367,7.0008C2.7883,6.9796 2.9179,7.4054 2.9239,7.9063L2.928,8.294L3.086,8.275L2.994,8.0294L4.9966,8L4.894,8.062L5.4237,8L5.4901,8.4276L4.011,8.601L3.3587,9L3.242,8.692L2.933,8.728L2.952,10.343L2.9562,10.3256L8.687,9.657ZM17.3669,7.0008L17.8084,7.0619C17.9603,7.083 18.044,7.5385 17.9986,8.0335L17.9986,8.0335L17.964,8.399L19.3724,8.5658C19.4455,8.5744 19.4928,8.6248 19.4985,8.6876L19.4947,8.7366L19.4665,8.8538C19.4458,8.9401 19.3597,8.9974 19.2729,8.987L19.2729,8.987L17.925,8.824L17.4676,13.8092C17.4592,13.9016 17.4169,13.9736 17.3657,13.9941L17.326,13.9993L17.1298,13.9719C17.0624,13.9625 17.0083,13.8627 17.0096,13.7458L17.0096,13.7458L17.069,8.72L16.763,8.683L16.6449,9L15.968,8.588L14.7096,8.4365L14.5101,8.4158L14.5585,8L14.7581,8.0207L15.106,8.061L15.007,8L17.0096,8.0294L16.916,8.275L17.074,8.294L17.0797,7.9063C17.0852,7.4471 17.1945,7.0511 17.3295,7.0046L17.3669,7.0008Z"
      android:strokeWidth="1"
      android:fillColor="#FFFFFF"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
</vector>
