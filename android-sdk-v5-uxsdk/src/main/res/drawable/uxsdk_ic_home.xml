<!--
  ~ Copyright (c) 2018-2020 DJI
  ~
  ~ Permission is hereby granted, free of charge, to any person obtaining a copy
  ~ of this software and associated documentation files (the "Software"), to deal
  ~ in the Software without restriction, including without limitation the rights
  ~ to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
  ~ copies of the Software, and to permit persons to whom the Software is
  ~ furnished to do so, subject to the following conditions:
  ~
  ~ The above copyright notice and this permission notice shall be included in all
  ~ copies or substantial portions of the Software.
  ~
  ~ THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
  ~ IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
  ~ FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
  ~ AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
  ~ LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
  ~ OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
  ~ SOFTWARE.
  ~
  -->

<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:width="24dp"
    android:height="23dp"
    android:viewportWidth="24"
    android:viewportHeight="23">
  <path
      android:pathData="M0,11.4838a11.9898,11.4838 0,1 0,23.9795 0a11.9898,11.4838 0,1 0,-23.9795 0z"
      android:strokeWidth="1"
      android:fillColor="#20A3F6"
      android:fillAlpha="0.443925498"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M3.7564,11.4557a8.2136,7.867 0,1 0,16.4272 0a8.2136,7.867 0,1 0,-16.4272 0z"
      android:strokeWidth="1"
      android:fillColor="#FFFFFF"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M8.264,7.1774l0,8.3028l1.4241,0l0,-3.6746l4.4062,0l0,3.6746l1.4241,0l0,-8.3028l-1.4241,0l0,3.4653l-4.4062,0l0,-3.4653z"
      android:strokeWidth="1"
      android:fillColor="#000000"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
</vector>
