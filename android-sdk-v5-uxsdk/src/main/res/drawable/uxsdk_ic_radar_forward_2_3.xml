<!--
  ~ Copyright (c) 2018-2020 DJI
  ~
  ~ Permission is hereby granted, free of charge, to any person obtaining a copy
  ~ of this software and associated documentation files (the "Software"), to deal
  ~ in the Software without restriction, including without limitation the rights
  ~ to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
  ~ copies of the Software, and to permit persons to whom the Software is
  ~ furnished to do so, subject to the following conditions:
  ~
  ~ The above copyright notice and this permission notice shall be included in all
  ~ copies or substantial portions of the Software.
  ~
  ~ THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
  ~ IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
  ~ FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
  ~ AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
  ~ LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
  ~ OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
  ~ SOFTWARE.
  ~
  -->

<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:width="347dp"
    android:height="35dp"
    android:viewportWidth="347"
    android:viewportHeight="35"
    tools:ignore="VectorRaster">
  <path
      android:pathData="M175.406,5.977L175.396,0C201.032,0.074 226.194,1.101 250.342,3.026L247.238,8.796C224.072,7.006 199.967,6.049 175.406,5.977"
      android:strokeWidth="1"
      android:fillColor="#FFC700"
      android:fillAlpha="0.399031929"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M175.417,13.564C198.624,13.617 221.397,14.472 243.333,16.079L246.496,10.2C223.559,8.459 199.709,7.536 175.408,7.481L175.417,13.564Z"
      android:strokeWidth="1"
      android:fillColor="#FFC700"
      android:fillAlpha="0.5"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
</vector>
