<!--
  ~ Copyright (c) 2018-2020 DJI
  ~
  ~ Permission is hereby granted, free of charge, to any person obtaining a copy
  ~ of this software and associated documentation files (the "Software"), to deal
  ~ in the Software without restriction, including without limitation the rights
  ~ to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
  ~ copies of the Software, and to permit persons to whom the Software is
  ~ furnished to do so, subject to the following conditions:
  ~
  ~ The above copyright notice and this permission notice shall be included in all
  ~ copies or substantial portions of the Software.
  ~
  ~ THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
  ~ IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
  ~ FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
  ~ AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
  ~ LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
  ~ OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
  ~ SOFTWARE.
  ~
  -->

<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:width="447dp"
    android:height="62dp"
    android:viewportWidth="447"
    android:viewportHeight="62"
    tools:ignore="VectorRaster">
  <path
      android:pathData="M0.702,31.401L9.921,22.755C42.826,32.441 80.02,39.71 119.768,44.325L115.104,54.744C73.553,49.749 34.773,41.88 0.702,31.401"
      android:strokeWidth="1"
      android:fillColor="#4BD663"
      android:fillAlpha="0.4"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M11.758,20.587L21.05,11.872C52.579,20.632 87.929,27.188 125.583,31.338L120.912,41.772C81.444,37.254 44.492,30.115 11.758,20.587"
      android:strokeWidth="1"
      android:fillColor="#FFC700"
      android:fillAlpha="0.4"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
</vector>
