<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <item android:state_enabled="true" android:state_pressed="true">
        <shape>
            <corners android:radius="4dp" />
            <stroke android:width="1.5px" android:color="@color/uxsdk_white" />
            <solid android:color="#4C000000" />
        </shape>
    </item>

    <item android:state_enabled="false" android:state_pressed="true">
        <shape>
            <corners android:radius="4dp" />
            <stroke android:width="1.5px" android:color="@color/uxsdk_white" />
            <solid android:color="#4C000000" />
        </shape>
    </item>

    <item android:state_enabled="true" android:state_pressed="false">
        <shape>
            <corners android:radius="4dp" />
            <stroke android:width="1.5px" android:color="@color/uxsdk_white" />
            <solid android:color="@android:color/transparent" />
        </shape>
    </item>

    <item android:state_enabled="false" android:state_pressed="false">
        <shape>
            <corners android:radius="4dp" />
            <stroke android:width="1.5px" android:color="@color/uxsdk_white_10_percent" />
            <solid android:color="@color/uxsdk_black_80_percent" />
        </shape>
    </item>
</selector>