<!--
  ~ Copyright (c) 2018-2020 DJI
  ~
  ~ Permission is hereby granted, free of charge, to any person obtaining a copy
  ~ of this software and associated documentation files (the "Software"), to deal
  ~ in the Software without restriction, including without limitation the rights
  ~ to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
  ~ copies of the Software, and to permit persons to whom the Software is
  ~ furnished to do so, subject to the following conditions:
  ~
  ~ The above copyright notice and this permission notice shall be included in all
  ~ copies or substantial portions of the Software.
  ~
  ~ THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
  ~ IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
  ~ FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
  ~ AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
  ~ LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
  ~ OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
  ~ SOFTWARE.
  ~
  -->

<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:width="447dp"
    android:height="62dp"
    android:viewportWidth="447"
    android:viewportHeight="62"
    tools:ignore="VectorRaster">
  <path
      android:pathData="M319.137,45.212L323.816,55.663C292.409,59.131 259.515,60.972 225.927,61.059L225.927,50.166C257.89,50.083 289.194,48.394 319.137,45.212"
      android:strokeWidth="1"
      android:fillColor="#4BD663"
      android:fillAlpha="0.4"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M225.927,36.543C255.853,36.449 285.16,34.93 313.258,32.08L317.946,42.552C288.381,45.678 257.48,47.343 225.927,47.441L225.927,36.543Z"
      android:strokeWidth="1"
      android:fillColor="#FFC700"
      android:fillAlpha="0.4"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
</vector>
