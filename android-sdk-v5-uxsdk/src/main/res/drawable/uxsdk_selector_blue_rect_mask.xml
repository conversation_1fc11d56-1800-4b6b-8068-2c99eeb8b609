<?xml version="1.0" encoding="utf-8"?>
<selector
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:enterFadeDuration="@android:integer/config_shortAnimTime"
    android:exitFadeDuration="@android:integer/config_mediumAnimTime">

    <item android:state_pressed="true">
        <shape android:shape="rectangle">
            <solid android:color="@color/uxsdk_dic_color_c10_sea_blue" />
            <corners android:radius="10dp"/>
        </shape>
    </item>

    <item>
        <shape android:shape="rectangle">
            <solid android:color="@color/uxsdk_dic_color_c0_transparent" />
            <corners android:radius="10dp"/>
        </shape>
    </item>
</selector>