<!--
  ~ Copyright (c) 2018-2020 DJI
  ~
  ~ Permission is hereby granted, free of charge, to any person obtaining a copy
  ~ of this software and associated documentation files (the "Software"), to deal
  ~ in the Software without restriction, including without limitation the rights
  ~ to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
  ~ copies of the Software, and to permit persons to whom the Software is
  ~ furnished to do so, subject to the following conditions:
  ~
  ~ The above copyright notice and this permission notice shall be included in all
  ~ copies or substantial portions of the Software.
  ~
  ~ THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
  ~ IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
  ~ FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
  ~ AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
  ~ LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
  ~ OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
  ~ SOFTWARE.
  ~
  -->

<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:width="32dp"
    android:height="32dp"
    android:viewportWidth="32"
    android:viewportHeight="32">
  <path
      android:pathData="M16,1C20.1426,1 23.8931,2.6793 26.6076,5.3944L25.1934,6.8086C22.8408,4.4555 19.5904,3 16,3C12.4101,3 9.1601,4.4551 6.8076,6.8076L5.3934,5.3934C8.1079,2.6789 11.8579,1 16,1Z"
      android:strokeWidth="1"
      android:fillColor="#0AEE8B"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M15.9966,6C18.7585,6 21.2589,7.1197 23.0687,8.9299L21.6545,10.3441C20.2067,8.8958 18.2062,8 15.9966,8C13.7875,8 11.7875,8.8954 10.3397,10.3431L8.9255,8.9289C10.7352,7.1193 13.2352,6 15.9966,6Z"
      android:strokeWidth="1"
      android:fillColor="#E70102"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M16,29C19.5899,29 22.8399,27.5449 25.1924,25.1924L26.6066,26.6066C23.8921,29.3211 20.1421,31 16,31C11.8579,31 8.1079,29.3211 5.3934,26.6066L6.8076,25.1924C9.1601,27.5449 12.4101,29 16,29Z"
      android:strokeWidth="1"
      android:fillColor="#E70102"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M21.6535,21.66L23.0677,23.0742C21.258,24.8839 18.758,26.0031 15.9966,26.0031C13.2357,26.0031 10.7361,24.8843 8.9265,23.0752L10.3407,21.661C11.7884,23.1081 13.788,24.0031 15.9966,24.0031C18.2057,24.0031 20.2057,23.1077 21.6535,21.66Z"
      android:strokeWidth="1"
      android:fillColor="#E70102"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M2.2896,9.9059L4.1173,10.7194C3.3991,12.3329 3,14.1199 3,16C3,17.8804 3.3992,19.6675 4.1176,21.2813L2.2898,22.0944C1.4608,20.2322 1,18.1699 1,16C1,13.8302 1.4607,11.768 2.2896,9.9059Z"
      android:strokeWidth="1"
      android:fillColor="#E70102"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M29.7108,9.9069C30.5395,11.7688 31,13.8306 31,16C31,18.1695 30.5394,20.2315 29.7107,22.0934L27.8824,21.2813C28.6008,19.6675 29,17.8804 29,16C29,14.1199 28.6009,12.3329 27.8827,10.7194Z"
      android:strokeWidth="1"
      android:fillColor="#E70102"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M16,16m-4,0a4,4 0,1 1,8 0a4,4 0,1 1,-8 0"
      android:strokeWidth="1"
      android:fillColor="#0AEE8B"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
</vector>
