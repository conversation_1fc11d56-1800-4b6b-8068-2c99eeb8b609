<?xml version="1.0" encoding="utf-8"?>
<ScrollView
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/scrollView"
    android:orientation="vertical"
    android:scrollbars="none"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:descendantFocusability="afterDescendants">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:descendantFocusability="afterDescendants">

     <dji.v5.ux.core.widget.hd.ChannelSelectWidget
         android:layout_width="match_parent"
         android:layout_height="wrap_content"/>

      <View
          android:layout_width="match_parent"
          android:layout_height="@dimen/uxsdk_divider_size_medium"
          android:layout_marginTop="@dimen/uxsdk_vertical_margin_minimal"
          android:background="@color/uxsdk_dic_color_c20_divider" />

    <dji.v5.ux.core.widget.hd.frequency.FreqView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"/>

     <dji.v5.ux.core.widget.hd.BandWidthWidget
         android:layout_width="match_parent"
         android:layout_height="wrap_content"/>
    <View
        android:layout_width="match_parent"
        android:layout_height="@dimen/uxsdk_divider_size_medium"
        android:layout_marginTop="@dimen/uxsdk_vertical_margin_minimal"
        android:background="@color/uxsdk_dic_color_c20_divider" />

     <dji.v5.ux.core.widget.hd.VideoRateTextWidget
         android:layout_width="match_parent"
         android:layout_height="wrap_content"/>
    </LinearLayout>
</ScrollView>