/*
 * Copyright (c) 2018-2020 DJI
 *
 * Permission is hereby granted, free of charge, to any person obtaining a copy
 * of this software and associated documentation files (the "Software"), to deal
 * in the Software without restriction, including without limitation the rights
 * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
 * copies of the Software, and to permit persons to whom the Software is
 * furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in all
 * copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
 * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
 * SOFTWARE.
 *
 */

package dji.v5.ux.core.panel.topbar

import android.annotation.SuppressLint
import android.content.Context
import android.util.AttributeSet
import androidx.core.content.res.use
import dji.v5.utils.common.LogUtils
import dji.v5.ux.R
import dji.v5.ux.core.base.WidgetSizeDescription
import dji.v5.ux.core.base.panel.BarPanelWidget
import dji.v5.ux.core.base.panel.PanelItem
import dji.v5.ux.core.base.panel.PanelWidgetConfiguration
import dji.v5.ux.core.extension.getDimension
import dji.v5.ux.core.extension.getIntegerAndUse
import dji.v5.ux.core.widget.airsense.AirSenseWidget
import dji.v5.ux.core.widget.battery.BatteryWidget
import dji.v5.ux.core.widget.connection.ConnectionWidget
import dji.v5.ux.core.widget.flightmode.FlightModeWidget
import dji.v5.ux.core.widget.gpssignal.GpsSignalWidget
import dji.v5.ux.core.widget.remotecontrollersignal.RemoteControllerSignalWidget
import dji.v5.ux.core.widget.simulator.SimulatorIndicatorWidget
import dji.v5.ux.core.widget.systemstatus.SystemStatusWidget
import dji.v5.ux.core.widget.videosignal.VideoSignalWidget
import dji.v5.ux.core.widget.perception.PerceptionStateWidget
import dji.v5.ux.warning.DeviceHealthAndStatusWidget
import dji.v5.ux.core.widget.setting.SettingWidget
import java.util.*

/**
 * 顶部栏小部件的容器。这个[BarPanelWidget]分为两部分。
 * 左侧列表包含：
 * - [SystemStatusWidget]
 * The right list contains
 * - [FlightModeWidget]
 * - [SimulatorIndicatorWidget]
 * - [AirSenseWidget]
 * - [GPSSignalWidget]
 * - [PerceptionStateWidget]
 * - [RemoteControllerSignalWidget]
 * - [VideoSignalWidget]
 * - [BatteryWidget]
 * - [ConnectionWidget]
 *
 * * 定制：
 * 使用属性“excludeItem”从列表中永久删除项目。这将防止
 * 某些项目在栏面板小部件的整个生命周期中被创建和显示。这里有
 *所有标志：system_status、flight_mode、simulator_indicator、air_sense、gps_signal、
 * 视觉、rc_signal、video_signal、电池、连接。
 *
 * 请注意，可以通过逻辑“或”同时使用多个标志
 * 他们。例如，要隐藏飞行模式和视觉，可以通过
 * 以下两步。
 * 在其布局文件中定义自定义 xmlns：
 * xmlns:app="http://schemas.android.com/apk/res-auto"
 * 然后，将以下属性添加到[TopBarPanelWidget]：
 * 应用程序：excludeItem =“飞行模式|视觉”。
 *
 * 此面板小部件还将属性传递给创建的每个子小部件。看到每个
 * 个人的小部件文档以获得更多自定义选项。
 */
open class TopBarPanelWidget @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0,
    barPanelWidgetOrientation: BarPanelWidgetOrientation = BarPanelWidgetOrientation.HORIZONTAL
) : BarPanelWidget<Any>(context, attrs, defStyleAttr, barPanelWidgetOrientation) {


    //区域小部件属性
    val deviceHealthAndStatusWidget: DeviceHealthAndStatusWidget?

    /**
     * [SystemStatusWidget] 的 Getter。从栏面板中排除时为空。
     */
    val systemStatusWidget: SystemStatusWidget?

    /**
     * [FlightModeWidget] 的 Getter。从栏面板中排除时为空。
     */
    val flightModeWidget: FlightModeWidget?

    /**
     * Getter for [SimulatorIndicatorWidget]. Null when excluded from the bar panel.
     */
    val simulatorIndicatorWidget: SimulatorIndicatorWidget?

    /**
     * Getter for [AirSenseWidget]. Null when excluded from the bar panel.
     */
    val airSenseWidget: AirSenseWidget?

    /**
     * Getter for [GPSSignalWidget]. Null when excluded from the bar panel.
     */
    val gpsSignalWidget: GpsSignalWidget?

    /**
     * Getter for [PerceptionStateWidget]. Null when excluded from the bar panel.
     */
    val visionWidget: PerceptionStateWidget?

    /**
     * Getter for [RemoteControllerSignalWidget]. Null when excluded from the bar panel.
     */
    val remoteControllerSignalWidget: RemoteControllerSignalWidget?

    /**
     * Getter for [VideoSignalWidget]. Null when excluded from the bar panel.
     */
    val videoSignalWidget: VideoSignalWidget?

    /**
     * Getter for [BatteryWidget]. Null when excluded from the bar panel.
     */
    val batteryWidget: BatteryWidget?

    /**
     * Getter for [SettingWidget]. Null when excluded from the bar panel.
     */
    val settingWidget: SettingWidget?

    /**
     * Getter for [ConnectionWidget]. Null when excluded from the bar panel.
     */
//    val connectionWidget: ConnectionWidget?
    //endregion

    //region Private properties
    private var excludedItemsValue = 0
    //endregion

    //region Lifecycle & Setup

    override fun initPanelWidget(context: Context, attrs: AttributeSet?, defStyleAttr: Int, widgetConfiguration: PanelWidgetConfiguration?) {
        // Nothing to do
    }

    init {
        val leftPanelItems = ArrayList<PanelItem>()
        if (!WidgetValue.SYSTEM_STATUS.isItemExcluded(excludedItemsValue)) {
            systemStatusWidget = SystemStatusWidget(context, attrs)
            leftPanelItems.add(PanelItem(systemStatusWidget, itemMarginTop = 0, itemMarginBottom = 0))
        } else {
            systemStatusWidget = null
        }
        addLeftWidgets(leftPanelItems.toTypedArray())

        val rightPanelItems = ArrayList<PanelItem>()
        if (!WidgetValue.DEVICE_HEALTH.isItemExcluded(excludedItemsValue)) {
            deviceHealthAndStatusWidget = DeviceHealthAndStatusWidget(context, attrs)
            rightPanelItems.add(PanelItem(deviceHealthAndStatusWidget))
        } else {
            deviceHealthAndStatusWidget = null
        }
        if (!WidgetValue.GPS_SIGNAL.isItemExcluded(excludedItemsValue)) {
            gpsSignalWidget = GpsSignalWidget(context, attrs)
            rightPanelItems.add(PanelItem(gpsSignalWidget))
        } else {
            gpsSignalWidget = null
        }
        if (!WidgetValue.FLIGHT_MODE.isItemExcluded(excludedItemsValue)) {
            flightModeWidget = FlightModeWidget(context, attrs)
            rightPanelItems.add(PanelItem(flightModeWidget))
        } else {
            flightModeWidget = null
        }
        if (!WidgetValue.SIMULATOR_INDICATOR.isItemExcluded(excludedItemsValue)) {
            simulatorIndicatorWidget = SimulatorIndicatorWidget(context, attrs)
            rightPanelItems.add(PanelItem(simulatorIndicatorWidget))
        } else {
            simulatorIndicatorWidget = null
        }
        if (!WidgetValue.AIR_SENSE.isItemExcluded(excludedItemsValue)) {
            airSenseWidget = AirSenseWidget(context, attrs)
            rightPanelItems.add(PanelItem(airSenseWidget))
        } else {
            airSenseWidget = null
        }
        if (!WidgetValue.VISION.isItemExcluded(excludedItemsValue)) {
            visionWidget = PerceptionStateWidget(context, attrs)
            rightPanelItems.add(PanelItem(visionWidget))
        } else {
            visionWidget = null
        }
        if (!WidgetValue.RC_SIGNAL.isItemExcluded(excludedItemsValue)) {
            remoteControllerSignalWidget = RemoteControllerSignalWidget(context, attrs)
            rightPanelItems.add(PanelItem(remoteControllerSignalWidget))
        } else {
            remoteControllerSignalWidget = null
        }
        if (!WidgetValue.VIDEO_SIGNAL.isItemExcluded(excludedItemsValue)) {
            videoSignalWidget = VideoSignalWidget(context, attrs)
            rightPanelItems.add(PanelItem(videoSignalWidget))
        } else {
            videoSignalWidget = null
        }
        if (!WidgetValue.BATTERY.isItemExcluded(excludedItemsValue)) {
            batteryWidget = BatteryWidget(context, attrs)
            rightPanelItems.add(PanelItem(batteryWidget))
        } else {
            batteryWidget = null
        }

        if (!WidgetValue.SETTING.isItemExcluded(excludedItemsValue)) {
            settingWidget = SettingWidget(context, attrs)
            rightPanelItems.add(PanelItem(settingWidget))
        } else {
            settingWidget = null
        }
        addRightWidgets(rightPanelItems.toTypedArray())
    }

    @SuppressLint("Recycle")
    override fun initAttributes(attrs: AttributeSet) {
        guidelinePercent = 0.25f
        itemsMarginTop = getDimension(R.dimen.uxsdk_bar_panel_margin).toInt()
        itemsMarginBottom = getDimension(R.dimen.uxsdk_bar_panel_margin).toInt()

        context.obtainStyledAttributes(attrs, R.styleable.TopBarPanelWidget).use { typedArray ->
            typedArray.getIntegerAndUse(R.styleable.TopBarPanelWidget_uxsdk_excludeTopBarItem) {
                excludedItemsValue = it
            }
        }

        super.initAttributes(attrs)
    }

    override fun reactToModelChanges() {
        // Nothing to do
    }
    //endregion

    //region Customizations
    override fun getIdealDimensionRatioString(): String? = null

    override val widgetSizeDescription: WidgetSizeDescription =
        WidgetSizeDescription(
            WidgetSizeDescription.SizeType.OTHER,
            widthDimension = WidgetSizeDescription.Dimension.EXPAND,
            heightDimension = WidgetSizeDescription.Dimension.EXPAND
        )
    //endregion

    private enum class WidgetValue(val value: Int) {
        SYSTEM_STATUS(1),
        DEVICE_HEALTH(2),
        FLIGHT_MODE(3),
        SIMULATOR_INDICATOR(4),
        AIR_SENSE(8),
        GPS_SIGNAL(16),
        VISION(32),
        RC_SIGNAL(64),
        VIDEO_SIGNAL(128),
        BATTERY(256),
        SETTING(512),
        CONNECTION(1024);

        fun isItemExcluded(excludeItems: Int): Boolean {
            return excludeItems and this.value == this.value
        }
    }
}