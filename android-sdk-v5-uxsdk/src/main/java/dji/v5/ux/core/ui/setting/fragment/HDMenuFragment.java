package dji.v5.ux.core.ui.setting.fragment;

import android.os.Bundle;
import android.view.View;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import java.util.List;

import dji.sdk.keyvalue.value.airlink.WlmDongleInfo;
import dji.v5.common.callback.CommonCallbacks;
import dji.v5.common.error.IDJIError;
import dji.v5.manager.aircraft.lte.LTEDongleInfoListener;
import dji.v5.manager.aircraft.lte.LTELinkType;
import dji.v5.manager.aircraft.lte.LTEManager;
import dji.v5.utils.common.ContextUtil;
import dji.v5.utils.common.StringUtils;
import dji.v5.ux.R;
import dji.v5.ux.core.base.SwitcherCell;
import dji.v5.ux.core.ui.setting.ui.MenuFragment;

/**
 * Description :
 *
 * @author: Byte.Cai
 * date : 2022/11/21
 * <p>
 * Copyright (c) 2022, DJI All Rights Reserved.
 */
public class HDMenuFragment extends MenuFragment {
    private SwitcherCell enhancedTransmission;

    @Override
    protected String getPreferencesTitle() {
        return StringUtils.getResStr(ContextUtil.getContext(), R.string.uxsdk_setting_menu_title_hd);
    }

    @Override
    protected int getLayoutId() {
        return R.layout.uxsdk_setting_menu_hd_layout;
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        enhancedTransmission = view.findViewById(R.id.setting_menu_hd_enhanced_transmission_switch);
        view.findViewById(R.id.tc_setting_menu_sdr_info_text).setOnClickListener(view1 -> {
            ChannelSelectFragment fragment = new ChannelSelectFragment();
            addFragment(getFragmentManager(), fragment, true);
        });

        LTEManager.getInstance().getLTEEnhancedTransmissionType(new CommonCallbacks.CompletionCallbackWithParam<LTELinkType>() {
            @Override
            public void onSuccess(LTELinkType lteLinkType) {
                enhancedTransmission.setChecked(lteLinkType == LTELinkType.OCU_SYNC_LTE);

                enhancedTransmission.setOnCheckedChangedListener(new SwitcherCell.OnCheckedChangedListener() {
                    @Override
                    public void onCheckedChanged(SwitcherCell cell, boolean isChecked) {
                        LTELinkType lteLinkType = LTELinkType.OCU_SYNC_LTE;
                        if (!isChecked) {
                            lteLinkType = LTELinkType.OCU_SYNC;
                        }

                        LTEManager.getInstance().setLTEEnhancedTransmissionType(lteLinkType, new CommonCallbacks.CompletionCallback() {
                            @Override
                            public void onSuccess() {
                                Toast.makeText(getContext(), "设置成功", Toast.LENGTH_SHORT).show();
                            }

                            @Override
                            public void onFailure(@NonNull IDJIError idjiError) {
                                Toast.makeText(getContext(), "设置失败:"+idjiError.description(), Toast.LENGTH_SHORT).show();
                            }
                        });
                    }
                });

            }

            @Override
            public void onFailure(@NonNull IDJIError idjiError) {
                Toast.makeText(getContext(), "获取增强图传类型失败"+idjiError.description(), Toast.LENGTH_SHORT).show();
            }
        });

    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
    }
}
