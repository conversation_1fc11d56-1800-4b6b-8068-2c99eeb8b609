package dji.v5.ux.mapkit.core.maps;

import android.os.Bundle;



public class DJIEmptyMapView implements DJIMapViewInternal {
    @Override
    public void onCreate(Bundle saveInstanceState) {
        //子类实现
    }

    @Override
    public void onStart() {
        //子类实现
    }

    @Override
    public void onResume() {
        //子类实现
    }

    @Override
    public void onPause() {
        //子类实现
    }

    @Override
    public void onStop() {
        //子类实现
    }

    @Override
    public void onDestroy() {
        //子类实现
    }

    @Override
    public void onSaveInstanceState(Bundle outState) {
        //子类实现
    }

    @Override
    public void onLowMemory() {
        //子类实现
    }

    @Override
    public void getDJIMapAsync(DJIMapView.OnDJIMapReadyCallback callback) {
        //子类实现
    }
}
